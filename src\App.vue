<template>
  <t-config-provider :global-config="globalConfig">
    <router-view :class="[mode]" />
  </t-config-provider>
</template>
<script setup lang="ts">
  import { computed } from 'vue'
  import { useSettingStore } from '@/store'
  import enUS from 'tdesign-vue-next/es/locale/en_US'
  import zhCN from 'tdesign-vue-next/es/locale/zh_CN'
  import ruRU from 'tdesign-vue-next/es/locale/ru_RU'
  import viVN from '@/i18n/lang/TDesign/vi_VN'
  const { locale } = useI18n({ useScope: 'global' })
  const store = useSettingStore()

  const mode = computed(() => {
    return store.displayMode
  })

  const message = {
    zh: zhCN,
    en: enUS,
    ru: ruRU,
    vi: viVN,
  }
  const globalConfig = computed(() => message[locale.value])
</script>
<style lang="less" scoped>
  #nprogress .bar {
    background: var(--td-brand-color) !important;
  }
</style>

import type { EChartsType } from 'echarts'

export interface UnExchangeType {
  quantity: string
  unExchange: string
  product: string
}

export type OrderState = {
  unexchangedEchart?: EChartsType | null
}

export const DefaultUnExchangeResult = [
  {
    quantity: '0',
    unExchange: '0',
    product: '湿米糠',
  },
  {
    quantity: '0',
    unExchange: '0',
    product: '干米糠',
  },
  {
    quantity: '0',
    unExchange: '0',
    product: '混合米糠',
  },
]

export const unExchangeEchartConfig = (unexchangeds: UnExchangeType[]) => {
  return {
    grid: {
      top: 40,
      bottom: 70,
      left: 50,
    },
    dataZoom: [
      {
        type: 'inside',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: Math.floor((4 / unexchangeds.length) * 100),
        bottom: '10',
        zoomLock: true,
      },
    ],
    tooltip: {
      hideDelay: 100,
      trigger: 'axis',
      axisPointer: { type: 'cross', crossStyle: { color: '#999' } },
    },
    legend: { data: ['交易总量', '待交货量'], bottom: 20 },
    xAxis: [
      {
        type: 'category',
        data: unexchangeds.map((i) => i.product),
        axisPointer: { type: 'shadow' },
      },
    ],
    yAxis: [
      {
        type: 'value',
        minInterval: 1,
        name: '吨',
        axisLabel: {
          formatter: '{value}',
          width: 47,
          margin: 3,
          overflow: 'breakAll',
        },
        nameTextStyle: {
          color: '#999',
          align: 'left',
        },
        axisLine: {
          lineStyle: {
            width: 1,
            color: '#999',
            type: 'dotted',
          },
        },
      },
    ],
    series: [
      {
        name: '交易总量',
        type: 'bar',
        data: unexchangeds.map((i) => i.quantity),
        barWidth: '25',
      },
      {
        name: '待交货量',
        type: 'bar',
        data: unexchangeds.map((i) => i.unExchange),
        barWidth: '25',
      },
    ],
  }
}

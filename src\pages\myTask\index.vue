<script setup lang="ts">
  import useMyTask from './hooks'
  const { value, tabList, taskList, columns, pagination, loading } = useMyTask()
</script>
<template>
  <div class="container">
    <t-tabs v-model="value">
      <t-tab-panel v-for="t in tabList" :key="t.value" :value="t.value" :label="t.title">
        <t-card :bordered="false">
          <t-table
            row-key="index"
            maxHeight="500"
            :data="taskList"
            :columns="columns"
            size="small"
            :loading="loading"
            :pagination="pagination"
            lazy-load
          >
          </t-table>
        </t-card>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>
<style scoped></style>

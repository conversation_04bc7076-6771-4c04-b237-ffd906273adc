<script lang="ts" setup>
  import { ref } from 'vue'
  import { OrderItemType } from '../utils/moneyConfig'
  import { saleContractOrderApi, purchContractOrderApi } from '@/api/contract'

  const router = useRouter()

  interface Props {
    title: string
    color?: string
    orders?: OrderItemType[]
  }

  withDefaults(defineProps<Props>(), {
    title: 'Thanh toán',
    color: '#666',
    orders: () => [],
  })

  const showOrders = ref<boolean>(true)

  /* loading */
  const showLoading = ref<boolean>(false)
  const jumpToOrderDetail = async (itemInfo: any) => {
    if (!itemInfo?.orderno) return
    const contractType = itemInfo.orderno.split('-')[0]
    showLoading.value = true
    if (contractType == 'SO') {
      const res = await saleContractOrderApi(itemInfo.orderid)
      console.log(res.data)

      router.push({ path: '/contract/sales-details', state: { info: JSON.stringify(res.data[0]) } })
    } else {
      const res = await purchContractOrderApi(itemInfo.orderid)
      router.push({
        path: '/contract/purchase-details',
        state: { info: JSON.stringify(res.data[0]) },
      })
    }
    showLoading.value = false
  }
</script>
<template>
  <div class="order-container">
    <div @click.prevent="showOrders = !showOrders" class="order-header">
      <div>{{ title }} <t-icon :name="showOrders ? 'chevron-down' : 'chevron-up'" /></div>
    </div>
    <t-loading size="small" :loading="showLoading">
      <div v-if="showOrders && orders.length > 0" class="order-list">
        <div class="order-item" v-for="order in orders" :key="order.orderid" @click="jumpToOrderDetail(order)">
          {{ order.orderno }}
        </div>
      </div>
    </t-loading>
  </div>
</template>
<style lang="less" scoped>
  .order-container {
    box-sizing: border-box;
    text-align: left;
    padding: 0 10px 20px;
    width: 100vw;

    .order-header {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .order-list {
      box-sizing: border-box;
      margin: 12px 0 0 12px;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;

      .order-item {
        cursor: pointer;
        box-sizing: border-box;
        color: v-bind(color);
        height: 1.8em;
      }
    }
  }
</style>

import { round } from './digit'

/**
 * @description 数字格式化
 * @param {number|string} number 要格式化的数字
 * @param {number} decimals 保留几位小数
 * @param {string} decimalPoint 小数点符号
 * @param {string} thousandsSeparator 千分位符号
 * @returns {string} 格式化后的数字
 */
export function priceFormat(number: number | string, decimals = 0, decimalPoint = '.', thousandsSeparator = ',') {
  number = `${number}`.replace(/[^0-9+-Ee.]/g, '')
  const n = !isFinite(+number) ? 0 : +number
  const prec = !isFinite(+decimals) ? 0 : Math.abs(decimals)
  const sep = typeof thousandsSeparator === 'undefined' ? ',' : thousandsSeparator
  const dec = typeof decimalPoint === 'undefined' ? '.' : decimalPoint
  let s = (prec ? round(n, prec) + '' : `${Math.round(n)}`).split('.')
  const re = /(-?\d+)(\d{3})/
  while (re.test(s[0])) {
    s[0] = s[0].replace(re, `$1${sep}$2`)
  }

  if ((s[1] || '').length < prec) {
    s[1] = s[1] || ''
    s[1] += new Array(prec - s[1].length + 1).join('0')
  }
  return s.join(dec)
}

export const deepClone = function (origin: any, target: any = {}) {
  for (const prop in target) {
    if (target[prop] !== null && typeof target[prop] === 'object') {
      origin[prop] = Object.prototype.toString.call(target[prop]) === '[object Array]' ? [] : {}
      deepClone(origin[prop], target[prop])
    } else {
      origin[prop] = target[prop]
    }
  }
}

export const isKg = (unit: string) => (unit || 'kg').toLocaleLowerCase() === 'kg'
export const calcDiv1000 = (num: string | number) => priceFormat(Number(num) / 1000, 2)
export const calcMulti1000 = (num: string | number) => priceFormat(Number(num) * 1000, 2)

export const quantityUnitFormat = (unit: string) => (isKg(unit || 'KG') ? 't' : 'KG')
export const quantityFormat = (quantity: string | number, unit: string) =>
  isKg(unit) ? calcDiv1000(quantity) : quantity
export const amountFormat = (amount: string | number, unit: string) => (isKg(unit) ? calcMulti1000(amount) : amount)

export const parsePercentString = (val: string) => {
  if (!val) return 0
  const needDiv100 = val.includes('%')
  const num = Number(val.replace('%', '')) || 0
  return needDiv100 ? num / 100 : num
}

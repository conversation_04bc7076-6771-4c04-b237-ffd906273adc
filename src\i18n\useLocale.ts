import { useLocalStorage } from '@vueuse/core'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { vueI18n, localeKey } from '@/i18n'

export function useLocale() {
  const { locale } = useI18n({ useScope: 'global' })
  const storage = useLocalStorage(localeKey, 'zh')
  locale.value = storage.value
  function changeLocale(lang: string) {
    locale.value = lang
    storage.value = lang
  }

  const getComponentsLocale = computed(() => {
    return vueI18n.global.getLocaleMessage(locale.value)
  })

  return {
    changeLocale,
    getComponentsLocale,
    locale,
  }
}

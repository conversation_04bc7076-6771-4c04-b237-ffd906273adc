<template>
  <l-header
    v-if="settingStore.showHeader"
    :show-logo="settingStore.showHeaderLogo"
    :theme="settingStore.displayMode"
    :layout="settingStore.layout"
    :is-fixed="settingStore.isHeaderFixed"
    :menu="headerMenu"
    :is-compact="settingStore.isSidebarCompact"
  />
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useUserStore, useSettingStore } from '@/store'
  import LHeader from './Header.vue'

  const userStore = useUserStore()
  const settingStore = useSettingStore()
  const { routers: menuRouters } = storeToRefs(userStore)
  const headerMenu: any = computed(() => {
    if (settingStore.layout === 'mix') {
      if (settingStore.splitMenu) {
        return menuRouters.value.map((menu) => ({
          ...menu,
          children: [],
        }))
      }
      return []
    }
    return menuRouters.value
  })
</script>

import { get, post, remove } from '@/utils/request'

export const loadNoticeList = () => {
  return get<any[]>('/scm/rfqNotice/list', { useFor: 'vn-raw-procure-rfq' })
}

export const loadNoticeById = (id: any) => {
  return get<any>(`/scm/rfqNotice/${id}`)
}

export const saveNotice = (data: any) => {
  return post<any>('/scm/rfqNotice', data)
}

export const removeNoticeById = (id: any) => {
  return remove<any>(`/scm/rfqNotice/${id}`)
}

// 通用声明

// Vue
declare module '*.vue' {
  import { DefineComponent } from 'vue'

  const component: DefineComponent<{}, {}, any>
  export default component
}

declare type ClassName = { [className: string]: any } | ClassName[] | string

declare module '*.svg' {
  const CONTENT: string
  export default CONTENT
}

declare type Recordable<T = any> = Record<string, T>

type Nullable<T> = T | null

import { FormRules } from 'element-plus'

declare global {
  interface UploadOption {
    /** 设置上传的请求头部 */
    headers: { [key: string]: any }

    /** 上传的地址 */
    url: string
  }

  /**
   * 导入属性
   */
  interface ImportOption extends UploadOption {
    /** 是否显示弹出层 */
    open: boolean
    /** 弹出层标题 */
    title: string
    /** 是否禁用上传 */
    isUploading: boolean

    /** 其他参数 */
    [key: string]: any
  }

  /**
   * 字典数据  数据配置
   */
  interface DictDataOption {
    id?: number
    label: string
    value: string
    elTagType?: ElTagType
    elTagClass?: string
    remark?: string
  }

  interface BaseEntity {
    createBy?: any
    createDept?: any
    createTime?: string
    updateBy?: any
    updateTime?: any
  }

  /**
   * 分页数据
   * T : 表单数据
   * D : 查询参数
   */
  interface PageData<T, D> {
    form: T
    queryParams: D
    rules: FormRules
  }

  /**
   * 分页查询参数
   */
  interface PageQuery {
    pageNum: number
    pageSize: number
  }

  class AMap {
    static [key: string]: any
  }

  type Nullable<T> = T | null
}
export {}

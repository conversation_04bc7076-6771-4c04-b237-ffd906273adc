import ChartLineData from '../config/chart-line-data.json'
import ChartPieData from '../config/chart-pie-data.json'
import ChartBarData from '../config/chart-bar-data.json'
import unselectLine from '../config/chart-unselect-line-data.json'
import { reactive } from 'vue'
import { purchDetailAll } from '@/api/data/vendor'
import { useUserStore } from '@/store'
import { useNsListLabelRaw } from '@/use/dict'
import { useYearDataStore } from './dataYearStore'
import { divideScale } from '@/utils/digit'

export const statisticalService = () => {
  const globalStore = useUserStore()
  const { slectVal } = storeToRefs(useYearDataStore())

  const { locale } = useI18n()
  /* 货币类型 */
  const currencyType = ref<any>({})
  const currencyLabelRaw = async () => {
    const { currency } = await useNsListLabelRaw('currency')
    currencyType.value = currency
  }

  const StatisticalInfo = reactive({
    allNum: '',
    successNum: 0,
    percent: '',
    quotedNum: 0,
    orderNum: '',
    totalAmount: ['0'] as string[],
  })

  const fetchAllData = async (year: string) => {
    if (!currencyType.value) return
    if (!globalStore.vendorIds?.length) return
    try {
      const res = await purchDetailAll<any[]>('PurchStats', {
        vendorIdList: globalStore.vendorIds,
        year,
      })

      const result = (res.data || []).reduce(
        (pre, cur) => {
          return {
            count: (parseInt(pre.count) + parseInt(cur.count)).toString(),
            quantity: (parseInt(pre.quantity) + parseInt(cur.quantity)).toString(),
            amount: {
              ...pre.amount,
              [cur.currency_id]: (Number(pre.amount[cur.currency_id] || '0') + Number(cur.amount)).toString(),
            },
          }
        },
        {
          count: '0',
          quantity: '0',
          amount: {} as Record<number, string>,
        },
      )
      if (result) {
        StatisticalInfo.orderNum = new Intl.NumberFormat().format(result.count)
        StatisticalInfo.allNum = new Intl.NumberFormat().format(divideScale(result.quantity, 1000, 3))
        StatisticalInfo.totalAmount = Object.entries(result.amount).map((e) => {
          return new Intl.NumberFormat(locale.value, {
            style: 'currency',
            currencyDisplay: 'code',
            currency: currencyType.value[e[0]],
            notation: 'compact',
            compactDisplay: 'long',
            minimumFractionDigits: 3,
            roundingMode: 'halfFloor',
          }).format(Number(e[1] || '0'))
        })
      } else {
        StatisticalInfo.orderNum = '0'
        StatisticalInfo.allNum = '0'
        StatisticalInfo.totalAmount = ['0']
      }
    } catch (error) {
      console.log('error:', error)
    }
    return {
      unselectLine,
      line: ChartLineData,
      pie: ChartPieData,
      bar: ChartBarData,
    }
  }

  onMounted(async () => {
    await currencyLabelRaw()
    if (slectVal.value) {
      await nextTick()
      await fetchAllData(slectVal.value)
    }
  })

  // 监听年份变化
  watch(
    () => slectVal.value,
    async () => {
      await nextTick()
      await fetchAllData(slectVal.value)
    },
  )
  // watchEffect(async () => {
  //   if (selectedVal.value) {
  //     await nextTick()
  //     await fetchAllData(selectedVal.value)
  //   }
  // })

  return {
    StatisticalInfo,
    fetchAllData,
  }
}

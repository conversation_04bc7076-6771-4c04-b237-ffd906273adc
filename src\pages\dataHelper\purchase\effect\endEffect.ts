import { computed, ref } from 'vue'
import dayjs from 'dayjs'
import { useYearDataStore } from '../hooks/dataYearStore'

const endEffect = () => {
  const { slectVal, yearEnd } = storeToRefs(useYearDataStore())
  const maxDate = dayjs().format('YYYY-MM-DD')
  const currentDate = dayjs().format('YYYY-MM-DD')
  const endTime = ref<any>(yearEnd.value)
  const showEndTime = ref<boolean>(false)
  const endTimeText = computed(() => dayjs(endTime.value).format('YYYY-MM-DD'))
  const endTimeValue = computed(() => dayjs(endTime.value).format('DD-MM-YYYY'))

  watch(
    () => slectVal.value,
    () => {
      endTime.value = yearEnd.value
    },
  )

  return {
    maxDate,
    endTime,
    showEndTime,
    endTimeValue,
    endTimeText,
    currenEndtDate: currentDate,
  }
}

export default endEffect

import dayjs from 'dayjs'
import { useUserStore } from '@/store'
import { useNsListLabelRaw } from '@/use/dict'
import { reportSumInfo } from '@/api/data/customer'
import { useYearDataStore } from '../../purchase/hooks/dataYearStore'
import TransactionQuery from '../components/transaction-query.vue'
import FundQuery from '../components/fund-query.vue'
import OrderQuery from '../components/order-query.vue'
import QuoteQuery from '../components/quote-query.vue'

export default () => {
  const { t, locale } = useI18n()
  const $t = t
  const globalStore = useUserStore()
  const { slectVal, yearStart, yearEnd } = storeToRefs(useYearDataStore())
  const value = ref(0)
  const disableDate = ref<any>({
    before: dayjs().subtract(2, 'year').format('YYYY'),
    after: dayjs().format('YYYY'),
  })

  /* 货币类型 */
  const currencyType = ref<any>({})

  const sumInfo = ref<any>({
    quantity: 0,
    amount: [],
    count: 0,
  })
  const echartList = [
    {
      title: $t('交易查询'),
      cmp: TransactionQuery,
    },
    {
      title: $t('订单查询'),
      cmp: OrderQuery,
    },
    {
      title: $t('资金查询'),
      cmp: FundQuery,
    },
    {
      title: $t('报价查询'),
      cmp: QuoteQuery,
    },
  ]
  const currencyLabelRaw = async () => {
    const { currency } = await useNsListLabelRaw('currency')
    currencyType.value = currency
  }

  const loadSumInfo = async () => {
    await nextTick()
    if (!globalStore.customerIds?.length) return
    const res = await reportSumInfo({
      startDate: yearStart.value,
      endDate: yearEnd.value,
      customerIdList: globalStore.customerIds,
    })
    const sumData = (res.data || []).reduce(
      (acc: any, val: any) => {
        acc.quantity += Number(val.quantity)
        acc.amount.push({
          currency: val.currency,
          amount: new Intl.NumberFormat(locale.value, {
            style: 'currency',
            currencyDisplay: 'code',
            currency: currencyType.value[val.currency],
            notation: 'compact',
            compactDisplay: 'long',
            minimumFractionDigits: 3,
            roundingMode: 'halfFloor',
          }).format(Number(val.foreignamount) || '0'),
        })
        acc.count += val.order_count
        return acc
      },
      {
        quantity: 0,
        amount: [],
        count: 0,
      },
    )
    sumInfo.value = {
      ...sumData,
      quantity: new Intl.NumberFormat().format(sumData.quantity),
      count: new Intl.NumberFormat().format(sumData.count),
    }
  }

  watch(
    () => slectVal.value,
    () => loadSumInfo(),
  )

  onMounted(async () => {
    await currencyLabelRaw()
    loadSumInfo()
  })

  return {
    disableDate,
    slectVal,
    value,
    echartList,
    sumInfo,
    yearStart,
    yearEnd,
  }
}

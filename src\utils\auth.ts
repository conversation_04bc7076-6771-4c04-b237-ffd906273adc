/* eslint-disable no-return-assign */
import { useStorage } from '@vueuse/core'

const TokenKey = 'Mobile-Token'

const tokenStorage = useStorage<null | string>(TokenKey, null)

export const getToken = () => tokenStorage.value

export const setToken = (token: string) => (tokenStorage.value = token)

export const removeToken = () => (tokenStorage.value = null)

const RedirectKey = 'Login-Redirect'

const redirectStorage = useStorage<null | string>(RedirectKey, null)

export const setRedirect = (path: string) => (redirectStorage.value = path)
export const getRedirect = () => redirectStorage.value
export const removeRedirect = () => (redirectStorage.value = null)

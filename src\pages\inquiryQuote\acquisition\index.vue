<script setup lang="ts">
  import Dialog from '@/components/dialog/index.vue'
  import useAcquisition from './hooks'
  import { AddIcon } from 'tdesign-icons-vue-next'
  import Calendar from './tabs/calendar.vue'

  const { t } = useI18n()
  const $t = t
  const {
    value,
    visible,
    productName,
    deductionStandard,
    formData,
    onConfirm,
    disableDate,
    FORM_RULES,
    purchaseVisible,
    productList,
    quotationLoading,
    purchase,
    purchaseForm,
    canEdit,
    submitProcurementReq,
    loading,
    quotationForm,
    pagination,
    quotationList,
    columns,
    resetForm,
    onSubmit,
    openRecordHistory,
    historyData,
    historyLoading,
    historyDialog,
    historyTableCols,

    noticeList,
    newNoticeDialog,
    handleOpenNewNotice,
    noticeForm,
    closeNoticeForm,
    submitNotice,
    removeNotice,
    toNoticeDetail,
  } = useAcquisition()
</script>
<template>
  <div class="container">
    <t-tabs v-model="value">
      <t-tab-panel :value="1" :label="$t('报价记录')" :destroy-on-hide="false">
        <t-card :bordered="false">
          <t-form layout="inline" label-width="auto" :data="quotationForm" @submit="onSubmit" @reset="resetForm">
            <t-form-item :label="$t('产品名称')">
              <t-select
                size="small"
                :keys="{ label: 'itemName', value: 'itemId' }"
                v-model="quotationForm.itemId"
                :options="productList"
                :placeholder="$t('请选择产品')"
              />
            </t-form-item>
            <t-form-item>
              <t-button size="small" theme="primary" type="submit">{{ $t('查询') }}</t-button>
              <t-button size="small" theme="default" variant="base" type="reset">{{ $t('重置') }}</t-button>
            </t-form-item>
          </t-form>
          <t-table
            style="margin-top: 20px"
            row-key="index"
            size="small"
            maxHeight="500"
            :data="quotationList"
            :columns="columns"
            :pagination="pagination"
            :loading="quotationLoading"
          />
        </t-card>
      </t-tab-panel>
      <t-tab-panel :value="2" :label="$t('预约日历')" destroy-on-hide>
        <Calendar v-if="value === 2" />
      </t-tab-panel>
      <t-tab-panel :value="3" :label="$t('收购计划')" destroy-on-hide>
        <t-collapse :default-value="[0]">
          <t-collapse-panel v-for="p in productList" :key="p.itemId" destroy-on-collapse :header="p.itemName">
            <template #headerRightContent>
              <t-button size="small" theme="default" variant="outline" @click="openRecordHistory(p)">{{
                $t('历史')
              }}</t-button>
            </template>
            <t-list split>
              <t-list-item v-for="c in p.configs" :key="c.id">
                {{ c.itemName }}({{ c.configName }})
                <template #action>
                  <t-button size="small" theme="primary" variant="outline" @click="purchase(c)">{{
                    $t('编辑')
                  }}</t-button>
                </template>
              </t-list-item>
            </t-list>
          </t-collapse-panel>
        </t-collapse>
      </t-tab-panel>
      <t-tab-panel :value="4" :label="$t('收购公告')" destroy-on-hide>
        <t-card :bordered="false">
          <t-button size="small" theme="primary" @click="handleOpenNewNotice"
            ><template #icon><add-icon /></template><span>{{ $t('新增公告') }}</span></t-button
          >
          <t-list split>
            <t-list-item v-for="n in noticeList" :key="n.id">
              <span>{{ n.content }}</span>
              <template #action>
                <t-button size="small" theme="primary" @click="toNoticeDetail(n)">{{ $t('编辑') }}</t-button>
                <t-button size="small" theme="danger" @click="removeNotice(n.id)">{{ $t('删除') }}</t-button>
              </template>
            </t-list-item>
          </t-list>
        </t-card>
      </t-tab-panel>
      <t-tab-panel :value="5" :label="$t('推荐人')" destroy-on-hide> </t-tab-panel>
    </t-tabs>
    <!-- 报价弹窗 -->
    <Dialog v-model="visible" top="4%" width="400px" :footer="false">
      <template #header>{{ $t('报价详情') }}</template>
      <template #body>
        <t-list split>
          <t-list-item>
            <t-list-item-meta :title="productName">
              <template #description>
                <div v-for="i in deductionStandard">{{ i.label }}：{{ i.value }}</div>
              </template>
            </t-list-item-meta>
          </t-list-item>
        </t-list>
        <t-form style="margin-top: 20px" label-width="auto" :data="formData" :rules="FORM_RULES" @submit="onConfirm">
          <t-form-item :label="$t('供应量') + `（${$t('吨')}）`" name="custrecord_it_quote_supply">
            <t-input
              size="small"
              :placeholder="$t('请输入供应量')"
              v-model="formData.custrecord_it_quote_supply"
              clearable
            />
          </t-form-item>
          <t-form-item :label="$t('收购价')" name="custrecord_it_quote_price">
            <t-input
              size="small"
              :placeholder="$t('请输入收购价')"
              readonly
              v-model="formData.custrecord_it_quote_price"
            />
          </t-form-item>
          <t-form-item :label="$t('交货地点')" name="custrecord_it_quote_location">
            <t-textarea
              size="small"
              :placeholder="$t('请输入交货地点')"
              v-model="formData.custrecord_it_quote_location"
            />
          </t-form-item>
          <t-form-item :label="$t('最晚交货日期')" name="custrecord_it_quote_lasttrandate">
            <t-date-picker
              size="small"
              style="width: 100%"
              :placeholder="$t('请选择最晚交货日期')"
              clearable
              :disable-date="disableDate"
              v-model="formData.custrecord_it_quote_lasttrandate"
            />
          </t-form-item>
          <t-form-item>
            <t-button size="small" theme="primary" type="submit" :loading="loading">{{ $t('确认') }}</t-button>
            <t-button size="small" theme="default" variant="base" type="reset" @click="visible = false">{{
              $t('取消')
            }}</t-button>
          </t-form-item>
        </t-form>
      </template>
    </Dialog>
    <!-- 采购弹窗 -->
    <Dialog v-model="purchaseVisible" top="3%" width="460px" :footer="false">
      <template #header>{{ $t('调整采购需求') }}</template>
      <template #body>
        <t-form ref="form" label-width="auto" :data="purchaseForm" @submit="submitProcurementReq">
          <t-form-item :label="$t('货品')">
            <t-input size="small" v-model="purchaseForm.itemName" readonly :placeholder="$t('请输入内容')" />
          </t-form-item>

          <t-form-item :label="$t('建议价')">
            <t-input
              size="small"
              type="number"
              v-model="purchaseForm.standardPrice"
              :placeholder="$t('请输入建议价')"
              :readonly="!canEdit"
            />
          </t-form-item>
          <t-form-item :label="$t('需求量') + `（${$t('吨')}）`">
            <t-input
              size="small"
              type="number"
              v-model="purchaseForm.demandQuantity"
              :placeholder="$t('请输入需求量')"
              :readonly="!canEdit"
            />
          </t-form-item>
          <t-typography-title level="h5">{{ $t('质量标准') }}</t-typography-title>
          <t-form-item :label="$t('杂质%')">
            <t-input
              size="small"
              v-model="purchaseForm.metrics1"
              :placeholder="$t('请输入杂质')"
              :readonly="!canEdit"
            />
          </t-form-item>
          <t-form-item :label="$t('霉变粒%')">
            <t-input
              size="small"
              v-model="purchaseForm.metrics2"
              :placeholder="$t('请输入霉变粒')"
              :readonly="!canEdit"
            />
          </t-form-item>
          <t-form-item :label="$t('容重(g/L)')">
            <t-input
              size="small"
              v-model="purchaseForm.metrics3"
              :placeholder="$t('请输入容重')"
              :readonly="!canEdit"
            />
          </t-form-item>
          <t-typography-title level="h5">{{ $t('扣量及拒收标准') }}</t-typography-title>
          <t-form-item :label="$t('杂质%')">
            <t-input
              size="small"
              v-model="purchaseForm.metrics1standard"
              :placeholder="$t('请输入杂质')"
              :readonly="!canEdit"
            />
          </t-form-item>
          <t-form-item :label="$t('霉变粒%')">
            <t-input
              size="small"
              v-model="purchaseForm.metrics2standard"
              :placeholder="$t('请输入霉变粒')"
              :readonly="!canEdit"
            />
          </t-form-item>
          <t-form-item :label="$t('容重(g/L)')">
            <t-input
              size="small"
              v-model="purchaseForm.metrics3standard"
              :placeholder="$t('请输入容重')"
              :readonly="!canEdit"
            />
          </t-form-item>
          <t-form-item>
            <t-button size="small" theme="primary" type="submit" :loading="loading">{{ $t('确认') }}</t-button>
            <t-button size="small" theme="default" variant="base" type="reset" @click="purchaseVisible = false">{{
              $t('取消')
            }}</t-button>
          </t-form-item>
        </t-form>
      </template>
    </Dialog>

    <!-- 收购计划历史弹窗 -->
    <Dialog v-model="historyDialog" top="3%" width="70vw" :footer="false">
      <template #header>{{ $t('历史数据') }}</template>
      <template #body>
        <t-base-table
          row-key="id"
          size="small"
          :loading="historyLoading"
          :data="historyData"
          :columns="historyTableCols"
          :pagination="{
            current: 1,
            defaultCurrent: 1,
            defaultPageSize: 5,
            total: historyData?.length ?? 0,
          }"
        ></t-base-table>
      </template>
    </Dialog>

    <Dialog v-model="newNoticeDialog" top="5%" width="600px" :footer="false" @close="closeNoticeForm">
      <template #header>{{ $t('收购公告') }}</template>
      <template #body>
        <t-form label-width="auto" :data="noticeForm" @submit="submitNotice">
          <t-form-item :label="$t('公告内容')">
            <t-textarea
              v-model="noticeForm.content"
              :placeholder="$t('请输入公告内容')"
              name="content"
              :autosize="{ minRows: 12 }"
            />
          </t-form-item>
          <t-form-item>
            <t-button size="small" theme="primary" type="submit">{{ $t('提交') }}</t-button>
          </t-form-item>
        </t-form>
      </template>
    </Dialog>
  </div>
</template>
<style scoped lang="less">
  :deep(.t-form__controls-content) {
    justify-content: flex-end;
  }

  .appoint-result {
    height: 400px;
    text-align: center;

    .appoint-result-detail {
      text-align: start;
      padding: 0 64px;
    }

    .footer {
      width: 100%;
      display: flex;
      margin-top: 32px;
      justify-content: center;
    }
  }
</style>

import { get, post, remove } from '@/utils/request'
import { useUserStore } from '@/store'

/* 采购合同可选货品及统计数据 */
export const purchContractGoodsApi = () => {
  const globalStore = useUserStore()
  return post(`/scm/ns/transfer/569/QueryPurchContractItem`, {
    ...JSON.parse(JSON.stringify(globalStore.buildAuthParams())),
  })
}

/* 销售合同可选货品及统计数据 */
export const saleContractGoodsApi = () => {
  const globalStore = useUserStore()
  return post(`/scm/ns/transfer/570/QuerySalesContractItem`, { ...globalStore.buildAuthParams() })
}

/* 采购合同订单列表 */
export const purchContractListApi = (data: object) => {
  const globalStore = useUserStore()
  return post(`/scm/ns/transfer/569/QueryPurchContractList`, {
    ...globalStore.buildAuthParams(),
    ...data,
  })
}

/* 销售合同订单列表 */
export const saleContractListApi = (data: object) => {
  const globalStore = useUserStore()
  return post(`/scm/ns/transfer/570/QuerySalesContractList`, {
    ...globalStore.buildAuthParams(),
    ...data,
  })
}

/* 供应商列表 */
export const vendorListApi = (projectld: number) => get(`/admin/ns/vendor`, { projectld })
/* 客户列表 */
export const customerListApi = (projectld: number) => get(`/admin/ns/customer`, { projectld })

/* 采购合同详情 */
export const purchContractDetailApi = (id: number) => get(`/scm/ns/transfer/569/QueryPurchContractDetail`, { id })

/* 上传交单文件 */
export const uploadOrderFileApi = (nsOrderId: number, orderNo: number, data: object) =>
  post(`/tms/order/file/deliveryDocuments/${nsOrderId}/${orderNo}`, data)

/* 采购/销售合同详情-TMS发运量统计 */
export const purchContractDetailStatisticsApi = (params: object, type: string) =>
  get(`/tms/transport/order/${type}/list`, params)

/* 获取订单交单文件列表 */
export const orderFileListApi = (data: object) => get(`/tms/order/file/deliveryDocuments`, data)

/* 删除交单文件 */
export const deleteOrderFileApi = (dataId: number) => remove(`/tms/order/file/${dataId}`)

/* 根据订单编号查询许可证 */
export const getLicenseApi = (orderNo: number) => get(`/scrm/permit/apply/${orderNo}`)

/* 销售合同详情 */
export const saleContractDetailApi = (id: number) => get(`/scm/ns/transfer/570/QuerySalesContractDetail`, { id })

/* 自提单列表 */
export const selfPickupListApi = (params: object) => get(`/tms/transport/pickup`, params)

/* 自提订单保存 */
export const selfPickSaveApi = (data: object) => post(`/tms/transport/pickup`, data)

/* 自提订单删除 */
export const selfPickDeleteApi = (dataId: number) => remove(`/tms/transport/pickup/${dataId}`)

/*  采购/销售合同详情-TMS运单 */
export const purchSaleContractDetailTmsApi = (params: object) => get<any>(`/tms/transport/dispatch/list`, params)

/* 订单信息 */
export const orderInfoApi = (id: number) => get<any>(`/tms/transport/order/${id}`)

/* 根据运单id查询运综 */
export const logisticsTrack = (id: number) => get<any>(`tms/transport/monitor/track/${id}`)

/* 查询运综信息列表 */
export const logisticsInfoApi = (data: any) => get<any>(`/tms/ec/${data.platform}/detail/${data.hph}`)

/** 运单统计信息 */
export const dispatchCountInfoApi = (params: object) => get<any[]>(`/tms/transport/dispatch/statusStatistics`, params)

/* 采购合同 根据订单id获取订单信息 */
export const purchContractOrderApi = (orderId: any) =>
  get<any[]>(`/scm/ns/transfer/569/QueryPurchContractListItem`, { orderId })

/* 销售合同 根据订单id获取订单信息 */
export const saleContractOrderApi = (orderId: any) =>
  get<any[]>(`/scm/ns/transfer/570/QuerySalesContractListItem`, { orderId })

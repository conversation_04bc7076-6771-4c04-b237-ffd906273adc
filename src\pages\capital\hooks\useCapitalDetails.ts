/* 资金管理详情hooks */

import { getNsList } from '@/api/dict'
import { getPrepayTotal, getCurrency } from '@/api/capital'
import { useNsListLabelRaw } from '@/use/dict'

interface ReceivableParams {
  sumCols: string[]
  groupCols: string[]
  riskCategory: string
  companyProject?: string
  normal: Boolean
  dataLevel?: number | string
}
export default () => {
  const { t, locale } = useI18n()
  const $t = t
  const projectList = ref<any>([])
  // const type: 'prepay' | 'payable' | 'receivables' | 'save' = JSON.parse(history?.state?.info).type
  const projectId = JSON.parse(history?.state?.info).projectId
  let projectName: string[] = []
  const type = computed(() => {
    const info = history?.state?.info
    return info ? JSON.parse(info).type : ''
  })

  /* 项目总额数据 */
  const projectTotal = ref<any>([])
  /* 项目临期总额 */
  const projectOverdue = ref<any>([])
  /* 项目超期总额 */
  const projectOverdueTotal = ref<any>([])
  /* 人员总额 */
  const projectPerson = ref<any>([])
  /* 人员临期金额 */
  const projectPersonOverdue = ref<any>([])

  /* 人员超期金额 */
  const projectPersonOverdueTotal = ref<any>([])

  /* 点击图表传递参数 */
  const chartParams = ref<any>({})
  /* loading */
  const showLoading = ref<boolean>(false)

  /* 货币类型 */
  const currencyType = ref<any>({})
  /* 金额汇率数组 */
  const currencyTypeList = ref<any>([])
  const title = computed(() => {
    switch (type.value) {
      case 'prepay':
        return t('预付')
      case 'payable':
        return t('应付')
      case 'receivables':
        return t('应收')
      case 'save':
        return t('预收')
      default:
        return ''
    }
  })
  const legend = computed(() => {
    switch (type.value) {
      case 'prepay':
        return [t('预付总额'), t('临期金额'), t('超期金额')]
      case 'payable':
        return [t('应付总额'), t('临期应付'), t('超期应付')]
      case 'receivables':
        return [t('应收总额'), t('临期应收'), t('超期应收')]
      case 'save':
        return [t('预收总额'), t('临期预收'), t('超期预收')]
      default:
        return []
    }
  })
  const advancePayment = ref<any>(null)
  const payment = ref<any>(null)

  /* 金额转换请求参数 */
  let currencyParams = {
    fromCurrency: '',
    fromCurrencyList: [],
    toCurrency: 'USD',
  }

  /* 请求参数 */
  let prepayParams = reactive<ReceivableParams>({
    sumCols: ['occupy_amount'],
    groupCols: ['risk_category', 'control_point', 'company_project', 'currency_id'],
    riskCategory: '3',
    companyProject: projectId,
    normal: true,
  })

  /* 按客户帅选参数 */
  let testParams = reactive<ReceivableParams>({
    sumCols: ['occupy_amount'],
    groupCols: ['risk_category', 'control_point', 'company_project', 'currency_id', 'entity_id', 'entity_name'],
    riskCategory: '3',
    companyProject: projectId,
    normal: true,
  })

  /* 获取字典label */
  const nsListLabelRaw = async () => {
    const { currency } = await useNsListLabelRaw('currency')
    currencyType.value = currency
  }

  /* 获取金额汇率 */
  const getCurrencyData = async () => {
    const { data } = await getCurrency(currencyParams)
    currencyTypeList.value = data
  }

  /* 获取公司项目列表 */
  const getNsListData = async () => {
    showLoading.value = true
    try {
      const { data } = await getNsList('CUSTOMLIST_PROJECT_LIST')
      projectList.value = data

      if (projectId !== '0') {
        let temp = projectList.value.filter((item: any) => item.listValue === projectId)[0]?.listLabel
        projectName.push(temp)
      }
    } finally {
      showLoading.value = false
    }
  }

  const typeCodeMap = {
    prepay: '30001',
    receivables: '30003',
    payable: '30002',
    save: '30004',
  }

  /* 获取项目金额总额 */
  const getPrepayTotalData = async (params: ReceivableParams) => {
    const res = await getPrepayTotal(params)
    chartParams.value = {
      name: projectName,
      riskCategory: '3',
      controlPoint: typeCodeMap[type.value],
      normal: true,
      companyProject: projectId,
      type,
    }
    switch (params.dataLevel) {
      case 1:
        projectOverdue.value = formatTotalCurrency(res.data, typeCodeMap[type.value])
        break
      case 2:
        projectOverdueTotal.value = formatTotalCurrency(res.data, typeCodeMap[type.value])
        break
      default:
        projectTotal.value = formatTotalCurrency(res.data, typeCodeMap[type.value])
        break
    }
  }
  /* 按人员项目总额请求 */
  const receivablestApi = async (params: any) => {
    const res = await getPrepayTotal(params)
    chartParams.value = {
      name: projectName,
      riskCategory: '3',
      controlPoint: typeCodeMap[type.value],
      normal: true,
      companyProject: projectId,
      type,
    }
    switch (params.dataLevel) {
      case 1:
        projectPersonOverdue.value = formatTotalCurrency(res.data, typeCodeMap[type.value])
        break
      case 2:
        projectPersonOverdueTotal.value = formatTotalCurrency(res.data, typeCodeMap[type.value])
        break
      default:
        projectPerson.value = formatTotalCurrency(res.data, typeCodeMap[type.value])
        break
    }
  }

  /* 金额类型筛选 */
  const formatTotalCurrency = (data: any, status: any) => {
    const temp: any[] = []
    const result = data
      .map((it: any) => ({
        ...it,
        occupyAmount: Number(it.occupyAmount),
        currencyType: currencyType.value[it.currencyId],
      }))
      .filter((item: any) => item.controlPoint == status)
    result.forEach((item: any) => {
      const existingItem = temp.find((i) => i.entityName === item.entityName && i.currencyType === item.currencyType)
      if (existingItem) {
        existingItem.occupyAmount += item.occupyAmount
      } else {
        temp.push({ ...item })
      }
    })
    const res = result.reduce((acc: any, item: any) => {
      if (!acc[item.currencyType]) {
        acc[item.currencyType] = []
      }
      acc[item.currencyType].push(item)
      return acc
    }, {})

    return Object.values(res).flat()
  }

  onMounted(async () => {
    await nsListLabelRaw()
    await getCurrencyData()
    await getNsListData()
    await getPrepayTotalData(prepayParams)
    await getPrepayTotalData({ ...prepayParams, dataLevel: 1 })
    await getPrepayTotalData({ ...prepayParams, dataLevel: 2 })
    await receivablestApi(testParams)
    await receivablestApi({ ...testParams, dataLevel: 1 })
    await receivablestApi({ ...testParams, dataLevel: 2 })
  })

  return {
    advancePayment,
    payment,
    title,
    showLoading,
    /* --- */
    projectTotal,
    projectOverdue,
    projectOverdueTotal,
    chartParams,
    projectName,
    legend,
    projectPerson,
    projectPersonOverdue,
    projectPersonOverdueTotal,
  }
}

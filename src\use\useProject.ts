import { $getPack, type Options } from '@/api/inquiryQuote/acquisition'
import { useUserStore } from '@/store'
import { useSessionStorage } from '@vueuse/core'
import { useRequest } from 'vue-request'

export const projectCurrency: Record<number, number> = {
  1: 6, // 米糠VND
  2: 6, // 鱼粉VND
  3: 6, // 鱼片VND
  4: 6, // 食用油VND
  5: 1, // 中亚CNY
  7: 1, // 重庆贸易CNY
  9: 6, // 中亚CNY
}

export const projectQuantityUnit: Record<number, number> = {
  1: 1, // 米糠VND
  2: 1, // 鱼粉VND
  3: 1, // 鱼片VND
  4: 1, // 食用油VND
  5: 2, // 中亚CNY
  7: 2, // 重庆贸易CNY
  9: 1, // 中亚CNY
}

export const useProjectStore = defineStore('project', () => {
  const globalStore = useUserStore()
  const activeProjectId = useSessionStorage('scm-activeProjectId', 1)
  const activeProjectName = computed(
    () => menuOptions.value.filter((it: any) => it.value == activeProjectId.value)[0]?.text,
  )
  const activeProjectCurrency = computed(() => {
    return projectCurrency[activeProjectId.value]
  })

  const { data: menuOptions } = useRequest<Options[]>(async () => {
    const res = await $getPack('CUSTOMLIST_PROJECT_LIST')
    if (res.code !== 200) {
      MessagePlugin.error(res.msg)
      return []
    }
    return res.data
      .filter((item) => {
        if (globalStore.projectIds?.length) {
          return globalStore.projectIds.includes(Number(item.listValue))
        }
        return true
      })
      .map((item) => ({ text: item.listLabel, value: Number(item.listValue) }))
  })

  const projectNameMap = computed<Record<string, any>>(() =>
    Object.fromEntries(menuOptions.value?.map((item) => [item.value, item.text])),
  )

  const changeItem = (value: number) => {
    activeProjectId.value = value
  }

  return {
    menuOptions,
    activeProjectId,
    changeItem,
    projectNameMap,
    activeProjectName,
    activeProjectCurrency,
  }
})

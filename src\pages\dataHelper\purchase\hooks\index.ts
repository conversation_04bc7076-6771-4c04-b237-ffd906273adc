import dayjs from 'dayjs'
import DealChart from '../components/dealChart.vue'
import OrderQuery from '../components/orderQuery.vue'
import MoneyQuery from '../components/moneyQuery.vue'
import EnquiryQuotation from '../components/enquiryQuotation.vue'

export default () => {
  const { t } = useI18n()
  const $t = t

  const value = ref(0)
  const disableDate = ref<any>({
    before: dayjs().subtract(2, 'year').format('YYYY'),
    after: dayjs().format('YYYY'),
  })
  const echartList = [
    {
      // 交易查询
      title: $t('交易查询'),
      component: DealChart,
    },
    {
      // 订单查询
      title: $t('订单查询'),
      component: OrderQuery,
    },
    {
      // 资金查询
      title: $t('资金查询'),
      component: MoneyQuery,
    },
    // {
    // 	// 违约查询
    // 	title: 'Tra hủy ước',
    // 	component: BreakContract
    // },
    {
      // 报价查询
      title: $t('报价查询'),
      component: EnquiryQuotation,
    },
  ]

  return {
    disableDate,
    value,
    echartList,
  }
}

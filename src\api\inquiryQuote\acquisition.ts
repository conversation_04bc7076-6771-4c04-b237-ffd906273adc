import { get, post, put } from '@/utils/request'
import { NsListDataVO } from '../dict'

export interface Options {
  text: string
  value: number
}

const Apis = {
  rfqSetting: '/scm/rfqSetting',
  package: '/admin/ns/list/data/byType/',
  offerPost: '/scm/ns/transfer/386/procureOfferPost',
  approve: '/scm/rfqSetting',
  rfqHistory: '/scm/rfqSetting/history',
}

export interface Product {
  id: number
  itemName: string
  icon: string
  useFor: string
  itemCode: string
  itemId: number
  configName: string
  standardPrice: string
  metricsConfig: { label: string; value: string }[]
}
/* 获取产品列表 */
export const $getList = (useFor: string) => get<Product[]>(Apis.rfqSetting, { useFor })

export const $getPack = (listType: string) => get<NsListDataVO[]>(`${Apis.package}${listType}`)
/* 提交报价 */
export const $submit = (form: any) => post(Apis.offerPost, form)

/* 更新采购需求 */
export const updateActivePlan = (plan: any) => put(Apis.rfqSetting, plan)

export const $history = (useFor: string, itemId: number) => get(Apis.rfqHistory, { useFor, itemId })

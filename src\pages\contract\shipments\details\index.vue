<script setup lang="ts">
  import { formatNumber } from '@/utils/tools'
  import useShipmentsDetails from '../hooks/details'
  const {
    waybillDetailloading,
    railFreight,
    waybillInfo,
    railwayTransport,
    roadTransport,
    automobileTransport,
    logisticsInfo,
    submitDocuments,
    canUseTrackInfo,
    expressageInfo,
    orderVisible,
    fileLoading,
    fileList,
    fileColumns,
    donloadFile,
    deleteFile,
    files,
    customUpload,
    filePagination,
    map,
    detailVisible,
  } = useShipmentsDetails()
  const { t } = useI18n()
  const $t = t
</script>
<template>
  <div class="container">
    <t-card>
      <div class="steps-wrap">
        <!-- 铁运 -->
        <t-steps theme="dot" :current="railFreight" readonly v-if="waybillInfo?.trackPlatform == 'ec.95306'">
          <t-step-item v-for="t in railwayTransport" :key="t.id" :title="t.title" />
        </t-steps>
        <!-- 汽运 -->
        <t-steps theme="dot" :current="roadTransport" readonly v-if="waybillInfo?.trackPlatform == 'lgxdl.log56'">
          <t-step-item v-for="t in automobileTransport" :key="t.id" :title="t.title" />
        </t-steps>
      </div>
      <t-descriptions bordered tableLayout="auto" :column="3">
        <t-descriptions-item :label="$t('发站')">{{
          logisticsInfo?.trackItem?.fjm || waybillInfo.shipperAddress
        }}</t-descriptions-item>
        <t-descriptions-item :label="$t('到站')">{{
          logisticsInfo?.trackItem?.djm || waybillInfo.consigneeAddress
        }}</t-descriptions-item>
        <t-descriptions-item :label="$t('托运人')">{{ waybillInfo.cargoOwnerName }}</t-descriptions-item>
        <t-descriptions-item :label="$t('收货人')">{{ waybillInfo.destCompany }}</t-descriptions-item>
        <t-descriptions-item :label="$t('车船信息')">{{ waybillInfo.vehicleNo }}</t-descriptions-item>
        <t-descriptions-item :label="$t('车辆类型')">{{ waybillInfo.vehicleType }}</t-descriptions-item>
        <t-descriptions-item :label="$t('待发运量')"
          >{{ formatNumber(waybillInfo.materialUnexecutedNum / 1000) }} t</t-descriptions-item
        >
        <t-descriptions-item :label="$t('已发运量')"
          >{{ formatNumber(waybillInfo.materialNum / 1000) }} t</t-descriptions-item
        >
        <t-descriptions-item :label="$t('签收数量')"
          >{{ formatNumber(waybillInfo.materialSignNum / 1000) }} t</t-descriptions-item
        >
      </t-descriptions>
      <div class="submit-documents">
        <t-button size="small" @click="submitDocuments">{{ $t('交单') }}</t-button>
      </div>
    </t-card>

    <t-card bordered style="margin-top: 20px" v-if="canUseTrackInfo">
      <t-loading size="small" :loading="waybillDetailloading" show-overlay>
        <div class="waybill-info">
          <!--采购：交付单号 -->
          <div class="waybill-number">{{ $t('运单号') }}：{{ waybillInfo.ladingBillNo }}</div>
          <div>
            {{ $t('订单状态') }}：<span class="status">{{ logisticsInfo?.trackItem?.ztgjjc }}</span>
          </div>
        </div>
        <t-steps layout="vertical" theme="dot" :current="0" readonly>
          <t-step-item :title="expressageInfo?.[0]?.message">
            <template #extra>
              <t-button size="small" v-if="expressageInfo?.length" variant="base" @click="detailVisible = true">{{
                $t('详细信息')
              }}</t-button>
            </template>
          </t-step-item>
          <t-step-item :title="$t('发至')" :content="`${$t('收货人')}：${waybillInfo.destCompany}`" />
        </t-steps>
        <div ref="map" class="map"></div>
      </t-loading>
    </t-card>
    <!-- 交单弹窗 -->
    <Dialog v-model="orderVisible" :footer="false" top="5%" width="620px">
      <template #header>
        <div>{{ $t('交单详情') }}</div>
      </template>
      <template #body>
        <t-table
          row-key="index"
          height="200px"
          size="small"
          :loading="fileLoading"
          :pagination="filePagination"
          :data="fileList"
          :columns="fileColumns"
          table-layout="fixed"
        >
          <template #link="{ row }">
            <t-button size="small" @click="donloadFile(row.id)">{{ $t('下载') }}</t-button>
            <t-popconfirm theme="default" :content="$t('确认删除此文件吗')" @confirm="deleteFile(row.id)">
              <t-button size="small" theme="danger">{{ $t('删除') }}</t-button>
            </t-popconfirm>
          </template>
        </t-table>
        <t-upload v-model="files" theme="custom" :request-method="customUpload" draggable>
          <template #dragContent="params">
            <div>{{ $t('点击上传 / 拖拽到此区域') }}</div>
          </template>
        </t-upload>
      </template>
    </Dialog>
    <!-- 详细信息弹窗 -->
    <Dialog v-model="detailVisible" :footer="false" top="5%" width="620px">
      <template #header>
        <div class="title-info">
          <div class="track">
            <span>{{ $t('物流轨迹') }}</span>
            <span>【</span>
            <span class="status">{{ logisticsInfo?.trackItem?.ztgjjc }}</span>
            <span>】</span>
          </div>
        </div>
      </template>
      <template #body>
        <div class="steps">
          <t-steps layout="vertical" theme="dot" :current="0" readonly>
            <t-step-item v-for="(item, idx) in expressageInfo" :key="item.id" :title="item.detail">
              <template #content>
                <t-card>
                  <div>{{ item.czdz }}</div>
                  <div class="message">{{ item.message }}</div>
                </t-card>
              </template>
            </t-step-item>
          </t-steps>
        </div>
      </template>
    </Dialog>
  </div>
</template>
<style scoped lang="less">
  .steps-wrap {
    width: 100%;
    overflow-x: scroll;
    white-space: nowrap;
    margin-bottom: 20px;
  }
  .submit-documents {
    margin: 20px 0;
    text-align: right;
  }
  .waybill-info {
    display: flex;
    column-gap: 16px;
    margin-bottom: 20px;
  }
  .status {
    color: #f4a808;
  }
  .title-info {
    border-bottom: 1px solid #fff;
    box-sizing: border-box;
    height: 40px;
    line-height: 40px;
  }
  .t-card {
    box-shadow: var(--td-shadow-1);
  }
  .steps {
    height: 460px;
    :deep(.t-steps--vertical) {
      width: 100%;
    }
    :deep(.t-steps-item__inner) {
      width: 100%;
      .t-steps-item__content {
        width: 100%;
      }
    }
  }
  .map {
    width: 100%;
    height: 500px;
    margin-top: 20px;
  }
  /* 隐藏掉高德logo */
  :deep(.amap-logo) {
    display: none;
    opacity: 0 !important;
  }
  :deep(.amap-copyright) {
    opacity: 0;
  }
  :deep(.start-content) {
    position: relative;

    .start-img {
      width: 14px;
      height: 14px;
    }
    .tips {
      position: absolute;
      top: -260%;
      left: 50%;
      transform: translateX(-50%);
      white-space: nowrap;

      padding: 8px;
      // box-sizing: border-box;
      border-radius: 10px;
      background: #fff;
      display: flex;
      align-items: center;
      .img {
        width: 32px;
        height: 32px;
        margin-right: 10px;
        min-width: 32px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .content {
        :nth-child(1) {
          font-size: 12px;
          color: #8c8c8c;
        }
        :nth-child(2) {
          font-size: 16px;
        }
      }
    }
  }
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #e5e5e5;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #b2b2b2;
  }
  :deep(.t-upload__dragger-center) {
    margin: 20px auto 0;
    height: 120px;
  }
</style>

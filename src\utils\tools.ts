/* 数量转换 */
export const formatNumber = (value: any) => {
  if (value === null || value === undefined) return ''
  const number = parseFloat(value)
  if (isNaN(number)) return ''

  return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export const formatNumber2 = (value: any, currencyId: number, locale: any, currencyType: any) => {
  if (value === null || value === undefined) return ''
  const number = parseFloat(value)
  if (isNaN(number)) return ''

  return new Intl.NumberFormat(locale.value, {
    style: 'currency',
    currencyDisplay: 'code',
    currency: currencyType.value[currencyId],
    notation: 'compact',
    compactDisplay: 'long',
    minimumFractionDigits: 3,
    roundingMode: 'halfFloor',
  }).format(value)
}

export function parseNumber(num: any) {
  return Number(num) || 0
}

export function divideScaleStr(num1: any, num2: any, scale: any) {
  return (parseNumber(num1) / parseNumber(num2)).toFixed(scale)
}

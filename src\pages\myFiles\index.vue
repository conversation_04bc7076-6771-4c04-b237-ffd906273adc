<script setup lang="ts">
  import PreviewDocument from './components/PreviewDocument.vue'
  import useFile from './hooks'
  const {
    form,
    visible,
    formData,
    rules,
    fileList,
    columns,
    pagination,
    loading,
    cancelForm,
    confirmForm,
    confirmLoading,
    deleteFile,
    toViewFile,
    renameFile,
    renameVisible,
    cancelRenameForm,
    confirmRenameForm,
    renameForm,
    renameFormData,
    renameRules,
    renameLoading,
    wxCpUserVisible,
    userList,
    selectedIds,
    handleCancel,
    handleConfirm,
  } = useFile()

  const testVisible = ref<boolean>(false)
  const activeId = ref<string>('')

  const handleClick = (id: string) => {
    activeId.value = id
  }

  const cancelFileView = () => (activeId.value = '')

  const sheetRef = ref<any | null>(null)

  const confirmFileView = () => {
    console.log(sheetRef.value.getSelectedRange())
  }
</script>
<template>
  <div class="container">
    <t-card :bordered="false">
      <t-row class="row">
        <t-col>
          <t-button size="small" theme="primary" @click="visible = true">{{ $t('新建文件') }}</t-button>
          <t-button size="small" theme="danger" @click="testVisible = true">测试</t-button>
        </t-col>
      </t-row>
      <t-table
        row-key="index"
        maxHeight="500"
        :data="fileList"
        :columns="columns"
        size="small"
        :loading="loading"
        :pagination="pagination"
        lazy-load
      >
        <template #docName="{ row }">
          <t-link theme="primary" @click="toViewFile(row)">{{ row.docName }}</t-link>
        </template>
        <template #docType="{ row }">
          <span>{{ row.docType === 3 ? $t('文档') : $t('表格') }}</span>
        </template>
        <template #link="{ row }">
          <t-space>
            <t-button size="small" theme="primary">{{ $t('分享') }}</t-button>
            <t-popconfirm theme="warning" content="是否确认删除文件？" @confirm="deleteFile(row)">
              <t-button size="small" theme="danger">{{ $t('删除') }}</t-button>
            </t-popconfirm>
            <t-button size="small" theme="primary" @click="renameFile(row)">{{ $t('重命名') }}</t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>
    <!-- 新建文档弹窗 -->
    <t-dialog v-model:visible="visible" @cancel="cancelForm" @confirm="confirmForm" :confirmLoading="confirmLoading">
      <t-form ref="form" :data="formData" :rules="rules">
        <t-form-item :label="$t('文件名称')" name="docName">
          <t-input v-model="formData.docName" :placeholder="$t('请输入文件名称')"></t-input>
        </t-form-item>
        <t-form-item :label="$t('可查看人员')" name="adminUsers">
          <t-tagInput
            readonly
            v-model="formData.adminUsers"
            :placeholder="`${$t('请选择')}${$t('可查看人员')}`"
            @click="wxCpUserVisible = true"
          />
        </t-form-item>
      </t-form>
    </t-dialog>
    <!-- 重命名弹窗 -->
    <t-dialog
      :header="$t('重命名')"
      v-model:visible="renameVisible"
      @cancel="cancelRenameForm"
      @confirm="confirmRenameForm"
      :confirmLoading="renameLoading"
    >
      <t-form ref="renameForm" :data="renameFormData" :rules="renameRules">
        <t-form-item :label="$t('文件名称')" name="newName">
          <t-input v-model="renameFormData.newName" :placeholder="$t('请输入文件名称')"></t-input>
        </t-form-item>
      </t-form>
    </t-dialog>
    <!-- 企业微信联系人弹窗 -->
    <t-drawer
      destroyOnClose
      v-model:visible="wxCpUserVisible"
      :zIndex="9999"
      size="24%"
      @cancel="handleCancel"
      @confirm="handleConfirm"
    >
      <template #header>{{ $t('请选择联系人') }}</template>
      <div class="user-container">
        <div class="user-list" id="anchor-container">
          <t-checkbox-group v-model="selectedIds">
            <div v-for="item in userList" :key="item.index">
              <div :id="`group-${item.index}`">
                <t-space direction="vertical">
                  <h3>{{ item.index }}</h3>

                  <t-checkbox v-for="it in item.children" :key="it.userId" :value="it.userId">
                    <t-avatar :image="it.avatar" style="margin-right: 4px"> </t-avatar>
                    <span>{{ it.name }}</span>
                  </t-checkbox>
                </t-space>
              </div>
            </div>
          </t-checkbox-group>
        </div>
        <t-anchor container="#anchor-container" :targetOffset="0">
          <t-anchor-item
            v-for="item in userList"
            :key="item.index"
            :href="`#group-${item.index}`"
            :title="item.index"
          />
        </t-anchor>
      </div>
    </t-drawer>

    <!-- 测试 -->
    <t-dialog
      destroyOnClose
      placement="center"
      width="80%"
      style="height: 100%"
      v-model:visible="testVisible"
      header="文件预览"
      @close="cancelFileView"
      @confirm="confirmFileView"
    >
      <div class="test-container">
        <div class="file-list">
          <t-space direction="vertical">
            <div v-for="item in fileList" :key="item.docId">
              <span :class="[activeId == item.docId ? 'active' : '']" @click="handleClick(item.docId)">{{
                item.docName
              }}</span>
            </div>
          </t-space>
        </div>
        <div class="preview">
          <div v-if="!activeId" style="text-align: center; font-size: 18px">点击文件预览</div>
          <div v-else>
            <PreviewDocument :docId="activeId" ref="sheetRef" />
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>
<style scoped lang="less">
  .row {
    margin-bottom: 10px;
  }
  :deep(.t-drawer__body) {
    padding: 0 !important;
    overflow: hidden !important;
  }
  .t-anchor {
    width: fit-content;
    height: 100%;
    overflow-y: auto;
  }
  .t-checkbox-group {
    flex-direction: column;
  }
  .user-container {
    width: 100%;
    height: 100%;
    display: flex;
    .user-list {
      flex: 1;
      overflow-y: auto;
      padding: 10px;
      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        display: none;
        scrollbar-width: none;
      }
    }
  }
  :deep(.t-dialog__wrap) {
    overflow: hidden;
    .t-dialog--center {
      height: 100%;
      overflow: hidden;
    }
    .t-dialog__body {
      height: 86%;
      overflow: hidden;
    }
  }
  .test-container {
    height: 100%;
    display: flex;
    column-gap: 10px;
    .file-list {
      width: 20%;
      max-height: 100%;
      overflow-y: auto;
      border-right: 1px solid #ccc;
      > div {
        cursor: pointer;
        span:hover {
          color: #54c7b2;
          border-bottom: 1px solid #54c7b2;
        }
        .active {
          color: #54c7b2;
        }
      }
    }
    .preview {
      flex: 1;
      > div {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>

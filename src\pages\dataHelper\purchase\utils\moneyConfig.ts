import type { EChartsType } from 'echarts'

export interface PaymentType {
  unpaidAmount: string
  prepayAmount: string
}

export type PaymentState = {
  paymentEchart?: EChartsType
}

export interface OrderItemType {
  orderno: string
  orderid: string
}

export const moneyEchartConfig = (currencyType: string, payment: PaymentType, $t: any, locale: any) => {
  // 格式化 X 轴数值
  const formatXAxisValue = (value: number) => {
    if (value >= 100000000) {
      return `${(value / 100000000).toFixed(1)}${$t('亿')}` // 大于 1 亿时显示为 "X亿"
    }
    return value.toString() // 否则正常显示
  }

  return {
    title: {
      text: `${currencyType}`,
      left: 0,
      top: 0,
      textStyle: {
        fontSize: 14,
      },
    },
    grid: {
      top: 40,
      bottom: 30,
      left: 50,
    },
    dataZoom: [
      {
        type: 'inside',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 100,
        bottom: '10',
        zoomLock: true,
      },
    ],
    tooltip: {
      hideDelay: 100,
      trigger: 'axis',
      axisPointer: { type: 'cross', crossStyle: { color: '#999' } },
      position: ['40%', '30%'],
      formatter: '{b}:{c}',
    },
    legend: { data: ['资金坐标'], type: 'scroll', bottom: 20, show: false },
    xAxis: [
      {
        type: 'category',
        data: ['应收', '预收'],
        axisPointer: { type: 'shadow' },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: $t('金额'),
        minInterval: 1,
        axisLabel: {
          width: 47,
          margin: 3,
          overflow: 'breakAll',
          formatter: (value: number) => formatXAxisValue(value),
        },
        axisTick: {
          show: false,
        },
        min: 0,
        nameTextStyle: {
          color: '#999',
          align: 'left',
        },
        axisLine: {
          lineStyle: {
            width: 1,
            color: '#999',
            type: 'dotted',
          },
        },
      },
    ],
    series: [
      {
        name: '资金坐标',
        type: 'bar',
        data: [payment?.unpaidAmount, payment?.prepayAmount],
        barMinWidth: '12',
        barMaxWidth: '30',
        barCategoryGap: 8,
        label: {
          show: true,
          position: 'inside',
          fontSize: 12,
          formatter: function ({ value }: any) {
            return new Intl.NumberFormat(locale.value, {
              style: 'decimal',
              notation: 'compact',
              compactDisplay: 'long',
              minimumFractionDigits: 3,
              roundingMode: 'halfFloor',
            }).format(Number(value))
          },
        },
        itemStyle: {
          color: ({ dataIndex }: any) => ['#595DBB', '#8BC76E', '#EDC758'][dataIndex],
        },
        tooltip: {
          show: true,
        },
      },
    ],
  }
}

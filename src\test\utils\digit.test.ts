import { describe, it, expect } from 'vitest'

import { times, plus } from '@/utils/digit'

describe('digit', () => {
  it('times', () => {
    expect(times(1, 1)).toBe(1)
    expect(times(1, 1.1)).toBe(1.1)
    expect(times(1, 1.1, 1.1)).toBe(1.21)
  })

  it('plus', () => {
    expect(plus(1, 1)).toBe(2)
    expect(plus(1, 1.1)).toBe(2.1)
    expect(plus(1, 2, 5)).toBe(8)
    expect(plus(1, 2, 5.12, 6.23)).toBe(14.35)
  })
})

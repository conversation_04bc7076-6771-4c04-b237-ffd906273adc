<script setup lang="ts">
  import Dialog from '@/components/dialog/index.vue'
  import usePosition from './hooks'
  const { t } = useI18n()
  const $t = t
  const {
    form,
    projectList,
    loading,
    onsubmit,
    onReset,
    finishedProductColumns,
    positionStatementMap,
    positionType,
    visible,
    quantityDetail,
    formData,
    bomList,
    openBomActive,
    showActiveBomPopup,
    currentBom,
    saveBom,
    editBom,
    showEnitRecipe,
    recipeForm,
    productColumns,
    rawColumns,
    handleEditProductRow,
    showProduct,
    rowForm,
    ROW_FORM_RULES,
    productPosition,
    rawPosition,
    saveProduct,
    handleChange,
    removeProductRow,
    handleEditRawRow,
    isEdit,
    removeRawRow,
    saveRecipe,
    deleteRecipe,
    flag,
    saveLoading,
    delLoading,
  } = usePosition()
</script>
<template>
  <div class="container">
    <t-card>
      <t-loading :loading="loading" show-overlay>
        <t-form label-width="auto" layout="inline" @submit="onsubmit" @reset="onReset">
          <t-form-item :label="$t('项目名称')">
            <t-select
              size="small"
              v-model="form.projectId"
              :options="projectList"
              :keys="{ label: 'text', value: 'value' }"
              :placeholder="$t('请选择项目')"
              :onChange="onsubmit"
            />
          </t-form-item>
          <t-form-item>
            <t-button size="small" theme="primary" type="submit">{{ $t('查询') }}</t-button>
            <t-button size="small" theme="default" type="reset">{{ $t('重置') }}</t-button>
          </t-form-item>
        </t-form>
        <t-row :gutter="26">
          <template v-for="type in positionType" :key="type.value">
            <t-col :span="6" v-if="positionStatementMap[type.value]?.length">
              <h1 class="title">{{ type.label }}</h1>
              <t-table
                row-key="index"
                bordered
                size="small"
                max-height="800"
                :data="positionStatementMap[type.value]"
                :columns="finishedProductColumns"
                lazy-load
              >
                <template #quantity="{ row }">
                  <t-link theme="primary" @click="quantityDetail(row)">{{ row.quantity }}</t-link>
                </template>
              </t-table>
            </t-col>
          </template>
        </t-row>
        <div class="list">
          <div class="list-title">
            <h1 class="">{{ $t('生效配方') }}</h1>
            <t-button size="small" theme="primary" @click="editBom()">{{ $t('增加配方') }}</t-button>
          </div>
          <t-list split v-if="bomList?.length">
            <t-list-item v-for="bom in bomList" :key="bom.id">
              <t-link theme="primary" @click="editBom(bom.id)">{{ bom.bomName }}</t-link>
              <template #action>
                <t-switch v-model="bom.active" @click.stop @change="openBomActive(bom)">
                  <template #label>{{ bom.active ? $t('生效') : $t('失效') }}</template>
                </t-switch>
              </template>
            </t-list-item>
          </t-list>
        </div>
      </t-loading>
    </t-card>
    <!-- 头寸量详情 -->
    <Dialog v-model="visible" :footer="false" width="520px">
      <template #header>{{ $t('头寸量详情') }}</template>
      <template #body>
        <t-descriptions size="small" colon bordered :column="2">
          <t-descriptions-item :label="$t('自有库存')">{{ formData.quantityOrigin }} T</t-descriptions-item>
          <t-descriptions-item :label="$t('暂存库存')">{{ formData.quantityTemp }} T</t-descriptions-item>
          <t-descriptions-item :label="$t('已采待入')">{{ formData.quantityPurch }} T</t-descriptions-item>
          <t-descriptions-item :label="$t('生产待入')" v-if="['2', '3'].includes(formData.positionType!)"
            >{{ formData.quantityPlanProduce }} T</t-descriptions-item
          >
          <t-descriptions-item :label="$t('已销待出')">{{ formData.quantitySales }} T</t-descriptions-item>
          <t-descriptions-item :label="$t('领料待出')" v-if="['1', '3'].includes(formData.positionType!)"
            >{{ formData.quantityProduceCost }} T</t-descriptions-item
          >
        </t-descriptions>
      </template>
    </Dialog>
    <!-- 开/关配方 -->
    <Dialog
      v-model="showActiveBomPopup"
      width="320px"
      @confirm="saveBom"
      :cancelBtn="null"
      :closeBtn="false"
      :closeOnOverlayClick="false"
      :closeOnEscKeydown="false"
    >
      <template #body>
        <span>{{ $t('计划生产') }}({{ $t('吨') }}):</span><t-input v-model="currentBom.produceQuantity" />
      </template>
    </Dialog>
    <!-- 新增/编辑配方 -->
    <Dialog v-model="showEnitRecipe" top="2%" :footer="false">
      <template #header>{{ flag ? $t('编辑') : $t('新增') }}{{ $t('配方') }}</template>
      <template #body>
        <t-input size="small" v-model="recipeForm.bomName" :placeholder="$t('请输入配方名称')" />
        <div class="tab-title">
          <h1>{{ $t('成品比例') }}：</h1>
          <t-icon
            name="add-circle-filled"
            color="#0052d9"
            style="font-size: 24px !important; cursor: pointer"
            @click="handleEditProductRow()"
          />
        </div>
        <t-table row-key="index" height="160px" :data="recipeForm.productExpVoList" :columns="productColumns" lazy-load>
          <template #itemName="{ row }">
            <t-link theme="primary" @click="handleEditProductRow(row)">{{ row.itemName }}</t-link>
          </template>
          <template #primary="{ row }">
            <t-icon name="check" style="font-size: 20px !important" v-if="row.primary" />
          </template>
          <template #delete="{ row }">
            <t-icon
              name="minus-circle-filled"
              color="#d54941"
              style="font-size: 24px !important; cursor: pointer"
              @click="removeProductRow(row)"
            />
          </template>
        </t-table>
        <div class="tab-title">
          <h1>{{ $t('原料比例') }}：</h1>
          <t-icon
            name="add-circle-filled"
            color="#0052d9"
            style="font-size: 24px !important; cursor: pointer"
            @click="handleEditRawRow()"
          />
        </div>
        <t-table row-key="index" height="160px" :data="recipeForm.rawExpVoList" :columns="rawColumns" lazy-load>
          <template #itemName="{ row }">
            <t-link theme="primary" @click="handleEditRawRow(row)">{{ row.itemName }}</t-link>
          </template>
          <template #delete="{ row }">
            <t-icon
              name="minus-circle-filled"
              color="#d54941"
              style="font-size: 24px !important; cursor: pointer"
              @click="removeRawRow(row)"
            />
          </template>
        </t-table>
        <div class="btn">
          <t-popconfirm v-if="flag" theme="default" :content="$t('确认删除配方吗')" @confirm="deleteRecipe">
            <t-button size="small" :loading="delLoading" theme="danger">{{ $t('删除') }}</t-button>
          </t-popconfirm>
          <t-button size="small" :loading="saveLoading" @click="saveRecipe">{{ $t('保存') }}</t-button>
        </div>
      </template>
    </Dialog>
    <!-- 配方比例 -->
    <Dialog v-model="showProduct" width="320px" :footer="false">
      <template #header>{{ isEdit ? $t('编辑') : $t('新增') }}{{ $t('产品') }}</template>
      <template #body>
        <t-form ref="rowform" :data="rowForm" :rules="ROW_FORM_RULES" @submit="saveProduct">
          <t-form-item :label="$t('货品') + '：'" name="itemName">
            <t-select
              size="small"
              v-model="rowForm.itemName"
              :options="rowForm.isProduct ? productPosition : rawPosition"
              :keys="{ label: 'itemName', value: 'itemNo' }"
              :placeholder="$t('请选择货品')"
              @change="handleChange"
            />
          </t-form-item>
          <t-form-item :label="$t('主产物') + '：'" v-if="rowForm.isProduct">
            <t-checkbox v-model="rowForm.primary" />
          </t-form-item>
          <t-form-item :label="$t('比率') + '：'" name="ratio">
            <t-input-number size="small" v-model="rowForm.ratio" theme="normal" :max="100" :min="0" />
          </t-form-item>
          <t-form-item>
            <t-button size="small" theme="default" @click="showProduct = false">{{ $t('取消') }}</t-button>
            <t-button size="small" type="submit">{{ $t('保存') }}</t-button>
          </t-form-item>
        </t-form>
      </template>
    </Dialog>
  </div>
</template>
<style scoped lang="less">
  .title {
    margin: 20px 0 14px;
  }
  .list {
    margin-top: 40px;
    .list-title {
      display: flex;
      align-items: center;
      column-gap: 20px;
    }
  }
  .tab-title {
    display: flex;
    align-items: center;
    column-gap: 26px;
    margin: 20px 0;
  }
  .btn {
    margin-top: 16px;
    text-align: right;
  }
</style>

import { TableProps, FormProps, CustomValidator } from 'tdesign-vue-next'
import { groupBy, sortBy, isNil } from 'lodash-es'
import { useDict } from '@/use/dict'
import { getNsList } from '@/api/dict'
import { loadBomByProject, edit, detail, removeRecipe } from '@/api/inventory'
import { positionTotal } from '@/api/inventory'
import { divideScaleStr } from '@/utils/tools'
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'
import { useProjectStore } from '@/use/useProject'

export default () => {
  const { t } = useI18n()
  const $t = t
  const { menuOptions, activeProjectId } = storeToRefs(useProjectStore())
  /* form表单 */
  const form = ref({
    projectId: activeProjectId.value,
  })
  /* 项目列表 */
  const projectList = ref(menuOptions)
  /* loading */
  const loading = ref(false)
  /* 列表数据 */
  const positionData = ref<any[]>([])
  /* 生效配方列表 */
  const bomList = ref<any[]>([])
  /* 头存量弹窗 */
  const visible = ref<boolean>(false)
  /* 当前id */
  const currentId = ref<number>(0)
  /* 表格配置 */
  const finishedProductColumns = computed<TableProps['columns']>(() => [
    { colKey: 'itemName', title: $t('产品'), ellipsis: true },
    { colKey: 'quantity', title: $t('头寸量') + `(${$t('吨')})`, ellipsis: true },
    { colKey: 'positionCost', title: $t('头寸成本'), ellipsis: true },
  ])
  const itemCodeNameMap = ref<Record<string, string>>()
  const dicts = useDict('position_item_type')
  const positionStatementMap = computed<Record<string, any[]>>(() => groupBy(positionData.value, 'positionType'))
  const productPosition = computed<any[]>(() => positionStatementMap.value?.[2] || [])
  const rawPosition = computed<any[]>(() => [
    ...(positionStatementMap.value?.[1] || []),
    ...(positionStatementMap.value?.[3] || []),
  ])
  const positionType = computed(() => sortBy(dicts.position_item_type, 'dictSort'))
  const activeBomList = computed(() => bomList.value.filter((item) => item.active).map((item) => item.id))
  /* 头存量弹窗form */
  const formData = ref<any>({
    positionType: null,
    quantityOrigin: '',
    quantityPlanProduce: '',
    quantityProduceCost: '',
    quantityTemp: '',
    quantityPurch: '',
    quantitySales: '',
  })
  const showActiveBomPopup = ref<boolean>(false)
  const currentBom = ref<any>({})
  /* 是否展示配方 */
  const showEnitRecipe = ref<boolean>(false)
  const showProduct = ref<boolean>(false)
  /* loading */
  const saveLoading = ref<boolean>(false)
  const delLoading = ref<boolean>(false)

  /* 配方详情form */
  const DEFAULT_RECIPE_FORM = {
    produceQuantity: '0',
    productExp: '',
    productExpVoList: [],
    rawExp: '',
    rawExpVoList: [],
    bomName: '',
    projectId: form.value.projectId,
  }
  const recipeForm = ref<any>({
    ...DEFAULT_RECIPE_FORM,
  })
  /* 表格配置 */
  const productColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'itemName', title: $t('产品') },
    { colKey: 'primary', title: $t('主产物') },
    { colKey: 'ratio', title: $t('比率'), cell: (h, { col, row }) => `${row.ratio}%` },
    { colKey: 'delete', title: $t('操作') },
  ])
  const rawColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'itemName', title: $t('产品') },
    { colKey: 'ratio', title: $t('比率'), cell: (h, { col, row }) => `${row.ratio}%` },
    { colKey: 'delete', title: $t('操作') },
  ])
  const rowform = ref(null)
  const rowForm = ref<any>({
    itemCode: '',
    itemName: '',
    ratio: 0,
  })
  /* 是否编辑 */
  const isEdit = ref<boolean>(false)
  const flag = ref<boolean>(false)
  /* 自定义校验 */
  const ratioValidator: CustomValidator = (val: any) => {
    const numVal = Number(val)
    if ((numVal <= 0.0001 && numVal > 0) || numVal > 100) {
      return { result: false, message: '请输入0-100！', type: 'error' }
    }

    return { result: true, message: '' }
  }
  const ROW_FORM_RULES: FormProps['rules'] = {
    itemName: [{ required: true, message: $t('请选择货品') }],
    ratio: [{ required: true, message: $t('请输入比率'), type: 'error' }, { validator: ratioValidator }],
  }
  /* 原料列表 */
  const loadPositionData = async (bomIdList: number[] = []) => {
    loading.value = true
    try {
      const res = await positionTotal(form.value.projectId, bomIdList)
      if (res.code !== 200) {
        throw new Error(res.msg)
      }
      res.data.forEach((item: any) => {
        item.quantity = Number(Number(item.quantity) / 1000).toFixed(3)
        item.positionCost = Number(Number(item.positionCost) * 1000).toFixed(0)
      })
      positionData.value = res.data
      itemCodeNameMap.value = res.data.reduce((acc, cur) => {
        acc[cur.itemNo] = cur.itemName
        return acc
      }, {} as Record<string, string>)
    } finally {
      loading.value = false
    }
  }
  const getBomList = async () => {
    const res = await loadBomByProject(form.value.projectId)
    bomList.value = sortBy(
      (res.data || []).map((d: any) => ({
        ...d,
        active: false,
      })),
      'bomName',
    )
  }
  const getCash = async () => {
    const bomList = activeBomList.value
    try {
      await nextTick()
      await loadPositionData(bomList)
    } catch (e: any) {
      return
    } finally {
    }
  }
  /* 表单搜素 */
  const onsubmit = async () => {
    activeProjectId.value = form.value.projectId
    await getBomList()
    await getCash()
  }

  /* 表单重置 */
  const onReset = () => {}
  /* 头存量详情 */
  const quantityDetail = (row: any) => {
    visible.value = true
    formData.value = {
      ...row,
      quantityOrigin: divideScaleStr(row.quantityOrigin, 1000, 3).toString(),
      quantityPlanProduce: divideScaleStr(row.quantityPlanProduce, 1000, 3).toString(),
      quantityProduceCost: divideScaleStr(row.quantityProduceCost, 1000, 3).toString(),
      quantityTemp: divideScaleStr(row.quantityTemp, 1000, 3).toString(),
      quantityPurch: divideScaleStr(
        Number(row.quantityPurch) > Number(row.quantityTemp)
          ? Number(row.quantityPurch) - Number(row.quantityTemp)
          : row.quantityPurch,
        1000,
        3,
      ).toString(),
      quantitySales: divideScaleStr(row.quantitySales, 1000, 3).toString(),
    }
  }
  /* 打开弹窗 */
  const openBomActive = async (bom: any) => {
    if (bom.active) {
      currentBom.value = bom
      showActiveBomPopup.value = true
      return
    }
    bom.active = false
    currentBom.value = {}
    await getCash()
  }
  /* 保存配方*/
  const saveBom = async () => {
    const res = await edit({
      id: currentBom.value.id,
      produceQuantity: Number(currentBom.value.produceQuantity).toFixed(3) || '0.00',
    })
    showActiveBomPopup.value = false
    currentBom.value.active = true
    await getCash()
  }
  /* 编辑配方 */
  const editBom = async (id?: number) => {
    recipeForm.value = { ...DEFAULT_RECIPE_FORM }
    showEnitRecipe.value = true

    flag.value = !!id
    currentId.value = id
    await loadBomInfo(id)
  }

  const handleChange = (value: any, context: any) => {
    rowForm.value.itemCode = value
    rowForm.value.itemName = context.selectedOptions[0].label
  }

  const handleCode = async (io: string) => {
    if (!itemCodeNameMap.value) {
      await loadPositionData()
    }
    return itemCodeNameMap.value?.[io] || ''
  }

  /* 获取配方信息 */
  const loadBomInfo = async (id: number) => {
    if (id) {
      try {
        const res = await detail(Number(id))
        if (res.data?.productExpVoList) {
          for (let i = 0; i < res.data.productExpVoList.length; i++) {
            const item = res.data.productExpVoList[i]
            item.itemName = await handleCode(item.itemCode)
            item.rowIndex = i
          }
        }
        if (res.data?.rawExpVoList) {
          for (let i = 0; i < res.data.rawExpVoList.length; i++) {
            const item = res.data.rawExpVoList[i]
            item.itemName = await handleCode(item.itemCode)
            item.rowIndex = i
          }
        }

        recipeForm.value = res.data
      } finally {
      }
    } else {
      recipeForm.value = {
        produceQuantity: '0',
        productExp: '',
        productExpVoList: [],
        rawExp: '',
        rawExpVoList: [],
        bomName: '',
        projectId: form.value.projectId,
      }
    }
  }

  /* 新增成品 */
  const handleEditProductRow = async (row?: any) => {
    if (row) {
      isEdit.value = true
      rowForm.value = {
        ...row,
        rowIndex: row.rowIndex,
        isProduct: true,
        isRaw: false,
      }
    } else {
      isEdit.value = false
      rowForm.value = {
        itemCode: '',
        primary: false,
        itemName: '',
        ratio: 0,
        isProduct: true,
        isRaw: false,
        rowIndex: recipeForm.value.productExpVoList.length,
      }
    }
    showProduct.value = true
  }
  /* 新增原料 */
  const handleEditRawRow = (row?: any) => {
    if (row) {
      isEdit.value = true
      rowForm.value = {
        ...row,
        rowIndex: row.rowIndex,
        isProduct: false,
        isRaw: true,
      }
    } else {
      isEdit.value = false
      rowForm.value = {
        itemCode: '',
        itemName: '',
        ratio: 0,
        isProduct: false,
        isRaw: true,
        rowIndex: recipeForm.value.rawExpVoList.length,
      }
    }
    showProduct.value = true
  }
  /* 保存修改 */
  const saveProduct = ({ validateResult }) => {
    if (validateResult === true) {
      if (rowForm.value.itemCode) {
        if (rowForm.value.isProduct) {
          if (!isNil(rowForm.value.rowIndex)) {
            recipeForm.value.productExpVoList[rowForm.value.rowIndex] = {
              itemCode: rowForm.value.itemCode,
              itemName: rowForm.value.itemName,
              primary: rowForm.value.primary,
              ratio: rowForm.value.ratio,
              rowIndex: rowForm.value.rowIndex,
            }
          } else {
            recipeForm.value.productExpVoList = [
              ...(recipeForm.value.productExpVoList || []),
              {
                itemCode: rowForm.value.itemCode,
                itemName: rowForm.value.itemName,
                primary: rowForm.value.primary,
                ratio: rowForm.value.ratio,
              },
            ]
          }
        } else if (rowForm.value.isRaw) {
          if (!isNil(rowForm.value.rowIndex)) {
            recipeForm.value.rawExpVoList[rowForm.value.rowIndex] = {
              itemCode: rowForm.value.itemCode,
              itemName: rowForm.value.itemName,
              ratio: rowForm.value.ratio,
              rowIndex: rowForm.value.rowIndex,
            }
          } else {
            recipeForm.value.rawExpVoList = [
              ...(recipeForm.value.rawExpVoList || []),
              {
                itemCode: rowForm.value.itemCode,
                itemName: rowForm.value.itemName,
                ratio: rowForm.value.ratio,
              },
            ]
          }
        }
      }

      showProduct.value = false
    }
  }
  /* 删除行 */
  const removeProductRow = (row: any) =>
    (recipeForm.value.productExpVoList = recipeForm.value.productExpVoList.toSpliced(row.rowIndex, 1))
  const removeRawRow = (row: any) =>
    (recipeForm.value.rawExpVoList = recipeForm.value.rawExpVoList.toSpliced(row.rowIndex, 1))

  /* 保存配方 */
  const saveRecipe = async () => {
    let total = recipeForm.value.productExpVoList.reduce((prev: any, next: any) => prev + Number(next.ratio), 0)
    if (total > 100) return MessagePlugin.warning($t('配比总和不能大于') + '100')

    total = recipeForm.value.rawExpVoList.reduce((prev: any, next: any) => prev + Number(next.ratio), 0)
    if (total > 100) return MessagePlugin.warning($t('配比总和不能大于') + '100')

    recipeForm.value.productExp = JSON.stringify(recipeForm.value.productExpVoList)
    recipeForm.value.rawExp = JSON.stringify(recipeForm.value.rawExpVoList)
    if (!recipeForm.value.bomName) return MessagePlugin.warning($t('请输入配方名称'))
    if (!recipeForm.value.productExp) return MessagePlugin.warning($t('请选择成品添加配比'))
    if (!recipeForm.value.rawExp) return MessagePlugin.warning($t('请选择原料添加配比'))
    saveLoading.value = true
    const res = await edit({
      ...recipeForm.value,
      id: Number(currentId.value),
    })
    if (res.code !== 200) return MessagePlugin.error(res.msg)
    saveLoading.value = false
    MessagePlugin.success(res.msg)
    showEnitRecipe.value = false
    await getBomList()
  }
  /* 删除配方 */
  const deleteRecipe = async () => {
    if (!currentId.value) return
    delLoading.value = true
    const res = await removeRecipe(Number(currentId.value))
    if ((res as any).code !== 200) return MessagePlugin.error((res as any).msg)
    delLoading.value = false
    MessagePlugin.success((res as any).msg)
    showEnitRecipe.value = false
    await getBomList()
  }

  onMounted(async () => {
    await loadPositionData()
    await getBomList()
  })

  return {
    form,
    projectList,
    loading,
    onsubmit,
    onReset,
    finishedProductColumns,
    positionStatementMap,
    positionType,
    visible,
    quantityDetail,
    formData,
    bomList,
    openBomActive,
    showActiveBomPopup,
    currentBom,
    saveBom,
    editBom,
    showEnitRecipe,
    recipeForm,
    productColumns,
    rawColumns,
    handleEditProductRow,
    showProduct,
    rowform,
    rowForm,
    ROW_FORM_RULES,
    productPosition,
    rawPosition,
    saveProduct,
    handleChange,
    removeProductRow,
    handleEditRawRow,
    isEdit,
    removeRawRow,
    saveRecipe,
    deleteRecipe,
    flag,
    saveLoading,
    delLoading,
  }
}

<script setup lang="ts">
  import { appointMonth } from '@/api/inquiryQuote/appoint'
  import { groupBy, sortBy, countBy } from 'lodash-es'
  import dayjs from 'dayjs'
  import {
    DialogPlugin,
    MessagePlugin,
    type BaseTableCol,
    type TableRowData,
    type CalendarCell,
  } from 'tdesign-vue-next'
  import { refreshAppoint, checkoutAppoint, vehicleDateAppoint, cancelAppoint } from '@/api/inquiryQuote/appoint'
  import { IconFont } from 'tdesign-icons-vue-next'
  import { VuePrintNext } from 'vue-print-next'
  import { useAcquisitionStore } from '../store/useAcquisitionStore'

  const { t } = useI18n()
  const $t = t

  const acquisitionStore = useAcquisitionStore()
  const productList = computed(() => acquisitionStore.productList)
  const quotationForm = ref({
    itemId: productList.value?.[0]?.itemId || '',
  })

  const appointStatus = {
    '': {
      theme: 'default',
      label: t('未入场'),
    },
    0: {
      theme: 'danger',
      label: t('已失约'),
    },
    1: {
      theme: 'primary',
      label: t('已取号'),
    },
    2: {
      theme: 'success',
      label: t('已入场'),
    },
  }

  const calendarCurrentYear = ref(dayjs().year())
  const calendarCurrentMonth = ref(dayjs().month() + 1)
  const calendarRangeStart = computed(() =>
    dayjs()
      .year(calendarCurrentYear.value)
      .month(calendarCurrentMonth.value - 1)
      .startOf('month')
      .startOf('day'),
  )
  const calendarRangeEnd = computed(() => calendarRangeStart.value.endOf('month').endOf('day'))

  const calendarDataMap = ref<Record<string, [string, any[]][]>>()
  const loadAppointList = async (itemId: string, dateStart: string, dateEnd: string) => {
    const res = await appointMonth(itemId, dateStart, dateEnd)
    const appointList = Object.fromEntries(
      Object.entries(
        groupBy(
          (res.data || []).map((d) => {
            const at = dayjs(d.appointTime)
            const isOverdue = at.add(3, 'hours').isBefore(dayjs())
            return {
              ...d,
              isOverdue,
              status: d.isCheckout ? '2' : d.appointSerial ? '1' : isOverdue ? '0' : '',
              appointDate: dayjs(d.appointTime).format('YYYY-MM-DD'),
              ampm: dayjs(d.appointTime).format('A'),
            }
          }),
          'appointDate',
        ),
      ).map((e) => [e[0], sortBy(Object.entries(groupBy(e[1], 'ampm')), 0)]),
    )
    calendarDataMap.value = appointList
  }

  watchEffect(() => {
    loadAppointList(
      quotationForm.value.itemId,
      calendarRangeStart.value.format('YYYY-MM-DD HH:mm:ss'),
      calendarRangeEnd.value.format('YYYY-MM-DD HH:mm:ss'),
    )
  })
  const isCalendarDataShow = (data: CalendarCell) => calendarDataMap.value?.[data.formattedDate]?.length

  const handleCalenderChangeMonth = (opts: { month: string; year: string }) => {
    calendarCurrentYear.value = Number(opts.year)
    calendarCurrentMonth.value = Number(opts.month)
  }
  const appointListDialog = ref<boolean>(false)
  const currentDialogList = ref<any[]>([])
  const currentAppointCols: BaseTableCol<TableRowData>[] = [
    { colKey: 'vendorName', title: $t('供应商') },
    { colKey: 'itemName', title: $t('货品') },
    { colKey: 'vehicleNo', title: $t('车号') },
    { colKey: 'driverName', title: $t('司机') },
    { colKey: 'appointSerial', width: '8em', title: $t('预约号'), align: 'center' },
    {
      colKey: 'isCheckout',
      width: '10em',
      title: $t('是否入场'),
      align: 'center',
    },
    { colKey: 'status', title: $t('状态') },
    { colKey: 'link', title: $t('操作'), width: '16em', fixed: 'right', align: 'center' },
  ]
  const currentDate = ref<string>('')
  const currentDateIsActive = computed(() => {
    const date = currentDate.value.split(' ')[0]
    return dayjs(date).isSame(dayjs(), 'D')
  })
  const openAppointList = (date: string, ampm: string, list: any[]) => {
    currentDate.value = `${date} ${ampm}`
    currentDialogList.value = list
    appointListDialog.value = true
  }

  const refreshLoading = ref<boolean>(false)
  const refreshSerialNo = async (row: any) => {
    refreshLoading.value = true
    const res = await refreshAppoint(row.id)
    if (res.code !== 200) {
      return MessagePlugin.error(t(res.msg))
    }
    currentDialogList.value = []
    await loadAppointList(
      quotationForm.value.itemId,
      calendarRangeStart.value.format('YYYY-MM-DD HH:mm:ss'),
      calendarRangeEnd.value.format('YYYY-MM-DD HH:mm:ss'),
    )
    await nextTick()
    const dateSplit = currentDate.value.split(' ')
    currentDialogList.value = calendarDataMap.value[dateSplit[0]]?.find((a) => a[0] === dateSplit[1])?.[1] || []
    refreshLoading.value = false
  }

  const printCursorRef = useTemplateRef('print-cursor')
  const appointPrintDialog = ref<boolean>(false)
  const printAppointRow = ref<any>()
  const openPrintAppoint = async (row: any) => {
    const res = await vehicleDateAppoint(row.vehicleNo, row.appointTime)
    if (res.code !== 200) {
      return MessagePlugin.error(t(res.msg))
    }
    if (!res.data) {
      return MessagePlugin.warning(t('未查询到预约信息'))
    }
    const { data } = res
    if (!data.serialNo) {
      return MessagePlugin.warning(t('未取号'))
    }
    if (data.isExpired) {
      return MessagePlugin.warning(t('已过号'))
    }
    printAppointRow.value = data
    appointPrintDialog.value = true
    await nextTick()
    callPrint()
  }
  const callPrint = () => {
    if (!printCursorRef.value) {
      return
    }
    const p = new VuePrintNext({
      el: printCursorRef.value,
      noPrintSelector: '#print-footer',
      vue: getCurrentInstance(),
    })
  }

  const appointCheckin = async (row: any) => {
    refreshLoading.value = true
    const checkoutRes = await checkoutAppoint(row.id)
    if (checkoutRes.code !== 200) {
      return MessagePlugin.error(t(checkoutRes.msg))
    }
    currentDialogList.value = []
    await loadAppointList(
      quotationForm.value.itemId,
      calendarRangeStart.value.format('YYYY-MM-DD HH:mm:ss'),
      calendarRangeEnd.value.format('YYYY-MM-DD HH:mm:ss'),
    )
    await nextTick()
    const dateSplit = currentDate.value.split(' ')
    currentDialogList.value = calendarDataMap.value[dateSplit[0]]?.find((a) => a[0] === dateSplit[1])?.[1] || []
    refreshLoading.value = false
  }

  const calendarConfig = {
    mode: { visible: false },
    weekend: { visible: false },
    year: {
      visible: true,
      selectProps: {
        autoWidth: false,
      },
    },
    month: {
      visible: true,
      selectProps: {
        autoWidth: false,
      },
    },
  }

  const cancelOrder = async (row: any) => {
    const confirmDialog = DialogPlugin.confirm({
      header: t('提示'),
      body: t('确定要取消订单吗？'),
      confirmBtn: $t('确认'),
      cancelBtn: $t('取消'),
      onConfirm: async ({ e }) => {
        const res = await cancelAppoint(row.id, row.contractId)
        if (res.code !== 200) {
          return MessagePlugin.error(t(res.msg))
        }
        currentDialogList.value = []
        await loadAppointList(
          quotationForm.value.itemId,
          calendarRangeStart.value.format('YYYY-MM-DD HH:mm:ss'),
          calendarRangeEnd.value.format('YYYY-MM-DD HH:mm:ss'),
        )
        await nextTick()
        const dateSplit = currentDate.value.split(' ')
        currentDialogList.value = calendarDataMap.value[dateSplit[0]]?.find((a) => a[0] === dateSplit[1])?.[1] || []
        refreshLoading.value = false
        confirmDialog.destroy()
      },
      onClose: () => {
        confirmDialog.hide()
      },
    })
  }
</script>

<template>
  <t-calendar
    :fillWithZero="false"
    :firstDayOfWeek="7"
    preventCellContextmenu
    :controllerConfig="calendarConfig"
    :month="calendarCurrentMonth"
    :year="calendarCurrentYear"
    @month-change="handleCalenderChangeMonth"
  >
    <template #head>
      <t-form layout="inline" label-width="auto" :data="quotationForm">
        <t-space>
          <t-form-item :label="$t('产品名称')">
            <t-select
              size="small"
              :keys="{ label: 'itemName', value: 'itemId' }"
              v-model="quotationForm.itemId"
              :options="productList"
              :placeholder="$t('请选择产品')"
            />
          </t-form-item>
          <t-button
            size="small"
            theme="primary"
            variant="base"
            @click="
              loadAppointList(
                quotationForm.itemId,
                calendarRangeStart.format('YYYY-MM-DD HH:mm:ss'),
                calendarRangeEnd.format('YYYY-MM-DD HH:mm:ss'),
              )
            "
            >{{ $t('刷新') }}</t-button
          >
        </t-space>
      </t-form>
    </template>
    <template #cellAppend="data">
      <div v-if="isCalendarDataShow(data) && calendarDataMap?.[data.formattedDate]">
        <t-tag
          v-for="item in calendarDataMap[data.formattedDate]"
          theme="success"
          size="small"
          style="width: 100%"
          @click="openAppointList(data.formattedDate, item[0], item[1])"
          >{{ item[0] }}: {{ item[1]?.length }}({{ $t('取号') }}:
          {{ countBy(item[1], (d) => !!d.appointSerial)?.['true'] ?? 0 }}/{{ $t('入场') }}:
          {{ countBy(item[1], (d) => !!d.isCheckout)?.['true'] ?? 0 }})</t-tag
        >
      </div>
    </template>
  </t-calendar>

  <!-- 预约日历单日记录弹窗 -->
  <Dialog v-model="appointListDialog" top="3%" width="65vw" :footer="false">
    <template #header>{{ $t('预约记录') }}({{ currentDate }})</template>
    <template #body>
      <t-base-table
        row-key="id"
        size="small"
        :data="currentDialogList"
        :columns="currentAppointCols"
        :pagination="{
          current: 1,
          defaultCurrent: 1,
          defaultPageSize: 10,
          total: currentDialogList?.length ?? 0,
        }"
      >
        <template #isCheckout="{ row }">
          <t-switch :value="row.isCheckout" />
        </template>
        <template #status="{ row }">
          <t-tag v-if="appointStatus[row.status]" :theme="appointStatus[row.status].theme">{{
            appointStatus[row.status]?.label
          }}</t-tag>
        </template>
        <template #link="{ row }">
          <t-space size="small">
            <t-button v-if="row.isCheckout && row.contractId" theme="danger" size="small" @click="cancelOrder(row)">{{
              $t('取消')
            }}</t-button>
            <t-button theme="primary" size="small" @click="openPrintAppoint(row)">{{ $t('打印') }}</t-button>
            <t-button v-if="currentDateIsActive" theme="primary" size="small" @click="refreshSerialNo(row)">{{
              $t('取号')
            }}</t-button>
            <t-button v-if="!row.isCheckout" theme="primary" size="small" @click="appointCheckin(row)">{{
              $t('入场')
            }}</t-button>
          </t-space>
        </template>
      </t-base-table>
    </template>
  </Dialog>
  <Dialog
    v-model="appointPrintDialog"
    top="5%"
    width="375px"
    :footer="false"
    @close="
      () => {
        printAppointRow = null
        appointPrintDialog = false
      }
    "
  >
    <template #header>{{ $t('打印预约单') }}</template>
    <template #body>
      <div ref="print-cursor" class="appoint-result">
        <icon-font name="check-circle-filled" style="color: var(--td-brand-color)" size="80px" />
        <template v-if="printAppointRow">
          <t-typography-title level="h2">{{ printAppointRow.serialNo }}</t-typography-title>
          <div class="appoint-result-detail">
            <t-typography-title level="h6">{{ $t('排队剩余') }}：{{ printAppointRow.queueIndex }}</t-typography-title>
            <t-typography-title level="h6">{{ $t('预约车辆') }}：{{ printAppointRow.vehicleNo }}</t-typography-title>
            <t-typography-title level="h6">
              {{ $t('预约时间') }}：{{
                dayjs(printAppointRow.appointTime).format('YYYY-MM-DD HH:mm:ss')
              }}</t-typography-title
            >
            <div id="print-footer" class="footer">
              <t-button @click="callPrint">{{ $t('打印') }}</t-button>
            </div>
          </div>
        </template>
      </div>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>

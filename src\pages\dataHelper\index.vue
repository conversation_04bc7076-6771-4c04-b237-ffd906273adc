<script setup lang="ts">
  import Empty from '@/components/Empty/index.vue'
  import PurchDataHelper from './purchase/index.vue'
  import SalesDataHelper from './sales/index.vue'
  import { useUserStore } from '@/store'

  const globalStore = useUserStore()
  const value = ref<number>(1)
  const { t } = useI18n()
  const $t = t
</script>
<template>
  <div class="container">
    <t-tabs v-model="value">
      <t-tab-panel :value="1" v-if="globalStore.vendorIds?.length">
        <template #label>{{ globalStore.userType === 1 ? $t('采购') : $t('销售') }}</template>
        <template #panel>
          <PurchDataHelper />
        </template>
      </t-tab-panel>
      <t-tab-panel :value="2" v-if="globalStore.customerIds?.length">
        <template #label>{{ globalStore.userType === 1 ? $t('销售') : $t('采购') }}</template>
        <template #panel>
          <SalesDataHelper />
        </template>
      </t-tab-panel>
    </t-tabs>
    <Empty v-if="!globalStore.vendorIds?.length && !globalStore.customerIds?.length" :description="$t('暂无数据')" />
  </div>
</template>
<style scoped lang="less"></style>

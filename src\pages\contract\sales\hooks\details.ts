import { useNsListLabelRaw } from '@/use/dict'
import {
  orderFileListApi,
  uploadOrderFileApi,
  deleteOrderFileApi,
  saleContractDetailApi,
  purchContractDetailStatisticsApi,
  selfPickupListApi,
  selfPickDeleteApi,
  selfPickSaveApi,
} from '@/api/contract'
import { sum } from 'lodash-es'
import { PrimaryTableCol, TableRowData, UploadFile, RequestMethodResponse } from 'tdesign-vue-next'
import { useUserStore } from '@/store'
import { useRouter } from 'vue-router'
import Sess from '@/plugins/cache'

export default () => {
  const globalStore = useUserStore()
  const { t, locale } = useI18n()
  const $t = t
  const router = useRouter()
  /* 订单详情 */
  const orderInfo = ref()
  Sess.session.set('salesInfo', history?.state?.info)
  const info = history.state.info
  /* 确保面包屑切换时特面不报错 */
  if (info) orderInfo.value = JSON.parse(info)
  else orderInfo.value = JSON.parse(Sess.session.get('salesInfo'))

  /* 金额币种 */
  const currencyType = ref<any>({})
  /* 交易方式 */
  const tradeType = ref<{ [key: string]: any }>({})
  /* 提货方式 */
  const pickMethod = ref<any>({})
  /* 已收金额/已开票金额 */
  const totalReceived = ref<number>(0)
  const totalPayment = ref<number>(0)
  /* 出库单 */
  const deliveryInfo = ref<any>([])
  /* 已出库数量 */
  const materialTotalOutboundNum = ref<number>(0)
  /* 销售发票 */
  const saleContract = ref<any[]>([])
  const transport = ref<object[]>([])
  /* 已发运量 */
  const totalDespatch = ref<number>(0)
  /* 待发运量 */
  const waitingDespatch = ref<number>(0)
  /* 已签收数量 */
  const materialTotalSignNum = ref<number>(0)
  /* 总量 */
  const totalMaterialNum = ref<number>(0)

  /* 步骤条 */
  const stepsActive = ref<number>(0)
  /* 是否展示交单详情 */
  const visible = ref<boolean>(false)
  /* 是否展示已出库 */
  const outboundVisible = ref<boolean>(false)
  /* 是否展示已开票 */
  const invoiceAmountVisible = ref<boolean>(false)
  /* 是否展示待提货弹窗 */
  const pickVisible = ref<boolean>(false)
  /* 是否展示自提订单form */
  const selfPickupFormVisible = ref<boolean>(false)
  /* 自提单列表 */
  const selfPickupList = ref<any>([])
  /* 待提数量 */
  const waitSelfPickup = ref<number>(0)
  /* 自提总数量 */
  const totalSelfPickup = ref<number>(0)

  const itemNameOptions = ref<any>([])
  /* 交单文件列表 */
  const fileList = ref<any>([])
  /* 上传文件列表 */
  const files = ref<any[]>([])
  const canUpload = ref<boolean>(false)
  const canDelete = ref<boolean>(false)
  const canDownload = ref<boolean>(true)
  /* loading */
  const fileLoading = ref<boolean>(false)
  const stepLoading = ref<boolean>(false)
  const desLoading = ref<boolean>(false)

  const detailLoading = ref<boolean>(false)
  const selfPickupFormLoading = ref<boolean>(false)
  /* 文件表格配置 */
  const fileColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'orderNo', title: $t('销售单号'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'filename', title: $t('文件名称'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'updateTime', title: $t('上传时间'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'link', title: $t('操作'), ellipsis: true, width: 160, align: 'center' },
  ])
  /* 已出库表格配置 */
  const outboundColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'tranid', title: $t('出库单号'), ellipsis: true, width: 100, align: 'center' },
    { colKey: 'quantity', title: $t('出库数量'), ellipsis: true, width: 80, align: 'center' },
    { colKey: 'trandate', title: $t('出库时间'), ellipsis: true, width: 80, align: 'center' },
  ])
  /* 已开票表格配置 */
  const invoiceAmountColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'tranid', title: $t('结算单号'), ellipsis: true, width: 80, align: 'center' },
    {
      colKey: 'invoice_sum',
      title: () => (globalStore.userType == 2 ? $t('应付总额') : $t('应收总额')),
      ellipsis: true,
      width: 120,
      align: 'center',
    },
    {
      colKey: 'paid',
      title: () => (globalStore.userType == 2 ? $t('已付总额') : $t('已收金额')),
      ellipsis: true,
      width: 120,
      align: 'center',
    },
    {
      colKey: 'unpaid',
      title: () => (globalStore.userType == 2 ? $t('未付总额') : $t('未收金额')),
      ellipsis: true,
      width: 120,
      align: 'center',
    },
  ])
  /* 待提货表格配置 */
  const selfPickupColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'itemName', title: $t('货物名称'), ellipsis: true, width: 120, align: 'center' },
    { colKey: 'pickupQuantity', title: $t('装货重量'), ellipsis: true, width: 80, align: 'center' },
    { colKey: 'vehicleNo', title: $t('车头号'), ellipsis: true, width: 100, align: 'center' },
    { colKey: 'trailerNo', title: $t('挂车号'), ellipsis: true, width: 100, align: 'center' },
    { colKey: 'driverName', title: $t('司机姓名'), ellipsis: true, width: 80, align: 'center' },
    { colKey: 'driverTel', title: $t('电话'), ellipsis: true, width: 120, align: 'center' },
    { colKey: 'driverIdCard', title: $t('身份证号'), ellipsis: true, width: 180, align: 'center' },
    { colKey: 'vehicleType', title: $t('车型'), width: 100, align: 'center' },
    { colKey: 'tareWeight', title: $t('空车皮重'), width: 80, align: 'center' },
    { colKey: 'link', title: $t('操作'), ellipsis: true, width: 120, align: 'center' },
  ])
  /* 表格分页 */
  const filePagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })
  const selfPickupPagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })
  const DEFAULT_FORM = {
    id: '',
    contract_name: '',
    subsidiary_id: '',
    nsOrderId: '',
    orderNo: '',
    vehicleNo: '',
    pickupQuantity: '0',
    itemName: '',
    trailerNo: '',
    driverName: '',
    driverTel: '',
    driverIdCard: '',
    vehicleType: '',
    tareWeight: '',
  }
  /* 自提订单form */
  const selfPickupForm = ref({ ...DEFAULT_FORM })

  /* 获取项目label字典 */
  const getProjectLabel = async () => {
    const {
      CUSTOMLIST_PROJECT_LIST,
      currency,
      CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE,
      CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI,
    } = await useNsListLabelRaw(
      'CUSTOMLIST_PROJECT_LIST',
      'currency',
      'CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE',
      'CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI',
    )
    // projectType.value = CUSTOMLIST_PROJECT_LIST
    currencyType.value = currency
    tradeType.value = CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE
    pickMethod.value = CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI
  }

  /* 获取合同详情 */
  const getSaleContractDetail = async () => {
    console.log('🚀 ~ getProjectLabel ~ currencyType.value:', currencyType.value)
    stepLoading.value = true
    desLoading.value = true
    try {
      const { data } = await saleContractDetailApi(orderInfo.value.order_id)
      deliveryInfo.value = (data as any).itemShip

      materialTotalOutboundNum.value = (data as any).itemShip.reduce(
        (a: any, b: any) => Number(a) + Number(b.quantity),
        0,
      )
      saleContract.value = (data as any).invoice
      transport.value = (data as any).transport
      totalReceived.value = saleContract.value.reduce((a: any, b: any) => Number(a) + Number(b.invoice_sum), 0)
      totalPayment.value = saleContract.value.reduce((a: any, b: any) => Number(a) + Number(b.paid), 0)

      if (orderInfo.value.delivery_type !== 1) {
        if (Number(waitingDespatch.value) > 0) stepsActive.value = 1
        if (Number(materialTotalOutboundNum.value) > 0) stepsActive.value = 2
        if (Number(totalDespatch.value) > 0) stepsActive.value = 3
        if (Number(totalDespatch.value) - Number(materialTotalSignNum.value) > 0) stepsActive.value = 4
        if (Number(materialTotalSignNum.value) > 0) stepsActive.value = 5
        if (Number(totalReceived.value) > 0) stepsActive.value = 6
      } else {
        if (Number(totalMaterialNum.value) - Number(materialTotalOutboundNum.value) > 0) stepsActive.value = 1
        if (Number(materialTotalOutboundNum.value) > 0) stepsActive.value = 2
        if (Number(totalReceived.value) > 0) stepsActive.value = 3
      }
    } finally {
      stepLoading.value = false
      desLoading.value = false
    }
  }
  /* TMS发运量统计 */
  const getTmsStatistics = async () => {
    const params = {
      pageNum: '1',
      pageSize: orderInfo.value?.item_name.split('/').length,
      orderNo: orderInfo.value?.order_no,
    }
    const res = await purchContractDetailStatisticsApi(params, 'sales')
    totalDespatch.value = (res as any).rows?.reduce((a: any, b: any) => Number(a) + Number(b.materialExecutedNum), 0)
    waitingDespatch.value = (res as any).rows?.reduce(
      (a: any, b: any) => Number(a) + Number(b.materialUnexecutedNum),
      0,
    )
    materialTotalSignNum.value = (res as any).rows?.reduce(
      (a: any, b: any) => Number(a) + Number(b.materialTotalSignNum),
      0,
    )
    totalMaterialNum.value = (res as any).rows?.reduce((a: any, b: any) => Number(a) + Number(b.materialNum), 0)
    if (!totalMaterialNum.value) {
      totalMaterialNum.value = Number(
        sum(orderInfo.value.item_quantity.split('/').map((it: any) => Number(it))).toFixed(2),
      )
    }
  }
  /* 获取交单列表 */
  const orderFileList = async () => {
    fileLoading.value = true
    try {
      const { data } = await orderFileListApi({
        nsOrderId: orderInfo.value.order_id,
        orderNo: orderInfo.value.order_no,
      })
      fileList.value = data
      filePagination.value.total = fileList.value.length
    } finally {
      fileLoading.value = false
    }
  }
  const params = ref({
    orderNo: orderInfo.value.order_no,
    pageNum: 1,
    pageSize: 10,
  })
  /* 获取自提订单列表 */
  const getSelfPickupList = async () => {
    const res = await selfPickupListApi(params.value)
    selfPickupList.value = (res as any).rows
    selfPickupPagination.value.total = res.total
    waitSelfPickup.value =
      totalSelfPickup.value - selfPickupList.value.reduce((a: any, b: any) => Number(a) + Number(b?.pickupQuantity), 0)
  }

  /* 分页改变 */
  const onPageChange = (pageInfo: any) => {
    selfPickupPagination.value.defaultCurrent = pageInfo.current
    selfPickupPagination.value.defaultPageSize = pageInfo.pageSize
    params.value.pageNum = pageInfo.current
    params.value.pageSize = pageInfo.pageSize
    getSelfPickupList()
  }

  /* 打开自提订单form */
  const openSelfPickupForm = () => {
    selfPickupFormVisible.value = true
    selfPickupForm.value.contract_name = orderInfo.value?.customer_name
    selfPickupForm.value.subsidiary_id = orderInfo.value?.subsidiary_name
    selfPickupForm.value.nsOrderId = orderInfo.value?.order_id
    selfPickupForm.value.orderNo = orderInfo.value?.order_no
    selfPickupForm.value.itemName = orderInfo.value?.item_name.split('/').length <= 1 ? orderInfo.value?.item_name : ''
    itemNameOptions.value = orderInfo.value?.item_name.split('/').map((it: string) => ({
      label: it,
      value: it,
    }))
  }
  /* 下载交单文件 */
  const donloadFile = (id: number) => {
    const fileUrl = import.meta.env.VITE_APP_BASE_API + `/tms/order/file/download/${id}`
    location.assign(fileUrl)
  }
  /* 删除交单文件*/
  const deleteFile = async (id: number) => {
    try {
      await deleteOrderFileApi(id)
      MessagePlugin.success($t('删除成功'))
      orderFileList()
    } finally {
    }
  }
  /* 交单上传附件 */
  const customUpload = async (file: UploadFile | UploadFile[]): Promise<RequestMethodResponse> => {
    return new Promise(async (resolve) => {
      try {
        const formData: any = new FormData()
        if (Array.isArray(file)) {
          file.forEach((f) => {
            formData.append('file', f.raw)
          })
        } else formData.append('file', file.raw)

        const result = await uploadOrderFileApi(orderInfo.value.order_id, orderInfo.value.order_no, formData)
        MessagePlugin.success($t('上传成功'))
        await orderFileList()
        files.value = []
        resolve({
          status: 'success',
          response: { url: result.msg },
        })
      } catch (error) {
        MessagePlugin.error($t('上传失败'))
        resolve({
          status: 'fail',
          error: error.msg,
          response: {},
        })
      }
    })
  }
  /* 下载合同文件 */
  const downloadFile = (row: any) => window.open(row.contract_files, '_blank')
  /* 跳转交货列表 */
  const consignment = (active?: number) => {
    if (orderInfo.value.delivery_type !== 1) {
      router.push({ path: `/contract/shipments/${orderInfo.value.order_no}`, query: { active } })
    } else pickVisible.value = true
  }
  /* 编辑自提订单 */
  const editSelfPickup = (row: any) => {
    selfPickupForm.value = {
      contract_name: orderInfo.value?.customer_name,
      subsidiary_id: orderInfo.value?.subsidiary_name,
      ...row,
    }
    selfPickupFormVisible.value = true
  }
  /* 保存自提订单 */
  const submitSelfPickupForm = async () => {
    selfPickupFormLoading.value = true
    try {
      await selfPickSaveApi(selfPickupForm.value)
      MessagePlugin.success($t('保存成功'))
      selfPickupForm.value = { ...DEFAULT_FORM }
      selfPickupFormVisible.value = false
      getSelfPickupList()
    } finally {
      selfPickupFormLoading.value = false
    }
  }
  /* 删除自提订单 */
  const deleteSelfPickup = async (row: any) => {
    try {
      await selfPickDeleteApi(row.id)
      MessagePlugin.success($t('删除成功'))
      getSelfPickupList()
    } finally {
    }
  }
  /* 重置自提订单form */
  const resetSelfPickupForm = () => (selfPickupForm.value = { ...DEFAULT_FORM })

  /* 金额转换 */
  const formatCurrency = (value: any, currencyId: number) => {
    if (!currencyType.value[currencyId]) return ''
    if (value === null || value === undefined) return ''
    const number = parseFloat(value)
    if (isNaN(number)) return ''
    return new Intl.NumberFormat(locale.value, {
      style: 'currency',
      currencyDisplay: 'code',
      currency: currencyType.value[currencyId],
      notation: 'compact',
      compactDisplay: 'long',
      minimumFractionDigits: 2,
      roundingMode: 'halfFloor',
    }).format(value)
  }

  onBeforeMount(async () => {
    await getProjectLabel()
    await getTmsStatistics()
  })

  onMounted(async () => {
    await getSaleContractDetail()
    await orderFileList()
    await getSelfPickupList()
  })
  return {
    orderInfo,
    currencyType,
    tradeType,
    pickMethod,
    totalReceived,
    totalPayment,
    stepsActive,
    waitingDespatch,
    materialTotalOutboundNum,
    totalDespatch,
    materialTotalSignNum,
    totalMaterialNum,
    visible,
    fileLoading,
    filePagination,
    fileList,
    fileColumns,
    donloadFile,
    deleteFile,
    files,
    customUpload,
    canUpload,
    canDelete,
    canDownload,
    downloadFile,
    outboundVisible,
    deliveryInfo,
    outboundColumns,
    formatCurrency,
    invoiceAmountVisible,
    saleContract,
    invoiceAmountColumns,
    desLoading,
    stepLoading,
    consignment,
    pickVisible,
    waitSelfPickup,
    openSelfPickupForm,
    selfPickupList,
    selfPickupColumns,
    selfPickupPagination,
    editSelfPickup,
    deleteSelfPickup,
    selfPickupFormVisible,
    selfPickupForm,
    submitSelfPickupForm,
    resetSelfPickupForm,
    itemNameOptions,
    selfPickupFormLoading,
    onPageChange,
  }
}

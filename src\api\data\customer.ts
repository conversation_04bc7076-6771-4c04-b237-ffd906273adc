import { post } from '@/utils/request'

export function reportSumInfo(data: any) {
  return post<any>('/scm/ns/transfer/577/ReportSum', data)
}

export function executingOrder(data: any) {
  return post<any>('/scm/ns/transfer/577/ExecutingOrder', data)
}

export function executedOrder(data: any) {
  return post<any>('/scm/ns/transfer/577/ExecutedOrder', data)
}

export function waitPayment(data: any) {
  return post<any>('/scm/ns/transfer/577/WaitPayment', data)
}

export function overPayment(data: any) {
  return post<any>('/scm/ns/transfer/577/OverPayment', data)
}

export function tradingByProduct(data: any) {
  return post<any>('/scm/ns/transfer/577/TradingByProduct', data)
}

export function tradingPriceTrend(data: any) {
  return post<any>('/scm/ns/transfer/577/TradingPriceTrend', data)
}

export function quoteQuery(data: any) {
  return post<any>('/scm/ns/transfer/577/InquiryList', data)
}

interface WarnDataParams {
  sumCols: string[]
  groupCols: string[]
  riskCategory: string
  entityIdList?: any[]
  companyProject?: string | number // 添加 companyProject 属性
  normal: Boolean
  dataLevel?: number | string
}
export const getWarnData = (data: WarnDataParams) => {
  return post<any>('/scm/ns/transfer/577/GetWarnData', {
    sumCols: ['occupy_amount'],
    groupCols: ['risk_category', 'control_point', 'item_code', 'item_name', 'data_level', 'currency_id'],
    riskCategory: '3',
    controlPoint: '30003',
    normal: true,
  })
}

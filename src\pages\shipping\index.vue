<script setup lang="ts">
  import useShipping from './hooks'
  const {
    tabList,
    tabLoading,
    orderType,
    globalStore,
    projectItemList,
    defaultValue,
    handleClick,
    searchForm,
    statusList,
    changeStatus,
    statusId,
    orderList,
    columns,
    pagination,
    loading,
    pickMethod,
    waybillDetail,
    searchOrder,
    onReset,
    changeTab,
  } = useShipping()
  const { t } = useI18n()
  const $t = t
</script>
<template>
  <div class="container">
    <t-card>
      <t-tabs v-model="orderType" @change="changeTab">
        <t-tab-panel
          v-if="globalStore.userType === 1 || globalStore.customerIds?.length"
          :value="item.value"
          v-for="item in tabList"
          :key="item.value"
        >
          <template #label>
            {{ item.label }}
          </template>
          <t-loading :loading="tabLoading">
            <t-row :gutter="26" style="margin-top: 20px">
              <t-col :xs="12" :sm="12" :md="12" :lg="2">
                <t-card>
                  <t-tree
                    v-model:actived="defaultValue"
                    checkStrictly
                    expandOnClickNode
                    size="small"
                    :data="projectItemList"
                    :keys="{ disabled: 'isParent', children: 'items' }"
                    expandAll
                    activable
                    hover
                    transition
                    @click="handleClick"
                  />
                </t-card>
              </t-col>
              <t-col :xs="12" :sm="12" :md="12" :lg="10">
                <t-form layout="inline" label-width="auto" :data="searchForm" @submit="searchOrder" @reset="onReset">
                  <t-form-item :label="$t('模糊搜索')">
                    <t-input size="small" :placeholder="$t('请输入搜索内容')" v-model="searchForm.searchValue" />
                  </t-form-item>
                  <t-form-item :label="$t('车船号')">
                    <t-input size="small" :placeholder="$t('请输入车船号')" v-model="searchForm.vehicleNo" />
                  </t-form-item>
                  <t-form-item :label="$t('运单号')">
                    <t-input size="small" :placeholder="$t('请输入运单号')" v-model="searchForm.ladingBillNo" />
                  </t-form-item>
                  <t-form-item :label="$t('目的地')">
                    <t-input size="small" :placeholder="$t('请输入目的地')" v-model="searchForm.consigneeAddress" />
                  </t-form-item>
                  <t-form-item :label="$t('合同号')">
                    <t-input size="small" :placeholder="$t('请输入合同号')" v-model="searchForm.contractNo" />
                  </t-form-item>
                  <t-form-item>
                    <t-button size="small" type="submit">{{ $t('搜索') }}</t-button>
                    <t-button size="small" theme="default" type="reset">{{ $t('重置') }}</t-button>
                  </t-form-item>
                </t-form>
                <div class="status-btn">
                  <template v-for="s in statusList" :key="s.id">
                    <t-badge :count="s.count" size="small">
                      <t-button
                        size="small"
                        :theme="statusId == s.id ? 'success' : 'primary'"
                        :variant="statusId == s.id ? 'base' : 'outline'"
                        @click="changeStatus(s)"
                        >{{ s.title }}</t-button
                      >
                    </t-badge>
                    <img
                      class="img"
                      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGIAAAASCAYAAACghwvPAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAACrSURBVFhH7ZixCsQgEAXz/39oY6NWgtpYGxY05OJdsbtHIuENDGzSpBhE49bAEiCEklJKn3QghJIYY8s59yc5CKGEQhhj1DEQQskIQaaU+ls+rBDOOXjRWnuE0MRghTh/EH7qvT9mSQxRiFor/CIFkMYQhQC/CSGIYmBF/NHbVwScpY17zNwIBCvE9cQAHzo1gZlH/iPAzAihiUAghBLcNS0Cbl9fBkIsQWs7z5rmJA4hcEkAAAAASUVORK5CYII="
                    />
                  </template>
                </div>
                <t-table
                  size="small"
                  style="margin-top: 20px"
                  row-key="index"
                  max-height="450px"
                  :loading="loading"
                  :data="orderList"
                  :columns="columns"
                  :pagination="pagination"
                  lazy-load
                >
                  <template #dispatchStatus="{ row }">
                    <t-tag size="small" color="var(--td-brand-color-active)">{{
                      pickMethod?.[row.dispatchStatus]
                    }}</t-tag>
                  </template>
                  <template #link="{ row }">
                    <t-link theme="primary" @click="waybillDetail(row)">{{ $t('运单详情') }}</t-link>
                  </template>
                </t-table>
              </t-col>
            </t-row>
          </t-loading>
        </t-tab-panel>
      </t-tabs>
    </t-card>
  </div>
</template>
<style scoped lang="less">
  .t-card {
    height: 100%;
  }
  :deep(.t-card__body) {
    height: 100%;
  }

  .status-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px;
    .img {
      width: 50px;
      height: 10px;
      &:last-child {
        display: none;
      }
    }
  }
</style>

const IMG_BASE_URL = 'https://telegram.honoroad.com.cn/honoroad_static/goods-icon/'

export const getGoodImg = (key?: string) => {
  switch (key) {
    case '673':
      return `${IMG_BASE_URL}干米糠.png`
    case '674':
      return `${IMG_BASE_URL}湿米糠.png`
    case '10099':
      return `${IMG_BASE_URL}混合米糠.png`
    case '675':
      return `${IMG_BASE_URL}碎米.png`
    case '672':
      return `${IMG_BASE_URL}砻糠.png`
    case '10425':
      return `${IMG_BASE_URL}统糠.png`
    case '10874':
      return `${IMG_BASE_URL}湿稻谷.png`
    case '10':
      return `${IMG_BASE_URL}干湿比例.png`
    default:
      return `${IMG_BASE_URL}小麦.png`
  }
}

// Cám hỗn hợp 混合米糠   10099
// Cám to 统糠  10425
// Tấm 糠碎  675
// Cám ướt 湿米糠  674
// Cám khô 干米糠  673
// Cám y 砻糠   672
// LÚA TƯƠI 湿稻谷  10874

export const getImgByNameUtil = (name: string) => {
  return `${IMG_BASE_URL}${name}.png`
}

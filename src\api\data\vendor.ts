import { post } from '@/utils/request'

// data
export const purchData = <D = any>(action: string, data: any) => post<D>(`/scm/ns/transfer/462/${action}`, data)

// datapurchaseall
export const purchDetailAll = <D = any>(action: string, data: any) => post<D>(`/scm/ns/transfer/465/${action}`, data)

// datapurchase
export const purchQuantityChart = <D = any>(action: string, data: any) =>
  post<D>(`/scm/ns/transfer/466/${action}`, data)

// dataexchanged
export const purchExchangedChart = <D = any>(action: string, data: any) =>
  post<D>(`/scm/ns/transfer/467/${action}`, data)

// datacontract
export const purchContractChart = <D = any>(action: string, data: any) =>
  post<D>(`/scm/ns/transfer/468/${action}`, data)

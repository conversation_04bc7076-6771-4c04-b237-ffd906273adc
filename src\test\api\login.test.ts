import { describe, it, expect, vi, beforeEach } from 'vitest'
import axios from 'axios'
import MockAdapter from 'axios-mock-adapter'
import { getCaptcha, userLogin, userInfo, getRouters, uploadFIle } from '@/api/login'

// 初始化 MockAdapter
let mock: MockAdapter

beforeEach(() => {
  mock = new MockAdapter(axios)
})

describe.only('login测试', () => {
  it('获取验证码', async () => {
    mock.onGet('/auth/code').reply(200, {
      code: 200,
      msg: '操作成功',
    })

    const response = await getCaptcha()
    expect(response).toHaveProperty('code', 200)
    expect(response).toHaveProperty('msg', '操作成功')
  })
})

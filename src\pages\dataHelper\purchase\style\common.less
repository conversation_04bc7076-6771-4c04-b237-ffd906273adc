.echart-title {
  width: 100vw;
  font-size: 18px;
  line-height: 24px;
  text-align: left;
  margin: 8px 0;
  box-sizing: border-box;
  padding-left: 8px;
}

.bottom-line {
  width: 100vw;
  height: 1px;
  background-color: rgba(158, 157, 157, 0.6);
}

.select-list {
  display: flex;
  column-gap: 24px;
  .select-container {
    margin: 30px 0;

    font-size: 14px;
    display: flex;
    column-gap: 20px;
    align-items: center;

    .label {
      min-width: fit-content;
      margin-right: 16px;
    }

    .value-container {
      color: #666;
      margin-left: 12px;
    }
  }
}

.echart-pie-container {
  width: 90vw;
  height: 340px;
  margin: 0 auto 0;
}

.echart-line-container {
  width: 90vw;
  height: 380px;
  margin: 0 auto;
}

.total-container & {
  &:nth-child(1) {
    margin-bottom: 0;
  }
}

.total-container {
  font-size: 14px;
  text-align: left;
  padding: 12px 4vw;
  width: 100vw;
  margin: 20px 0;
  color: #666;

  span {
    margin-left: 8px;
    color: #333;
  }
}

<template>
  <div class="container">
    <SRM class="row-container" />
    <CRM class="row-container" />
    <TMS class="row-container" />
    <WMS class="row-container" />
  </div>
</template>

<script lang="ts">
  export default {
    name: 'DashboardBase',
  }
</script>

<script setup lang="ts">
  import SRM from './components/SRM.vue'
  import CRM from './components/CRM.vue'
  import TMS from './components/TMS.vue'
  import WMS from './components/WMS.vue'
</script>

<style scoped>
  .container {
    padding: 8px;
  }
  .row-container:not(:last-child) {
    margin-bottom: 16px;
  }
</style>

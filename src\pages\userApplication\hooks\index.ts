import { userInfoList, agreeUser, refuseUser } from '@/api/userApplication'
import { getCompany } from '@/api/inquiryQuote/rawPurch'
import { getNsList } from '@/api/dict'

export default () => {
  const $t = useI18n().t

  /* 表格配置 */
  const columns = computed(() => [
    {
      colKey: 'userName',
      title: $t('用户名称'),
      width: '60',
      ellipsis: true,
      cell: (h, { col, row }) => h('span', row.user.userName),
    },
    { colKey: 'countryArea', title: $t('地区'), width: '60', ellipsis: true },
    { colKey: 'entityName', title: $t('公司名称'), width: '100', ellipsis: true },
    { colKey: 'zaloId', title: 'ZaloId', width: '90', ellipsis: true },
    { colKey: 'serialNo', title: $t('税号/身份证号'), width: '90', ellipsis: true },
    { colKey: 'entityTel', title: $t('联系方式'), width: '70', ellipsis: true },
    { colKey: 'entityAddress', title: $t('联系地址'), width: '140', ellipsis: true },
    { colKey: 'bankAccount', title: $t('银行账号'), width: '100', ellipsis: true },
    { colKey: 'link', title: $t('操作'), width: '60', ellipsis: true },
  ])
  /* 表格分页 */
  const pagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })
  /* 用户信息列表 */
  const userRegisterList = ref<any[]>([])
  /* 当前注册用户信息 */
  const currentUserInfo = ref<any>({})
  /* loading */
  const lodaing = ref<boolean>(false)
  /* 详情弹窗 */
  const visible = ref<boolean>(false)
  /* 是否为代理商/底商 */
  const isAgent = ref<boolean>(false)
  /* 是否为供应商 */
  const isVendor = ref<boolean>(false)
  /* 是否为 客户*/
  const isCustomer = ref<boolean>(false)
  /* 关联子公司 */
  const subsidiaryIds = ref<number[]>([])
  /* 子公司列表 */
  const subCompany = ref<any[]>([])
  const images = ref<string[]>([])
  const pdfs = ref<string[]>([])
  const showImage = ref<boolean>(false)
  const cancelLoading = ref<boolean>(false)
  const confirmLoading = ref<boolean>(false)
  /* 地区列表 */
  const districtMap = ref<any>({})
  /* 请求分页参数 */
  const params = {
    pageSize: 999,
    pageNum: 1,
    handled: false,
  }
  /* 打开详情弹窗 */
  const openDetail = (row: any) => {
    currentUserInfo.value = row
    isAgent.value = row.isAgent
    isVendor.value = row.isVendor
    isCustomer.value = row.isCustomer
    subsidiaryIds.value = row.subsidiaryIds
    /* 筛选图片和pdf  */
    row.attachmentImages?.forEach((file: string) => {
      const parts = file.split('.')
      const ext = parts[parts.length - 1].toLowerCase()
      let temp = import.meta.env.VITE_BASE_URL + `/scm/oss/v2/download/deliveryDocuments?filename=${file}`
      if (ext === 'pdf') pdfs.value.push(temp)
      else images.value.push(temp)
    })
    visible.value = true
  }

  /* 获取地区 */
  const nsList = async () => {
    const { data } = await getNsList('CUSTOMLIST456')
    districtMap.value = (data || []).reduce((acc: any, val: any) => {
      acc[val.listValue] = val.listLabel
      return acc
    }, {} as any)
  }
  /* 获取子公司列表 */
  const getCompanyList = async () => {
    const res = await getCompany()
    if (res && res.code === 200) {
      subCompany.value = res.data
    }
  }
  /* 获取用户信息列表 */
  const getUserInfoList = async () => {
    try {
      lodaing.value = true
      const res = await userInfoList(params)
      userRegisterList.value = res.rows
      pagination.value.total = userRegisterList.value.length
      lodaing.value = false
    } catch (error) {
      lodaing.value = false
    }
  }

  const onClose = () => {
    visible.value = false
    isAgent.value = false
    isVendor.value = false
    isCustomer.value = false
    subsidiaryIds.value = []
    images.value = []
    pdfs.value = []
  }

  /* 同意申请 */
  const onConfirm = async () => {
    const data = {
      id: currentUserInfo.value.id,
      entityName: currentUserInfo.value.entityName,
      serialNo: currentUserInfo.value.serialNo,
      userId: currentUserInfo.value.userId,
      isAgent: isAgent.value,
      isVendor: isVendor.value,
      isCustomer: isCustomer.value,
      subsidiaryIds: subsidiaryIds.value,
    }

    try {
      confirmLoading.value = true
      await agreeUser(data)
      confirmLoading.value = false
      getUserInfoList()
      visible.value = false
      MessagePlugin.success($t('注册成功'))
    } catch (error) {
      confirmLoading.value = false
    }
  }
  /* 拒绝申请 */
  const dialogClose = async () => {
    try {
      cancelLoading.value = true
      await refuseUser(currentUserInfo.value.id)
      cancelLoading.value = false
      getUserInfoList()
      visible.value = false
      MessagePlugin.error($t('已拒绝'))
    } catch {
      cancelLoading.value = false
    }
  }

  /* 下载pdf */
  const previewPdf = async (url: any) => window.open(url, '_blank')

  onMounted(() => {
    getUserInfoList()
    getCompanyList()
    nsList()
  })

  return {
    userRegisterList,
    lodaing,
    columns,
    pagination,
    visible,
    openDetail,
    currentUserInfo,
    dialogClose,
    onClose,
    onConfirm,
    isAgent,
    isVendor,
    isCustomer,
    subsidiaryIds,
    subCompany,
    images,
    pdfs,
    showImage,
    previewPdf,
    confirmLoading,
    cancelLoading,
    districtMap,
  }
}

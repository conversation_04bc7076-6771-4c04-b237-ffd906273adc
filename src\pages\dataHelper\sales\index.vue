<script setup lang="ts">
  import useSalesDateHelper from './hooks'

  const { disableDate, slectVal, value, echartList, sumInfo } = useSalesDateHelper()

  const { t } = useI18n()
  const $t = t
</script>
<template>
  <div class="container">
    <div><span>查询年份：</span><t-date-picker mode="year" v-model="slectVal" :disable-date="disableDate" /></div>
    <t-card>
      <div class="card-body">
        <div class="header-content">
          <div>
            <div class="label">{{ $t('总成交量') }}({{ $t('吨') }})</div>
            <div class="value">
              {{ sumInfo.quantity || $t('加载中') }}
            </div>
          </div>
          <div>
            <div class="label">{{ $t('订单（件）') }}</div>
            <div class="value">
              {{ sumInfo.count || $t('加载中') }}
            </div>
          </div>
        </div>
        <div class="header-footer">
          <div class="label">{{ $t('总金额') }}</div>
          <div class="value">
            <div v-for="(a, i) in sumInfo.amount" :key="i">
              {{ a.amount || $t('加载中') }}
            </div>
          </div>
        </div>
      </div>
    </t-card>
    <div class="chart-wrap">
      <t-tabs theme="card" v-model="value">
        <t-tab-panel v-for="(item, index) in echartList" :key="index" :value="index" :label="item.title">
          <component :is="item.cmp" />
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>
<style scoped lang="less">
  .echart-content {
    display: flex;
    column-gap: 36px;
    .chart-wrap {
      flex: 1;
      text-align: center;
      .t-radio-group {
        height: 40px;
        background: #54c7b2;
      }
    }
  }
  .t-card {
    width: fit-content;
    margin: 20px 0;
    .card-body {
      display: flex;
      column-gap: 26px;
      .header-content {
        display: flex;
        flex-direction: column;
        row-gap: 16px;
      }

      .label {
        margin-bottom: 6px;
      }
      .value {
        font-size: 18px;
        line-height: 24px;
        color: #666;
        letter-spacing: 0;
        font-weight: 500;
      }
    }
  }
</style>

{"name": "hnr-scm-pc", "version": "0.6.1", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode development", "dev:linux": "vite --mode development", "build:test": "vite build --mode test", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,vss,sass,less}", "site:preview": "npm run build && cp -r dist _site", "test": "vitest"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/g2": "^5.3.3", "@univerjs/preset-docs-core": "^0.10.2", "@univerjs/preset-sheets-core": "^0.10.2", "@univerjs/presets": "^0.10.2", "@univerjs/sheets-formula": "^0.10.4", "@vueuse/core": "^13.0.0", "@wecom/jssdk": "^2.2.6", "axios": "^1.8.4", "dayjs": "^1.11.13", "echarts": "~5.6.0", "excel-formula-ast": "^1.1.1", "excel-formula-tokenizer": "^3.0.0", "js-base64": "^3.7.7", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "pinyin-pro": "^3.27.0", "tdesign-icons-vue-next": "^0.3.5", "tdesign-vue-next": "^1.13.2", "tvision-color": "^1.6.0", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-i18n": "^11.1.2", "vue-print-next": "^1.0.9", "vue-request": "^2.0.4", "vue-router": "~4.5.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/lodash-es": "^4.17.12", "@types/qs": "^6.9.18", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/ui": "3.1.2", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "axios-mock-adapter": "^2.1.0", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.23.0", "happy-dom": "^17.4.4", "less": "^4.2.2", "lint-staged": "^12.5.0", "mockjs": "^1.1.0", "prettier": "^2.8.8", "stylelint": "~13.13.1", "stylelint-config-prettier": "~9.0.5", "stylelint-less": "1.0.1", "stylelint-order": "~4.1.0", "typescript": "~5.8.2", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.4", "vite-plugin-mock": "^3.0.2", "vite-plugin-vue-devtools": "^7.7.2", "vite-svg-loader": "^5.1.0", "vitest": "^3.1.2", "vue-tsc": "^2.2.8"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "npm run lint:fix", "git add ."], "*.{html,vue,vss,sass,less}": ["npm run stylelint:fix", "git add ."]}, "description": "Base on tdesign-starter-cli"}
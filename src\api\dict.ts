import { AxiosPromise } from 'axios'
import request from '@/utils/request'

// 根据字典类型查询字典数据信息
export function getDicts(dictType: string): AxiosPromise<DictDataVO[]> {
  return request({
    url: `/admin/system/dict/data/type/${dictType}`,
    method: 'get',
  })
}

export function getNsList(dictType: string): AxiosPromise<NsListDataVO[]> {
  return request({
    url: `/admin/ns/list/data/byType/${dictType}`,
    method: 'get',
  })
}

export interface DictDataVO extends BaseEntity {
  dictCode: string
  dictLabel: string
  dictValue: string
  cssClass: string
  listClass: any
  dictSort: number
  status: string
  remark: string
}

export interface NsListDataVO {
  listCode: string
  listLabel: string
  listValue: string
  listType: string
  isDefault: string
  remark: string
}

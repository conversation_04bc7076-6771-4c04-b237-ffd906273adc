import Layout from '@/layouts/index.vue'
import { ChatDoubleIcon } from 'tdesign-icons-vue-next'

export default [
  {
    path: '/inquiryQuote',
    name: 'inquiryQuote',
    component: Layout,
    redirect: '/inquiryQuote/rawPurch',
    meta: { title: '询报价', icon: 'chat-bubble-1', orderNo: 1 },
    children: [
      {
        path: 'rawPurch',
        name: 'InquiryQuoteRawPurch',
        component: () => import('@/pages/inquiryQuote/rawPurch/index.vue'),
        meta: { title: '原料采购' },
      },
      {
        path: 'acquisition',
        name: 'InquiryQuoteAcquisition',
        component: () => import('@/pages/inquiryQuote/acquisition/index.vue'),
        meta: { title: '原料收购' },
      },
    ],
  },
]

import type { EChartsType } from 'echarts'

export const DefaultQuotationResult = [
  {
    bidrate: '0',
    times: '0',
    time: '2022',
  },
  {
    bidrate: '0',
    times: '0',
    time: '2021',
  },
]

export type QuoteState = {
  lineEchart?: EChartsType
}

export interface QuoteType {
  time: string
  times: string
  bidrate: string
}

export const enquiryQuotationConfig = (quotes: QuoteType[]) => {
  return {
    grid: {
      top: 40,
      bottom: 70,
      left: 50,
      right: 40,
    },
    dataZoom: [
      {
        type: 'inside',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: Math.floor((4 / quotes.length) * 100),
        bottom: '10',
        zoomLock: true,
      },
    ],
    tooltip: {
      hideDelay: 100,
      trigger: 'axis',
      axisPointer: { type: 'cross', crossStyle: { color: '#999' } },
      formatter: ([
        { seriesName: timesName, value: timeValue },
        { seriesName: bidrateTime, value: bidrateValue },
      ]: any) => {
        return `${timesName}:${timeValue}<br>${bidrateTime}:${bidrateValue}%`
      },
      position: ['40%', '30%'],
    },
    legend: { data: ['报价次数', '中标率'], bottom: 20 },
    xAxis: [
      {
        type: 'category',
        data: quotes.map((i) => i.time),
        axisPointer: { type: 'shadow' },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '次数',
        minInterval: 1,
        axisLabel: {
          formatter: '{value}',
          width: 45,
          margin: 5,
          overflow: 'breakAll',
        },
        nameTextStyle: {
          color: '#999',
          align: 'left',
        },
        axisLine: {
          lineStyle: {
            width: 1,
            color: '#999',
            type: 'dotted',
          },
        },
      },
      {
        type: 'value',
        name: '百分比',
        minInterval: 1,
        axisLabel: {
          formatter: '{value}%',
          width: 35,
          margin: 5,
          overflow: 'breakAll',
        },
        nameTextStyle: {
          color: '#aaa',
          align: 'right',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '报价次数',
        type: 'bar',
        data: quotes.map((i) => i.times),
        barWidth: '30',
      },
      {
        name: '中标率',
        type: 'line',
        yAxisIndex: 1,
        data: quotes.map((i) => i.bidrate),
      },
    ],
  }
}

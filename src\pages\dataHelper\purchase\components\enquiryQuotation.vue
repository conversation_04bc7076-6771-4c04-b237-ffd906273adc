<script lang="ts" setup>
  import { init } from 'echarts'
  import { ref, watchEffect } from 'vue'
  import Empty from '@/components/Empty/index.vue'
  // import HonoroadSelect from '../../../components/HonoroadSelect.vue'
  import { DefaultQuotationResult, enquiryQuotationConfig, QuoteState, QuoteType } from '../utils/enquiryConfig'
  import { useYearDataStore } from '../hooks/dataYearStore'
  import timeEffect from '../effect/timeEffect'
  import productEffect from '../effect/productEffect'
  import { useI18n } from 'vue-i18n'
  import { purchContractChart } from '@/api/data/vendor'
  import { useUserStore } from '@/store'

  const { t } = useI18n()
  const $t = t

  const { slectVal } = storeToRefs(useYearDataStore())
  const quoteElement = ref<HTMLDivElement>()

  const globalStore = useUserStore()

  const { timeType, TimeTypeSelect, showTimeTypeSelect } = timeEffect($t)
  const { productType, ProductSelect } = productEffect()
  const charState: QuoteState = {}
  const loading = ref<boolean>(false)

  const quoteResultEmpty = ref(false)
  const fetchQuoteData = async () => {
    if (!globalStore.vendorIds?.length) return
    loading.value = true
    quoteResultEmpty.value = false

    const { data: result } = await purchContractChart<QuoteType[]>('quotationtimeslinechart', {
      vendorIdList: globalStore.vendorIds,
      timeType: timeType.value,
      year: slectVal.value,
      product: productType.value,
    })

    if (!result?.length) {
      quoteResultEmpty.value = true
      loading.value = false
      charState.lineEchart?.dispose()
      charState.lineEchart = undefined
      return
    }
    const quoteRes = result.length > 0 ? result : DefaultQuotationResult
    quoteEchartHandler(
      slectVal.value !== 'all'
        ? quoteRes.map((i) => ({
            ...i,
            time: i.time.split('-')[1],
          }))
        : quoteRes,
    )
    loading.value = false
  }

  const quoteEchartHandler = (quoteRes?: QuoteType[]) => {
    if (quoteElement.value && quoteRes) {
      const unexchangedChart = charState.lineEchart ? charState.lineEchart : init(quoteElement.value)
      charState.lineEchart = unexchangedChart
      unexchangedChart.setOption(enquiryQuotationConfig(quoteRes))
    }
  }

  watchEffect(() => {
    if (timeType.value || productType.value || slectVal.value) {
      fetchQuoteData()
    }
  })
</script>
<template>
  <!-- <div class="echart-title">{{ $t('报价查询') }}</div> -->
  <div class="select-list">
    <div class="select-container">
      <div class="label">{{ $t('产品') }}</div>
      <t-select v-model="productType" :options="ProductSelect" />
    </div>
    <div v-if="showTimeTypeSelect" class="select-container">
      <div class="label">
        {{ $t('展示类型') }}
      </div>
      <t-select v-model="timeType" :options="TimeTypeSelect" />
    </div>
  </div>

  <t-loading size="small" :loading="loading">
    <div v-if="!quoteResultEmpty" class="echart-line-container" ref="quoteElement"></div>
    <Empty v-else :description="$t('暂无数据')" />
  </t-loading>
</template>
<style lang="less" scoped>
  @import '../style/common.less';
</style>

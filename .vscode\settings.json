{"eslint.format.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "[vue]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "cSpell.words": ["tdesign", "tvision", "echarts", "nprogress", "commitlint", "stylelint", "pinia", "qrcode"], "i18n-ally.localesPaths": ["src/i18n", "src/i18n/lang"]}
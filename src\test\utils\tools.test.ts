import { describe, it, expect } from 'vitest'
import { formatNumber, parseNumber, divideScaleStr } from '@/utils/tools'

describe('formatNumber', () => {
  it('formatNumber', async () => {
    expect(formatNumber('1,000,0000')).toBe('1.00')
  })
  it('parseNumber', async () => {
    expect(parseNumber('10000000')).toBe(10000000)
  })
  it('divideScaleStr', async () => {
    expect(divideScaleStr(9, 3, 2)).toBe('3.00')
  })
})

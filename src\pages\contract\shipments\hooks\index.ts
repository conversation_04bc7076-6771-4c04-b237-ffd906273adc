import { TableProps } from 'tdesign-vue-next'
import { useRouter } from 'vue-router'
import { purchSaleContractDetailTmsApi } from '@/api/contract'
import { useDictLabelRaw } from '@/use/dict'
import { useRoute } from 'vue-router'
import { formatNumber } from '@/utils/tools'

export default () => {
  const { t } = useI18n()
  const $t = t
  const router = useRouter()
  const route = useRoute()

  /* active */
  const btnActive = ref<number>(0)
  /* 运单状态 */
  const waybillStatus = reactive([
    { id: 0, title: $t('待发货'), statusList: [1, 2, 3, 5, 6] },
    { id: 1, title: $t('在途'), statusList: [7, 9] },
    { id: 2, title: $t('已收货'), statusList: [8, 10] },
  ])
  /* 请求参数 */
  const params = ref({
    pageNum: 1,
    pageSize: 9999,
    orderNo: route.params?.id,
    statusList: waybillStatus[0].statusList,
  })
  /* form表单 */
  const DEFAULT_FORM = {
    searchValue: '',
    vehicleNo: '',
    ladingBillNo: '',
    consigneeAddress: '',
    contractNo: '',
  }
  const searchForm = ref({ ...DEFAULT_FORM })
  /* 运单列表 */
  const waybillList = ref<any>([])
  /* loading */
  const loading = ref<boolean>(false)

  /* 当前节点 */
  const currentNode = ref<any>({})

  /* 是否展示列配置 */
  const columnControllerVisible = ref<boolean>(false)
  /* 表格配置 */
  const columns = ref<any>([
    { colKey: 'materialName', title: $t('产品'), ellipsis: true, width: 150, align: 'center' },
    {
      colKey: 'dispatchStatus',
      title: $t('当前节点'),
      ellipsis: true,
      width: 150,
      align: 'center',
      cell: (h, { col, row }) => currentNode.value?.[row[col.colKey]],
    },
    {
      colKey: 'materialNum',
      title: $t('数量') + '(t)',
      ellipsis: true,
      width: 150,
      align: 'center',
      cell: (h, { col, row }) =>
        row[col.colKey]
          .split('/')
          .map((it: any) => formatNumber(it / 1000))
          .join('/') + ' t',
    },
    { colKey: 'customer', title: $t('收货人'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'cargoOwnerName', title: $t('发货人'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'ladingBillNo', title: $t('运单号'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'vehicleNo', title: $t('车船号'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'deliveryTime', title: $t('起运日期'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'arriveTime', title: $t('预计到货日期'), ellipsis: true, width: 150, align: 'center' },
    {
      colKey: 'nowPosition',
      title: $t('当前位置'),
      ellipsis: true,
      width: 150,
      align: 'center',
      cell: (h, { col, row }) => row[col.colKey] || $t('未知'),
    },
    { colKey: 'link', title: $t('操作'), fixed: 'right', ellipsis: true, width: 120, align: 'center' },
  ])
  /* 列配置 */
  const columnControllerConfig = ref<TableProps['columnController']>({
    dialogProps: { preventScrollThrough: true },
    fields: [
      'materialName',
      'dispatchStatus',
      'materialNum',
      'customer',
      'cargoOwnerName',
      'ladingBillNo',
      'vehicleNo',
      'deliveryTime',
      'arriveTime',
      'nowPosition',
    ],
    hideTriggerButton: true,
  })
  /* 表格配置 */
  const displayColumns = ref<TableProps['displayColumns']>([
    'materialName',
    'dispatchStatus',
    'materialNum',
    'customer',
    'cargoOwnerName',
    'ladingBillNo',
    'vehicleNo',
    'deliveryTime',
    'arriveTime',
    'nowPosition',
    'link',
  ])
  /* 分页配置 */
  const pagination = ref<TableProps['pagination']>({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  /* 获取运单列表 */
  const getWaybillList = async () => {
    try {
      loading.value = true
      const res = await purchSaleContractDetailTmsApi({ ...params.value, ...searchForm.value })
      waybillList.value = res.rows
      pagination.value.total = res.total
    } finally {
      loading.value = false
    }
  }
  /* 字典查询方式 */
  const getLabel = async () => {
    const res = await useDictLabelRaw('transport_dispatch_status')
    currentNode.value = res.transport_dispatch_status
  }

  /* 切换订单状态 */
  const changeStatus = async (id: number) => {
    btnActive.value = id
    params.value.statusList = waybillStatus[id].statusList
    await getWaybillList()
  }
  /* 刷新表格 */
  const refresh = async () => await getWaybillList()
  /* 运单详情 */
  const waybillDetail = (row: any) => {
    router.push({
      path: '/contract/shipments-details',
      state: {
        info: JSON.stringify(row),
      },
    })
  }

  /* 搜索订单 */
  const searchOrder = async () => await getWaybillList()
  /* 表单重置 */
  const onReset = async () => {
    searchForm.value = { ...DEFAULT_FORM }
    await getWaybillList()
  }

  onMounted(async () => {
    if (route.query.active) {
      btnActive.value = Number(route.query.active)
      params.value.statusList = waybillStatus[btnActive.value].statusList
    }
    await getLabel()
    await getWaybillList()
  })

  return {
    btnActive,
    waybillStatus,
    waybillList,
    changeStatus,
    displayColumns,
    columnControllerVisible,
    columnControllerConfig,
    pagination,
    columns,
    loading,
    currentNode,
    refresh,
    waybillDetail,
    searchForm,
    searchOrder,
    onReset,
  }
}

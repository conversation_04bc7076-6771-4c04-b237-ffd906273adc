{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "bundler", "jsx": "preserve", "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "useDefineForClassFields": true, "lib": ["esnext", "dom"], "types": ["vite/client"], "noEmit": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["**/*.ts", "src/**/*.d.ts", "src/types/**/*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "node_modules/tdesign-vue-next/global.d.ts"], "compileOnSave": false}
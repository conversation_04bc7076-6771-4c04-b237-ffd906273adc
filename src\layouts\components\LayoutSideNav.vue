<template>
  <l-side-nav
    v-if="settingStore.showSidebar"
    :show-logo="settingStore.showSidebarLogo"
    :layout="settingStore.layout"
    :is-fixed="settingStore.isSidebarFixed"
    :menu="sideMenu"
    :theme="settingStore.displayMode"
    :is-compact="settingStore.isSidebarCompact"
  />
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { useRoute } from 'vue-router'
  import { storeToRefs } from 'pinia'
  import { useSettingStore, useUserStore } from '@/store'
  import LSideNav from './SideNav.vue'

  const route = useRoute()
  const userStore = useUserStore()
  const settingStore = useSettingStore()
  const { routers: menuRouters } = storeToRefs(userStore)

  const sideMenu: any = computed(() => {
    const { layout, splitMenu } = settingStore
    let newMenuRouters = menuRouters.value
    if (layout === 'mix' && splitMenu) {
      newMenuRouters.forEach((menu) => {
        if (route.path.indexOf(menu.path) === 0) {
          newMenuRouters = menu.children.map((subMenu) => ({ ...subMenu, path: `${menu.path}/${subMenu.path}` }))
        }
      })
    }
    return newMenuRouters
  })
</script>

import { createApp } from 'vue'
import 'tdesign-vue-next/es/style/index.css'
import { vueI18n } from './i18n'
import { store } from './store'
import router from './router'
import '@/style/index.less'
import { printPlugin } from 'vue-print-next'
import './permission'
import App from './App.vue'

import '@univerjs/preset-sheets-core/lib/index.css'
import '@univerjs/preset-docs-core/lib/index.css'

const app = createApp(App)

app.use(printPlugin)

app.use(router)
app.use(store)
app.use(vueI18n)

app.mount('#app')

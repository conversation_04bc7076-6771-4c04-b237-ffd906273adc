type BaseType = symbol | string | number | null | unknown | boolean

interface DataObj {
  [key: unknown]: BaseType | Array<BaseType> | Array<Record<string, BaseType>>
}

export interface MenuRoute {
  path: string
  title?: string
  name?: string
  icon?:
    | string
    | {
        render: () => void
      }
  redirect?: string
  children: MenuRoute[]
  meta: any
}

export type ModeType = 'dark' | 'light'

export interface TRouterInfo {
  path: string
  query?: LocationQueryRaw
  routeIdx?: number
  title?: string
  name?: RouteRecordName
  isAlive?: boolean
  isHome?: boolean
  meta?: any
}

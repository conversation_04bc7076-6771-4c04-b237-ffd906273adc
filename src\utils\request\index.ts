/* eslint-disable camelcase */
import axios, {
  type AxiosError,
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
  type InternalAxiosRequestConfig,
} from 'axios'
import cache from '@/plugins/cache'
import { getToken } from '@/utils/auth'
import type { DataObj } from '@/types/interface'

const baseURL = import.meta.env.VITE_APP_BASE_API
// 创建axios实例
const service: AxiosInstance = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL,
  // 超时
  timeout: 60000,
})
// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 是否需要设置 token
    // const noToken = (config.headers || {}).noToken === true;
    // const noToken = config.headers?.noToken ?? true;
    // 是否需要防止数据重复提交
    const isRepeatSubmit = config.headers?.repeatSubmit === false
    if (getToken()) {
      // 通常不需要传递token
      config.headers.Authorization = `Bearer ${getToken()}` // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = `${config.url}?${tansParams(config.params)}`
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime(),
      }
      const requestSize = Object.keys(JSON.stringify(requestObj)).length // 请求数据大小
      const limitSize = 5 * 1024 * 1024 // 限制存放数据5M
      if (requestSize >= limitSize) {
        return config
      }
      const sessionObj = cache.session.getJSON('sessionObj')
      if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
        cache.session.setJSON('sessionObj', requestObj)
      } else {
        const s_url = sessionObj.url // 请求地址
        const s_data = sessionObj.data // 请求数据
        const s_time = sessionObj.time // 请求时间
        const interval = 1000 // 间隔时间(ms)，小于此时间视为重复提交
        if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
          const message = '数据正在处理，请勿重复提交'
          console.warn(`[${s_url}]: ${message}`)
          return Promise.reject(new Error(message))
        }
        cache.session.setJSON('sessionObj', requestObj)
      }
    }
    return config
  },
  (error: AxiosError) => {
    // 请求错误处理
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const result = response.data
    if (result.code === 401) {
      return Promise.reject(result.msg || 'Error')
    }
    return result
  },
  (error: AxiosError) => {
    console.log(error.message)
    if (error.message.includes('500')) MessagePlugin.error('服务器出错')
    return Promise.reject(error)
  },
)

export const get = <T = any, D = DataObj>(url: string, params?: D): Promise<AxiosResponse<T>> =>
  service.get(url, { params })

export const post = <T = any, D = DataObj>(
  url: string,
  data?: D,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<T>> => service.post(url, data, config)
export const remove = <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
  service.delete(url, config)
export const put = <T = any, D = DataObj>(
  url: string,
  data?: D,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<T>> => service.put(url, data, config)

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params: Record<string, any>) {
  let result = ''
  if (!params) return result
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    const part = `${encodeURIComponent(propName)}=`
    if (value !== null && value !== '' && typeof value !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== '' && typeof value[key] !== 'undefined') {
            const params = `${propName}[${key}]`
            const subPart = `${encodeURIComponent(params)}=`
            result += `${subPart + encodeURIComponent(value[key])}&`
          }
        }
      } else {
        result += `${part + encodeURIComponent(value)}&`
      }
    }
  }
  return result
}

export default service

/**
 * 进页面第一时间调用getInfo，通过token获取用户信息和权限
 * 如果没有token或者为401时，跳转到登录页
 */

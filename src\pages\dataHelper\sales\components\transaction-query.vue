<script setup lang="ts">
  import { tradingByProduct, tradingPriceTrend } from '@/api/data/customer'
  import { useUserStore } from '@/store'
  import { useYearDataStore } from '../../purchase/hooks/dataYearStore'
  import { type EChartsType, init } from 'echarts'
  import { priceMixEchartConfig, productPieEchartConfig } from '../config'
  import Empty from '@/components/Empty/index.vue'
  const { t } = useI18n()
  const $t = t

  const { slectVal, yearStart, yearEnd } = storeToRefs(useYearDataStore())

  const chartState: Record<string, Nullable<EChartsType>> = {
    productPieChart: null,
    priceMixChart: null,
  }

  const pieLoading = ref(true)
  const mixLoading = ref(true)
  const pieEmpty = ref(false)
  const mixEmpty = ref(false)
  const pieData = ref<any>()

  const mixItemDataMap = ref<Record<string, any[]>>({})
  const mixItemFilter = ref<any>()

  const mixItemFilterOptions = ref<any[]>([])

  const productPieChartRef = useTemplateRef<HTMLDivElement>('productPieChart')
  const priceMixChartRef = useTemplateRef<HTMLDivElement>('priceMixChart')

  const globalStore = useUserStore()
  const loadPieData = async () => {
    if (!globalStore.customerIds?.length) return
    const queryParams: any = {
      startDate: yearStart.value,
      endDate: yearEnd.value,
      customerIdList: globalStore.customerIds,
    }
    pieLoading.value = true
    pieEmpty.value = false
    try {
      const res = await tradingByProduct(queryParams)
      if (!(res.data && res.data.length)) {
        pieEmpty.value = true
        return
      }
      const itemPriceData = res.data.map((d: any) => {
        return {
          name: d.itemName,
          value: Number(d.quantity),
        }
      })
      pieData.value = itemPriceData
    } finally {
      pieLoading.value = false
    }
  }

  const loadMixData = async () => {
    if (!globalStore.customerIds?.length) return
    const queryParams: any = {
      startDate: yearStart.value,
      endDate: yearEnd.value,
      customerIdList: globalStore.customerIds,
    }
    mixLoading.value = true
    mixEmpty.value = false
    try {
      const res = await tradingPriceTrend(queryParams)
      const itemData = res.data
      if (!itemData) {
        mixEmpty.value = true
        return
      }
      const itemOptions = (itemData.itemDataRecords || []).map((d: any) => ({
        value: d.itemId,
        label: d.itemName,
      }))
      mixItemFilterOptions.value = itemOptions
      mixItemDataMap.value = (itemData.itemDataRecords || []).reduce((acc: any, d: any) => {
        acc[d.itemId] = d.records
        return acc
      }, {} as any)
      if (!(itemOptions && itemOptions.length)) {
        mixEmpty.value = true
        return
      } else {
        mixItemFilter.value = itemOptions[0].value
      }
    } finally {
      mixLoading.value = false
    }
  }

  const createProductPieChart = (data: any[]) => {
    let chart = chartState.productPieChart
    if (!chart) {
      chart = init(productPieChartRef.value)
      chartState.productPieChart = chart
    }
    chart.setOption(productPieEchartConfig(data, $t))
  }

  watchEffect(() => {
    if (pieData.value) createProductPieChart(pieData.value)
    else if (chartState.productPieChart) {
      chartState.productPieChart.dispose()
      chartState.productPieChart = null
    }
  })

  const createPriceMixChart = (data: any[]) => {
    let chart = chartState.priceMixChart
    if (!chart) {
      chart = init(priceMixChartRef.value)
      chartState.priceMixChart = chart
    }
    chart.setOption(priceMixEchartConfig(data, $t))
  }

  watchEffect(() => {
    if (mixItemDataMap.value && mixItemFilter.value) createPriceMixChart(mixItemDataMap.value[mixItemFilter.value])
    else if (chartState.priceMixChart) {
      chartState.priceMixChart.dispose()
      chartState.priceMixChart = null
    }
  })

  const loadData = async () => {
    await nextTick()
    loadPieData()
    loadMixData()
  }
  onMounted(() => loadData())
  watch(
    () => slectVal.value,
    () => loadData(),
  )
</script>

<template>
  <div class="page-container">
    <div class="chart-card">
      <div class="chart-title">
        <div class="title-content">{{ $t('产品结构') }}</div>
      </div>
      <t-loading size="small" :loading="pieLoading">
        <div class="chart-content">
          <Empty v-if="pieEmpty" :description="$t('暂无数据')" />
          <div v-else ref="productPieChart" class="echart-pie-container" />
        </div>
      </t-loading>
    </div>
    <div class="chart-card">
      <div class="chart-title">
        <div class="title-content">
          <div class="title-text">{{ $t('价格走势') }}</div>
          <div class="title-item-selector" v-if="mixItemFilterOptions.length">
            <div class="label">{{ $t('产品') }}</div>
            <t-select v-model="mixItemFilter" :options="mixItemFilterOptions" />
          </div>
        </div>
      </div>
      <t-loading size="small" :loading="mixLoading">
        <div class="chart-content">
          <Empty v-if="mixEmpty" :description="$t('暂无数据')" />
          <div v-else ref="priceMixChart" class="echart-line-container" />
        </div>
      </t-loading>
    </div>
  </div>
</template>

<style scoped lang="less">
  @import url(../styles/common.less);

  .title-content {
    display: flex;
    align-items: center;
    width: 100%;
    .title-text {
      width: 10em;
      margin-bottom: 16px;
    }
    .title-item-selector {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
  }
  .label {
    width: 60px;
    font-size: 16px;
  }
</style>

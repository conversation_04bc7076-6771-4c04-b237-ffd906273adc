<script setup lang="ts">
  import { init, type EChartsType } from 'echarts'
  import { useYearDataStore } from '../../purchase/hooks/dataYearStore'
  import { useUserStore } from '@/store'
  import { quoteQuery } from '@/api/data/customer'
  import { max, min } from 'lodash-es'
  import { quoteBarChartConfig } from '../config'
  import Empty from '@/components/Empty/index.vue'
  const { t } = useI18n()
  const $t = t

  const chartState: Record<string, Nullable<EChartsType>> = {
    quoteChart: null,
  }
  const { slectVal, yearStart, yearEnd } = storeToRefs(useYearDataStore())
  const globalStore = useUserStore()
  const quoteQueryChartRef = useTemplateRef<HTMLDivElement>('quoteQueryChart')

  const itemFilterOptions = ref<any[]>([])
  const loading = ref(false)
  const isEmpty = ref(false)
  const pageData = ref<any>()

  const loadData = async () => {
    await nextTick()
    if (!globalStore.customerIds?.length) return
    isEmpty.value = false
    loading.value = true
    const queryParams: any = {
      startDate: yearStart.value,
      endDate: yearEnd.value,
      customerIdList: globalStore.customerIds,
    }
    const res = await quoteQuery(queryParams)
    const reportData = res.data
    if (!reportData || !reportData.length) {
      isEmpty.value = true
      loading.value = false
      return
    }
    const itemNameMap: Record<string, string> = {}
    const itemDataMap: Record<string, { all: number; in: number; radio: string }> = {}
    reportData.forEach((d: any) => {
      const itemData = itemDataMap[d.itemId]
      itemNameMap[d.itemId] = d.itemName
      if (itemData) {
        itemData.all += d.totalCount
        itemData.in += d.selectedCount
        if (itemData.all <= 0) {
          itemData.radio = '0.00'
        } else {
          itemData.radio = ((itemData.in / itemData.all) * 100).toFixed(2)
        }
      } else {
        itemDataMap[d.itemId] = {
          all: d.totalCount,
          in: d.selectedCount,
          radio: d.totalCount > 0 ? ((d.selectedCount / d.totalCount) * 100).toFixed(2) : '0.00',
        }
      }
    })
    const categories = Object.keys(itemDataMap).map((k) => itemNameMap[k])
    itemFilterOptions.value = Object.keys(itemDataMap).map((k) => ({
      value: k,
      text: itemNameMap[k],
    }))
    const lineData = Object.keys(itemDataMap).map((k) => Number(itemDataMap[k].radio))
    const totalCountData = Object.keys(itemDataMap).map((k) => itemDataMap[k].all)
    const selectedCountData = Object.keys(itemDataMap).map((k) => itemDataMap[k].in)
    const series: any[] = [totalCountData, selectedCountData, lineData]
    pageData.value = {
      categories,
      series,
    }
    loading.value = false
  }

  const createQuoteBarChart = (categories: string[], series: number[][]) => {
    const lineDataMax = (max(series[2]) as number) * 1.8
    const barDataMax = max(series[0]) as number
    let chart = chartState.quoteChart
    if (!chart) {
      if (!quoteQueryChartRef.value) return
      chart = init(quoteQueryChartRef.value)
      chartState.executedChartRef = chart
    }
    chart.setOption(
      quoteBarChartConfig(categories, series, parseInt(String(barDataMax * 1.2)), min([lineDataMax, 100]), $t),
    )
  }

  watchEffect(() => {
    if (pageData.value) createQuoteBarChart(pageData.value.categories, pageData.value.series)
    else if (chartState.quoteChart) {
      chartState.quoteChart.dispose()
      chartState.quoteChart = null
    }
  })

  onMounted(() => {
    loadData()
  })

  watch(
    () => slectVal.value,
    () => loadData(),
  )
</script>

<template>
  <div class="page-container">
    <div class="chart-card">
      <div class="chart-title">
        <div class="title-icon">
          <!-- <Icon icon="mdi-light:chart-bar" width="1.2em" height="1.2em" style="color: #006c5d" /> -->
        </div>
        <div class="title-content">{{ $t('询报价统计') }}</div>
      </div>
      <t-loading size="small" :loading="loading">
        <div class="chart-content">
          <div v-if="pageData" ref="quoteQueryChart" class="echart-line-container"></div>
          <Empty v-else :description="$t('暂无数据')" />
        </div>
      </t-loading>
    </div>
  </div>
</template>

<style scoped lang="less">
  @import url(../styles/common.less);
</style>

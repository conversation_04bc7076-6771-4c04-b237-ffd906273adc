import type { UploadFile, RequestMethodResponse } from 'tdesign-vue-next'
import { useDict } from '@/use/dict'
import {
  orderInfoApi,
  logisticsTrack,
  logisticsInfoApi,
  orderFileListApi,
  deleteOrderFileApi,
  uploadOrderFileApi,
} from '@/api/contract'
import AMapLoader from '@amap/amap-jsapi-loader'
import node from '@/assets/node.png'
import daozhan from '@/assets/daozhan.png'
import fazhan from '@/assets/fazhan.png'
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'

declare global {
  interface Window {
    _AMapSecurityConfig: {
      securityJsCode: string
    }
  }
}

export default () => {
  const { t } = useI18n()
  const $t = t
  /* loading */
  const waybillDetailloading = ref<boolean>(false)
  /* 地图 */
  const map = ref<any>(null)
  /* 交单弹窗 */
  const orderVisible = ref<boolean>(false)
  /* 相信信息弹窗 */
  const detailVisible = ref<boolean>(false)
  /* 文件列表 */
  const fileList = ref<any>([])
  const fileLoading = ref<boolean>(false)
  const filePagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })
  /* 文件列表配置 */
  const fileColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'orderNo', title: $t('采购单号'), ellipsis: true, width: 150 },
    { colKey: 'filename', title: $t('文件名称'), ellipsis: true, width: 150 },
    { colKey: 'updateTime', title: $t('上传时间'), ellipsis: true, width: 160 },
    { colKey: 'link', title: $t('操作'), ellipsis: true, width: 180 },
  ])
  const files = ref<any>([])
  /* 运单信息 */
  const contractOrderInfo = ref<any>({})
  /* 运综信息 */
  const expressageInfo = ref<any>({})
  /* 运单信息 */
  const waybillInfo = ref<any>(JSON.parse(history.state?.info))
  /* 物流信息 */
  const logisticsInfo = ref<any>({})
  const canUseTrackInfo = computed(() => waybillInfo.value.trackPlatform && waybillInfo.value.ladingBillNo)

  /* 铁运 */
  const railwayExternalSteps = [
    { id: 0, title: '已到达口岸站', steps: 10 },
    { id: 1, title: '已受理', steps: 11 },
    { id: 2, title: '已装车', steps: 12 },
    { id: 3, title: '已制票', steps: 13 },
    { id: 4, title: '已发车', steps: 14 },
    { id: 5, title: '已到达', steps: 15 },
  ]
  const railwayInternalSteps = [
    { id: 0, title: '提出需求', steps: 10 },
    { id: 1, title: '已制单', steps: 35 },
    { id: 2, title: '已发车', steps: 40 },
    { id: 3, title: '已办交付手续', steps: 75 },
    { id: 4, title: '货物已交付', steps: 80 },
  ]
  const { ec95306_glzm } = toRefs(useDict('ec95306_glzm'))

  const railwayTransport = computed(() =>
    ec95306_glzm.value.some((d) => d.remark && waybillInfo.value?.ladingBillNo?.startsWith(d.remark))
      ? railwayExternalSteps
      : railwayInternalSteps,
  )
  /* 汽运 */
  const automobileTransport = [
    { id: 0, title: '生成运输任务', steps: 100 },
    { id: 1, title: '司机接单', steps: 400 },
    { id: 2, title: '装货到厂', steps: 500 },
    { id: 3, title: '上传装货磅单', steps: 510 },
    { id: 4, title: '发车', steps: 600 },
    { id: 5, title: '到达卸货地', steps: 650 },
    { id: 6, title: '运输完成', steps: 700 },
    { id: 7, title: '上传卸货榜单', steps: 800 },
    { id: 8, title: '申请支付', steps: 900 },
    { id: 9, title: '支付完成', steps: 1200 },
  ]
  /* 步骤条active */
  const railFreight = computed(() => {
    const ztgjValue = logisticsInfo.value?.trackItem?.ztgj || 0
    // 找到最后一个 steps <= ztgjValue 的索引
    const lastValidIndex =
      railwayTransport.value
        .map((item, index) => ({ ...item, index }))
        .filter((item) => item.steps <= ztgjValue)
        .pop()?.index ?? 0

    return lastValidIndex
  })
  const roadTransport = computed(() => {
    const ztgjValue = logisticsInfo.value?.trackItem?.ztgj || 0
    // 找到最后一个 steps <= ztgjValue 的索引
    const lastValidIndex =
      automobileTransport
        .map((item, index) => ({ ...item, index }))
        .filter((item) => item.steps <= ztgjValue)
        .pop()?.index ?? 0

    return lastValidIndex
  })
  /* 获取运单信息 */
  const getWaybillDetail = async () => {
    try {
      const { data } = await orderInfoApi(waybillInfo.value.orderId)
      contractOrderInfo.value = data
      const platform = waybillInfo.value.trackPlatform
      const hph = waybillInfo.value.ladingBillNo
      if (platform && hph) {
        waybillDetailloading.value = true
        const res = await logisticsInfoApi({ platform, hph })
        const { data } = await logisticsTrack(waybillInfo.value.dispatchId)
        expressageInfo.value = res.data
        logisticsInfo.value = data
      }
    } finally {
      waybillDetailloading.value = false
    }
  }
  /* 获取交单列表 */
  const orderFileList = async () => {
    fileLoading.value = true
    try {
      const { data } = await orderFileListApi({
        nsOrderId: contractOrderInfo.value.nsOrderId,
        orderNo: contractOrderInfo.value.orderNo,
      })
      fileList.value = data
      filePagination.value.total = fileList.value.length
    } finally {
      fileLoading.value = false
    }
  }
  /* 打开交单弹窗 */
  const submitDocuments = async () => {
    orderVisible.value = true
    await orderFileList()
  }

  /* 下载交单文件 */
  const donloadFile = (id: number) => {
    const fileUrl = import.meta.env.VITE_APP_BASE_API + `/tms/order/file/download/${id}`
    location.assign(fileUrl)
  }

  /* 删除交单文件*/
  const deleteFile = async (id: number) => {
    try {
      await deleteOrderFileApi(id)
      MessagePlugin.success('删除成功')
      orderFileList()
    } finally {
    }
  }
  /* 交单上传附件 */
  const customUpload = async (file: UploadFile | UploadFile[]): Promise<RequestMethodResponse> => {
    const formData = new FormData()
    if (Array.isArray(file)) {
      file.forEach((f) => {
        formData.append('file', f.raw)
      })
    } else formData.append('file', file.raw)
    return new Promise(async (resolve) => {
      try {
        const result = await uploadOrderFileApi(
          contractOrderInfo.value.nsOrderId,
          contractOrderInfo.value.orderNo,
          formData,
        )
        MessagePlugin.success('上传成功')
        await orderFileList()
        files.value = []
        resolve({
          status: 'success',
          response: { url: result.msg },
        })
      } catch (error) {
        MessagePlugin.error('上传失败')
        resolve({
          status: 'fail',
          error: error.msg,
          response: {},
        })
      }
    })
  }
  /* 初始化地图 */
  const initMap = () => {
    window._AMapSecurityConfig = {
      securityJsCode: '8d704363c3ff7ce741276466ac57c9f4',
    }
    AMapLoader.load({
      key: 'e594607a7c375ae8d2cea02a4aa0a99a',
      version: '2.0',
    }).then((AMap) => {
      if (!map.value) return
      const myMap = new AMap.Map(map.value, {
        zoom: 4,
      })
      AMap.plugin('AMap.Driving', () => {
        const driving = new AMap.Driving({
          map: myMap,
          hideMarkers: true,
          showTraffic: false,
        })
        const startContent = `
          <div class="start-content">
            <img class="start-img" src="${node}" alt="" />
            <div class="tips">
              <div class="img">
                <img src="${fazhan}" alt="" />
              </div>
              <div class="content">
                <div>发站</div>
                <div>${logisticsInfo.value?.trackItem?.fjm || logisticsInfo.value?.trackItem?.gjz}</div>
              </div>
            </div>
          </div>
        `
        const endContent = `
          <div class="start-content">
            <img class="start-img" src="${node}" alt="" />
            <div class="tips">
              <div class="img">
                <img src="${daozhan}" alt="" />
              </div>
              <div class="content">
                <div>到站</div>
                <div>${logisticsInfo.value?.trackItem?.djm}</div>
              </div>
            </div>
          </div>
        `

        const position = logisticsInfo.value.vehicleLocationList?.map((item: any) => [item.lon, item.lat])

        let startLngLat = position?.[0]
        let endLngLat = position?.[position?.length - 1]
        const marker1 = new AMap.Marker({
          position: startLngLat,
          content: startContent,
        })
        myMap.add(marker1)
        const marker2 = new AMap.Marker({
          position: endLngLat,
          content: endContent,
        })
        myMap.add(marker2)

        driving.search(startLngLat, endLngLat, {
          waypoints: [...position?.slice(1, position?.length - 1)],
        })
      })
    })
  }

  /* 是否显示轨迹信息 */
  watchPostEffect(() => {
    if (logisticsInfo.value) initMap()
  })

  onMounted(async () => {
    await getWaybillDetail()
  })

  return {
    waybillDetailloading,
    railFreight,
    waybillInfo,
    railwayTransport,
    roadTransport,
    automobileTransport,
    logisticsInfo,
    submitDocuments,
    canUseTrackInfo,
    expressageInfo,
    orderVisible,
    fileLoading,
    fileList,
    fileColumns,
    donloadFile,
    deleteFile,
    files,
    customUpload,
    filePagination,
    map,
    detailVisible,
  }
}

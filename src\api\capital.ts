import { get, post } from '@/utils/request'

/* 按项目查询应付、应收、预付总额 */
export const getPrepayTotal = (data: any) => post<any[]>('/admin/warn/data/query-report', data)

/* 金额汇率查询 */
export const getCurrency = (params: any) => get('/admin/system/currency', params)

/* 预付、应付、应收账期 */
export const getReport = (params: Object) => get<any[]>('/admin/warn/data/list', params)

/* 销售合同可选货品及统计数据 */
export const bankRest = (data: any) => post<any>(`/scm/ns/transfer/597/queryBankRest`, data)
/* 可用贷款额度 */
export const getCreditRest = (data: any) =>
  post<any>(`/scm/ns/transfer/597/queryCreditRest`, { ...data, needCache: true })

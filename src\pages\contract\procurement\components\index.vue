<script setup lang="ts">
  import Dialog from '@/components/dialog/index.vue'
  import { formatNumber } from '@/utils/tools'
  import { useUserStore } from '@/store'
  import useContractDetail from '../hooks/details'
  const globalStore = useUserStore()
  const {
    orderInfo,
    currencyType,
    tradeType,
    totalPayment,
    stepsActive,
    totalMaterialNum,
    totalDespatch,
    qualityInfo,
    totalInventory,
    totalQuality,
    qualityVisible,
    inventoryInfo,
    inventoryVisible,
    licenceInfo,
    paymentVisible,
    accountStatement,
    downLoadFile,
    purchaseInfo,
    purchaseColumns,
    prepaymentInfo,
    formatCurrency,
    orderVisible,
    fileLoading,
    filePagination,
    fileList,
    files,
    fileColumns,
    donloadFile,
    deleteFile,
    customUpload,
    qualityColumns,
    pickMethod,
    inventoryColumns,
    licenceVisible,
    downloadFile,
    stepLoading,
    desLoading,
    toWaybill,
  } = useContractDetail()
  const { t } = useI18n()
  const $t = t
</script>
<template>
  <div class="container">
    <t-space direction="vertical" style="width: 100%">
      <t-card>
        <t-loading size="small" :loading="desLoading">
          <t-descriptions size="small" colon tableLayout="auto" :labelStyle="{ width: 'fit-content' }" :column="4">
            <t-descriptions-item :label="$t('采购单号')">{{ orderInfo?.order_no }}</t-descriptions-item>
            <t-descriptions-item :label="$t('产品名称')">{{ orderInfo?.item_name }}</t-descriptions-item>
            <t-descriptions-item :label="$t('订单日期')">{{ orderInfo?.signing_date }}</t-descriptions-item>
            <t-descriptions-item :label="$t('数量') + '（t）'">{{
              orderInfo?.item_quantity
                .split('/')
                .map((it: any) => formatNumber(it / 1000))
                .join('/')
            }}</t-descriptions-item>
            <t-descriptions-item :label="$t('单价') + `（${currencyType?.[orderInfo?.currency_id]}）`">{{
              orderInfo?.item_price
                .split('/')
                .map((it: any) => formatNumber(it * 1000))
                .join('/')
            }}</t-descriptions-item>
            <t-descriptions-item :label="$t('总金额')">{{
              formatCurrency(orderInfo?.item_amount, orderInfo?.currency_id)
            }}</t-descriptions-item>
            <t-descriptions-item :label="globalStore.userType === 2 ? $t('已收款') : $t('已付款')">
              <t-link theme="primary" @click="paymentVisible = true">{{
                formatCurrency(totalPayment, orderInfo?.currency_id)
              }}</t-link>
            </t-descriptions-item>
            <t-descriptions-item :label="$t('交易方式')">{{
              tradeType?.[orderInfo?.payment_type]
            }}</t-descriptions-item>
            <t-descriptions-item :label="$t('交货地点')">{{ orderInfo?.delivery_address }}</t-descriptions-item>
            <t-descriptions-item :label="$t('最晚交货期')">{{ orderInfo?.delivery_date_to }}</t-descriptions-item>
            <t-descriptions-item :label="$t('采购员')" :span="2">{{ orderInfo?.staff_name }}</t-descriptions-item>
            <t-descriptions-item :label="$t('供货商')" :span="2">{{ orderInfo?.vendor_name }}</t-descriptions-item>
            <t-descriptions-item :label="$t('实际供应商')" :span="2">{{
              orderInfo?.acture_vendor
            }}</t-descriptions-item>
          </t-descriptions>
        </t-loading>
      </t-card>
      <t-card>
        <template #header>
          <div>
            <t-button size="small" theme="primary" :disabled="orderInfo?.delivery_type != 1" @click="toWaybill">{{
              $t('收货')
            }}</t-button>
            <t-button size="small" theme="primary" :disabled="!orderInfo?.contract_files" @click="downloadFile">{{
              $t('合同附件')
            }}</t-button>
            <t-button size="small" theme="primary" :disabled="!licenceInfo.length" @click="licenceVisible = true">{{
              $t('查看许可证')
            }}</t-button>
            <t-button size="small" theme="primary" @click="orderVisible = true">{{ $t('交单') }}</t-button>
          </div>
        </template>
        <t-card :bordered="false" :loading="stepLoading">
          <t-steps theme="dot" :current="stepsActive" readonly layout="vertical">
            <t-step-item :title="$t('已双签')" />
            <template v-if="orderInfo?.delivery_type == 1">
              <t-step-item>
                <template #title>
                  <t-row :gutter="16" align="center" class="step-row">
                    <t-col :span="3">
                      <div>{{ $t('待发运') }}</div>
                      <div>（{{ formatNumber((totalMaterialNum - totalDespatch) / 1000) || 0 }}） t</div>
                    </t-col>
                  </t-row>
                </template>
              </t-step-item>
              <t-step-item>
                <template #title>
                  <t-row :gutter="16" align="center" class="step-row">
                    <t-col :span="3">
                      <div>{{ $t('已发运') }}</div>
                      <div>（{{ formatNumber(totalDespatch / 1000) || 0 }}）t</div>
                    </t-col>
                  </t-row>
                </template>
              </t-step-item>
            </template>
            <template v-else>
              <t-step-item>
                <template #title>
                  <t-row :gutter="16" align="center" class="step-row">
                    <t-col :span="3">
                      <div>{{ $t('待发运') }}</div>
                      <div>（{{ formatNumber((totalMaterialNum - totalInventory) / 1000) || 0 }}）t</div>
                    </t-col>
                  </t-row>
                </template>
              </t-step-item>
              <t-step-item>
                <template #title>
                  <t-row :gutter="16" align="center" class="step-row">
                    <t-col :span="3">
                      <div>{{ $t('已发运') }}</div>
                      <div>（{{ formatNumber(totalInventory / 1000) || 0 }}）t</div>
                    </t-col>
                  </t-row>
                </template>
              </t-step-item>
            </template>
            <t-step-item>
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('已质检') }}</div>
                    <div>（{{ formatNumber(totalQuality / 1000) || 0 }}）t</div>
                  </t-col>
                  <t-col v-if="qualityInfo.length" :span="3">
                    <t-button size="small" variant="base" @click="qualityVisible = true">{{ $t('详情') }}</t-button>
                  </t-col>
                </t-row>
              </template>
            </t-step-item>
            <t-step-item>
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('已入库') }}</div>
                    <div>（{{ formatNumber(totalInventory / 1000) || 0 }}）t</div>
                  </t-col>
                  <t-col v-if="inventoryInfo.length" :span="3">
                    <t-button size="small" variant="base" @click="inventoryVisible = true">{{ $t('详情') }}</t-button>
                  </t-col>
                </t-row>
              </template>
            </t-step-item>
          </t-steps>
        </t-card>
      </t-card>
    </t-space>

    <!-- 已付款弹窗 -->
    <Dialog v-model="paymentVisible" width="50%" top="2%" :footer="false">
      <template #header>
        <div>{{ $t('已付款详情') }}</div>
      </template>
      <template #body>
        <t-space direction="vertical" style="width: 100%">
          <t-card bordered header-bordered :title="$t('银行水单')">
            <div class="bank-wrap">
              <div v-if="accountStatement?.length">
                <div v-for="item in accountStatement" :key="item.id" @click="downLoadFile(item.custrecord_pfb_file)">
                  <t-link theme="primary"
                    >{{ item.created }}：{{
                      item.custrecord_pfb_filename == 62 ? $t('预付银行水单') : $t('付款银行水单')
                    }}</t-link
                  >
                </div>
              </div>
              <div v-else style="color: rgba(0, 0, 0, 0.26); text-align: center">{{ $t('暂无数据') }}</div>
            </div>
          </t-card>
          <t-card bordered header-bordered :title="$t('采购账单')">
            <t-table maxHeight="160" row-key="index" size="small" :data="purchaseInfo" :columns="purchaseColumns">
              <template #grossamount="{ row }">
                <span>{{ formatCurrency(row.grossamount, row.currency_id) }}</span>
              </template>
            </t-table>
          </t-card>
          <t-card bordered header-bordered :title="$t('预付款账单')">
            <t-table maxHeight="160" row-key="index" size="small" :data="prepaymentInfo" :columns="purchaseColumns">
              <template #grossamount="{ row }">
                <span>{{ formatCurrency(row.grossamount, row.currency_id) }}</span>
              </template>
            </t-table>
          </t-card>
        </t-space>
      </template>
    </Dialog>
    <!-- 质检详情 -->
    <Dialog v-model="qualityVisible" width="60%" :footer="false">
      <template #header>{{ $t('质检详情') }}</template>
      <template #body>
        <t-table row-key="index" size="small" max-height="360px" :data="qualityInfo" :columns="qualityColumns">
          <template #quantity="{ row }">
            <span>{{ formatNumber(row.quantity / 1000) || 0 }} t</span>
          </template>
          <template #pick_method="{ row }">
            <span>{{ pickMethod[row.pick_method] }}</span>
          </template>
        </t-table>
      </template>
    </Dialog>
    <!-- 入库详情 -->
    <Dialog v-model="inventoryVisible" width="40%" :footer="false">
      <template #header>{{ $t('入库详情') }}</template>
      <template #body>
        <t-table row-key="index" size="small" max-height="360px" :data="inventoryInfo" :columns="inventoryColumns">
          <template #quantity="{ row }">
            <span>{{ formatNumber(row.quantity / 1000) || 0 }} t</span>
          </template>
        </t-table>
      </template>
    </Dialog>
    <!-- 许可证详情 -->
    <Dialog v-model="licenceVisible" width="62%" top="6%" :footer="false">
      <template #header>{{ $t('许可证详情') }}</template>
      <template #body>
        <t-descriptions bordered table-layout="auto" :column="2">
          <t-descriptions-item :label="$t('产品名称')"
            >{{ licenceInfo[0]?.itemName }} （{{
              licenceInfo[0]?.itemType == 1 ? $t('原料') : $t('成品')
            }}）</t-descriptions-item
          >
          <t-descriptions-item :label="$t('许可证状态')">{{ licenceInfo[0]?.applyStatusLabel }}</t-descriptions-item>
          <t-descriptions-item :label="$t('申请时间')">{{ licenceInfo[0]?.createTime }}</t-descriptions-item>
          <t-descriptions-item :label="$t('申请人')">{{ licenceInfo[0]?.applyPerson }}</t-descriptions-item>
          <t-descriptions-item :label="$t('总重量')"
            >{{ (licenceInfo[0]?.permitWeight / 1000).toFixed(3) }} t</t-descriptions-item
          >
          <t-descriptions-item :label="$t('进境口岸')">{{ licenceInfo[0]?.permitPort }}</t-descriptions-item>
          <t-descriptions-item :label="$t('收货人')">{{ licenceInfo[0]?.receiver }}</t-descriptions-item>
          <t-descriptions-item :label="$t('供应商名称')">{{ licenceInfo[0]?.vendorName }}</t-descriptions-item>
          <t-descriptions-item :label="$t('产地(州市)')">{{ licenceInfo[0]?.producingArea }}</t-descriptions-item>
          <t-descriptions-item :label="$t('始发站')">{{ licenceInfo[0]?.startStation }}</t-descriptions-item>
          <t-descriptions-item :label="$t('粮仓')" :span="3">{{ licenceInfo[0]?.warehouseName }}</t-descriptions-item>
          <t-descriptions-item :label="$t('途径路线')" :span="3">{{ licenceInfo[0]?.route }}</t-descriptions-item>
          <t-descriptions-item :label="$t('初审联系单号')">{{ licenceInfo[0]?.preliminaryNo }}</t-descriptions-item>
          <t-descriptions-item :label="$t('许可证编号')">{{ licenceInfo[0]?.permitNo }}</t-descriptions-item>
        </t-descriptions>
        <div class="file">
          <div>{{ $t('附件') }}：</div>
        </div>
      </template>
    </Dialog>
    <!-- 交单详情 -->
    <Dialog v-model="orderVisible" :footer="false" width="600px" top="5%">
      <template #header>
        <div>{{ $t('交单详情') }}</div>
      </template>
      <template #body>
        <t-table
          row-key="index"
          height="200px"
          size="small"
          :loading="fileLoading"
          :pagination="filePagination"
          :data="fileList"
          :columns="fileColumns"
          table-layout="fixed"
        >
          <template #link="{ row }">
            <t-button size="small" @click="donloadFile(row.id)">{{ $t('下载') }}</t-button>
            <t-popconfirm theme="default" content="确认删除此文件吗" @confirm="deleteFile(row.id)">
              <t-button size="small" theme="warning">{{ $t('删除') }}</t-button>
            </t-popconfirm>
          </template>
        </t-table>
        <t-upload v-model="files" theme="custom" :request-method="customUpload" draggable>
          <template #dragContent="params">
            <div>{{ $t('点击上传 / 拖拽到此区域') }}</div>
          </template>
        </t-upload>
      </template>
    </Dialog>
  </div>
</template>
<style scoped lang="less">
  :deep(.t-steps) {
    .t-steps-item--process {
      .t-steps-item__title {
        font-weight: 100;
      }
    }
  }

  :deep(.t-upload__dragger-center) {
    margin: 20px auto 0;
    height: 120px;
  }
  .bank-wrap {
    height: 60px;
    overflow-y: auto;
  }
  .file {
    margin-top: 16px;
    display: flex;
  }
  .step-row {
    color: inherit;
    width: 60vw;

    div:nth-child(1) {
      display: flex;
    }
  }
</style>

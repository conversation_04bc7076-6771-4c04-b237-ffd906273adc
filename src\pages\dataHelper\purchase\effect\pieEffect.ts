import { computed, ref } from 'vue'

const pieEffect = () => {
	const PieTypeMap = { 1: 'Lượng giao dịch', 2: 'Số tiền giao dịch' }

	const pieType = ref<string>('1')
	const showPieType = ref<boolean>(false)
	const pieTypeText = computed(() => {
		return PieTypeMap[parseInt(pieType.value) as keyof typeof PieTypeMap]
	})

	const PieSelect = [
		{ text: 'Lượng giao dịch', value: '1' },
		{ text: 'Số tiền giao dịch', value: '2' }
	]

	return {
		pieType,
		showPieType,
		pieTypeText,
		PieSelect
	}
}

export default pieEffect

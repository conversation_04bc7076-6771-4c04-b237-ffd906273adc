import type { Univer } from '@univerjs/presets'
import { UniverSheetsCorePreset } from '@univerjs/preset-sheets-core'
import sheetsCoreZhCN from '@univerjs/preset-sheets-core/locales/zh-CN'
import sheetsCoreENUS from '@univerjs/preset-sheets-core/locales/en-US'
import sheetsCoreRU from '@univerjs/preset-sheets-core/locales/ru-RU'
import sheetsCoreVIVN from '@univerjs/preset-sheets-core/locales/vi-VN'
import { createUniver, LocaleType, mergeLocales } from '@univerjs/presets'
import { useMagicKeys, useEventListener } from '@vueuse/core'
import { saveDocConfig, getDocConfig } from '@/api/myFile'
import { tokenize } from 'excel-formula-tokenizer'
import { buildTree } from 'excel-formula-ast'

export default () => {
  const route = useRoute()
  const { locale } = useI18n({ useScope: 'global' })
  const loading = ref<boolean>(false)
  const keys = useMagicKeys()

  const univerSheetRef = ref<HTMLDivElement | null>(null)
  const univerInstance = ref<Univer | null>(null)
  let univerAPIInstance: any = null
  const docId = route.query.docId as string

  /* 语言环境映射 */
  const localeMap: Record<string, LocaleType> = {
    zh: LocaleType.ZH_CN,
    en: LocaleType.EN_US,
    ru: LocaleType.RU_RU,
    vi: LocaleType.VI_VN,
  }

  /* 初始化 Univer 实例 */
  const initUniver = async () => {
    if (univerAPIInstance) return univerAPIInstance
    if (!univerSheetRef.value || !docId) return

    const sheetConfig = await fetchConfig()
    const { univer, univerAPI } = createUniver({
      locale: localeMap[locale.value] || LocaleType.ZH_CN,
      locales: {
        [LocaleType.ZH_CN]: mergeLocales(sheetsCoreZhCN),
        [LocaleType.EN_US]: mergeLocales(sheetsCoreENUS),
        [LocaleType.RU_RU]: mergeLocales(sheetsCoreRU),
        [LocaleType.VI_VN]: mergeLocales(sheetsCoreVIVN),
      },
      presets: [
        UniverSheetsCorePreset({
          container: univerSheetRef.value as HTMLElement,
        }),
      ],
    })

    if (sheetConfig) univerAPI.createWorkbook(sheetConfig)
    else univerAPI.createWorkbook({})
    univerInstance.value = univer
    univerAPIInstance = univerAPI
  }

  /* 获取文档配置 */
  const fetchConfig = async () => {
    try {
      loading.value = true
      const res = await getDocConfig(docId)
      loading.value = false
      return res.data
    } catch (err) {
      console.log(`获取配置失败:${err}`)
    }
  }

  /* 保存表格数据到后端 */
  const save = async () => {
    if (!univerAPIInstance) return

    try {
      const fWorkbook = univerAPIInstance.getActiveWorkbook()
      const snapshot = fWorkbook.save()
      const sheets = snapshot.sheets || {}
      // 遍历每个 sheet
      Object.values(sheets).forEach((sheet: any) => {
        const cellData = sheet.cellData || {}

        Object.values(cellData).forEach((row: any) => {
          Object.values(row).forEach((cell: any) => {
            if ('f' in cell) {
              const formula = cell.f.replace(/^\s*=\s*/, '')
              const tokens = tokenize(formula)
              const ast = buildTree(tokens)
              cell.custom = {
                ast,
              }
            }
          })
        })
      })
      loading.value = true
      await saveDocConfig(docId, snapshot)
      loading.value = false
    } catch (error) {
      console.error('保存失败', error)
    }
  }

  watch(
    () => keys['ctrl+s'].value,
    async (pressed) => {
      if (pressed) {
        await save()
        MessagePlugin.success('保存成功')
      }
    },
    { immediate: false },
  )

  useEventListener(window, 'keydown', (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 's') {
      e.preventDefault()
    }
  })

  /* 监听语言变化 */
  watch(
    () => locale.value,
    async (newLocale) => {
      if (univerInstance) {
        univerAPIInstance.setLocale(localeMap[newLocale] || LocaleType.ZH_CN)
      }
    },
  )

  onMounted(() => {
    initUniver()
  })

  return {
    univerSheetRef,
    loading,
  }
}

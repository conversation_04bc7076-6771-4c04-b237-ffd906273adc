/* eslint-disable prefer-destructuring */
import { defineStore } from 'pinia'
import { store } from '@/store'
import { asyncRouterList } from '@/router'
import Layout from '@/layouts/index.vue'
import HomeIcon from '@/assets/assets-slide-dashboard.svg'
import {
  getCaptcha,
  userLogin,
  userInfo,
  getRouters,
  type UserBasicInfo,
  type UserCpInfo,
  type UserMpInfo,
  type StaffPostList,
  UserEntityList,
  redirectWxCpLogin,
  redirectWxMpLogin,
  wxCpLogin,
  wxMpLogin,
} from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { useWx } from '@/hooks/wxsdk'

export const useUserStore = defineStore('user', () => {
  const { canIUseCp, canIUseMp } = useWx()
  const canIUseWx = computed(() => {
    return canIUseCp.value || canIUseMp.value
  })

  const basicId = computed(() => {
    return state.basicInfo?.userId
  })
  const cpId = computed(() => {
    return state.cpInfo?.userId
  })
  const mpUnionId = computed(() => {
    return state.mpInfo?.userId
  })

  const wxRedirectLogin = () => (canIUseCp.value ? redirectWxCpLogin() : redirectWxMpLogin())

  const state = reactive({
    token: getToken(),
    name: '',
    avatar: '',
    roles: [] as any[],
    permissions: [] as any[],
    posts: [] as StaffPostList,
    hasAuth: true,
    roleInfos: [] as any[],
    loginType: 0,
    userType: 0,
    isAdmin: false,
    basicInfo: null as UserBasicInfo,
    cpInfo: null as UserCpInfo,
    mpInfo: null as UserMpInfo,
    vendorIds: [] as number[],
    vendors: [] as UserEntityList,
    customerIds: [] as number[],
    customers: [] as UserEntityList,
    projectIds: [] as number[],
    subsidiaryIds: [] as number[],
    routers: [] as any[],
    unionId: 0,
    routes: [] as any[],
  })

  function buildAuthParams() {
    const userType = state.userType
    let vendorIdList = state.vendorIds || []
    let customerIdList = state.customerIds || []
    if (userType !== 1) {
      vendorIdList = vendorIdList.length ? vendorIdList : [-1]
      customerIdList = customerIdList.length ? customerIdList : [-1]
    }
    return {
      vendorIdList,
      customerIdList,
      projectIdList: state.projectIds || [],
      subsidiaryIdList: state.subsidiaryIds || [],
    }
  }
  /* 验证码 */
  async function capchat() {
    const res = await getCaptcha()
    return res
  }
  /* 用户登录 */
  async function login(userInfo: Record<string, unknown>) {
    return new Promise<void>((resolve, reject) => {
      userLogin(userInfo)
        .then((res: any) => {
          if ((res as any).code !== 200) {
            MessagePlugin.error(res.msg)
            reject(res)
            state.hasAuth = false
            return
          }
          state.hasAuth = true
          setToken(res.data.token)
          state.token = res.data.token
          resolve()
        })
        .catch((error) => {
          state.hasAuth = false
          reject(error)
        })
    })
  }

  const tryWxLogin = async (code: string) => {
    return new Promise<void>((resolve, reject) => {
      ;(canIUseCp.value ? wxCpLogin(code) : wxMpLogin(code))
        .then((res) => {
          if (res.code !== 200) {
            reject(res)
            return
          }
          setToken(res.data)
          state.token = res.data
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /* 获取用户信息 */
  async function getUserInfo() {
    return new Promise<any>((resolve, reject) => {
      userInfo()
        .then((res) => {
          const { user, roles, permissions } = res.data
          const avatar = import.meta.env.VITE_APP_BASE_API + user.avatarUrl
          if (roles && roles.length > 0) {
            // 验证返回的roles是否是一个非空数组
            state.roles = roles
            state.permissions = permissions
          } else {
            state.roles = ['ROLE_DEFAULT']
          }
          state.name = user.userName
          state.avatar = avatar
          state.roleInfos = roles
          state.posts = user.posts
          state.userType = user.userType
          state.isAdmin = user.isAdmin
          state.basicInfo = user.basicUser
          state.cpInfo = user.wxCpUser
          state.mpInfo = user.wxMpUser
          state.vendorIds = user.vendorIds || []
          state.customerIds = user.customerIds || []
          state.vendors = user.vendors
          state.customers = user.customers
          state.projectIds = user.projectIds || []
          state.subsidiaryIds = user.subsidiaryIds || []
          state.unionId = user.unionId
          getUserRoutes()
          resolve(res)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  /* 获取用户路由 */
  async function getUserRoutes() {
    return new Promise<any>((resolve, reject) => {
      getRouters()
        .then((res) => {
          const { data } = res
          state.routes = data
          // / 过滤路由：仅保留路径匹配的顶级路由及其子路由
          const filterRouters = asyncRouterList.filter((route) => data.includes(route.path))

          state.routers = [
            {
              path: '/home',
              component: Layout,
              isHome: true,
              meta: { title: '首页', icon: HomeIcon },
              children: [
                {
                  path: '',
                  name: 'home',
                  component: () => import('@/pages/home/<USER>'),
                },
              ],
            },
            ...filterRouters,
          ]
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  async function logout() {
    removeToken()
    state.roles = []
    state.token = ''
  }
  // async function removeToken() {
  //   state.token = ''
  // }

  const canIUse = (typeLimit?: number[]) => {
    if (!typeLimit) return true
    return typeLimit.includes(state.userType)
  }

  return {
    canIUse,
    canIUseWx,
    canIUseCp,
    canIUseMp,
    basicId,
    cpId,
    mpUnionId,
    wxRedirectLogin,
    tryWxLogin,
    ...toRefs(state),
    buildAuthParams,
    capchat,
    login,
    getUserInfo,
    getUserRoutes,
    logout,
    removeToken,
  }
})

export function getUserStore() {
  return useUserStore(store)
}

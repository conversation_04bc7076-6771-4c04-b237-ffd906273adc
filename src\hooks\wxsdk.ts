import * as ww from '@wecom/jssdk'
import { getMpSignature, getCpSignature } from '@/api/wx'

export const useWx = () => {
  const initWxBridge = async () => {
    if (!(ww.env.isWeChat || ww.env.isWeCom)) return
    ww.register({
      corpId: ww.env.isWeCom ? 'wwd802c21f6249ac29' : 'wxccb55f93566c3978', // 必填，当前用户企业所属企业ID
      getConfigSignature,
      onConfigSuccess() {
        console.log('wx bridge init success')
      },
      onConfigFail() {
        console.log('wx bridge init fail')
      },
    })

    async function getConfigSignature(url: string) {
      const res = ww.env.isWeCom ? await getCpSignature(url) : await getMpSignature(url)
      const { nonceStr, signature, timestamp } = res.data
      return { timestamp, nonceStr, signature }
    }
  }
  initWxBridge()

  const canIUseMp = ref(ww.env.isWeChat)

  const canIUseCp = ref(ww.env.isWeCom)
  return { canIUseCp, canIUseMp }
}

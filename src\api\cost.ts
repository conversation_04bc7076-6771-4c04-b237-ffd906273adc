import { get, post, remove } from '@/utils/request'

export const costTplListByProject = (projectId: number) =>
  get<any[]>(`/scm/ns/transfer/570/QueryContractCostTemplate`, { projectId, needCache: false })

export const saveCostTemplate = (data: any) => post<any>(`/scm/ns/transfer/570/SaveContractCostTemplate`, data)

export const removeCostTemplte = (id: number) => get<any>(`/scm/ns/transfer/570/DeleteContractCostTemplate`, { id })

export const costTplConfigList = (id: string) => get<any[]>(`/scm/costTemplateConfig/config/${id}`)

export const getCostTplConfigDetail = (id: string) => get<any>(`/scm/costTemplateConfig/detail/${id}`)

export const saveCostTplConfig = (data: any) => post<any>(`/scm/costTemplateConfig`, data)

export const removeCostTplConfig = (id: string) => remove<any>(`/scm/costTemplateConfig/${id}`)

export const checkCostConfigValue = (id: number) => get<{ result: string }>(`/scm/costTemplateConfig/calc/${id}`)

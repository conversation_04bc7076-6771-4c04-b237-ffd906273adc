cd-pipeline: &cd-pipeline
  docker:
    image: node:22.14
  stages:
    - name: 前置安装
      script: npm i --global pnpm
    - name: 依赖安装
      script: pnpm i
    - name: 编译
      script: pnpm build
    - name: 部署
      imports: ./.ci/ssh-key.yml
      image: plugins/ansible
      settings:
        private_key: $PRIVATE_KEY
        inventory: ./.ci/hosts
        playbook: ./.ci/playbook.yml

v*:
  tag_push:
    - *cd-pipeline

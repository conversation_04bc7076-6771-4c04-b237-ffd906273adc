<script setup lang="ts">
  import Empty from '@/components/Empty/index.vue'
  import { formatNumber } from '@/utils/tools'
  import useSales from './hooks'
  const { t } = useI18n()
  const $t = t
  const {
    activeProjectId,
    saleContractList,
    saleContractInfoLoading,
    projectType,
    pickMethod,
    formData,
    timeRange,
    disableDate,
    timeChange,
    orderStatus,
    inputValue,
    filteredOptions,
    handleSearch,
    handleSelect,
    onSubmit,
    onReset,
    displayColumns,
    columnControllerVisible,
    columnControllerConfig,
    loading,
    columns,
    pagination,
    orderList,
    refresh,
    formatCurrency,
    tabChange,
    toContractDetail,
  } = useSales()
</script>
<template>
  <t-loading :loading="saleContractInfoLoading" class="container">
    <t-tabs v-model="activeProjectId" @change="tabChange" v-if="saleContractList.length">
      <t-tab-panel v-for="item in saleContractList" :key="item.projectId" :value="item.projectId">
        <template #label>
          <div class="tab-label">
            <span>{{ projectType[item.projectId] }}</span>
            <t-badge color="#54c7b2" :count="item.allCount" :offset="[0, -4]">{{ $t('待') }}</t-badge>
            <t-badge color="#ffc000" :count="item.expiringCount" :offset="[0, -4]">{{ $t('临') }}</t-badge>
            <t-badge :count="item.expiredCount" :offset="[0, -4]">{{ $t('逾') }}</t-badge>
          </div>
        </template>
        <t-card :bordered="false">
          <t-form ref="form" :data="formData" label-width="auto" layout="inline" @submit="onSubmit" @reset="onReset">
            <t-form-item :label="$t('产品名称')" name="itemId">
              <t-select
                size="small"
                v-model="formData.itemId"
                :options="item.itemList"
                :keys="{ label: 'itemName', value: 'itemId' }"
                :placeholder="$t('请选择产品')"
              >
              </t-select>
            </t-form-item>
            <t-form-item :label="$t('客户')" name="customerId">
              <t-auto-complete
                size="small"
                v-model="inputValue"
                :options="filteredOptions"
                :placeholder="$t('请输入客户名称')"
                highlight-keyword
                :filterable="false"
                :empty="$t('暂无数据')"
                :popup-props="{
                  overlayStyle: {
                    display: 'block',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  },
                }"
                @change="handleSearch"
                @select="handleSelect"
              />
            </t-form-item>
            <t-form-item :label="$t('时间')" name="timeRange">
              <t-date-range-picker
                size="small"
                :placeholder="[$t('开始时间'), $t('结束时间')]"
                :disable-date="disableDate"
                v-model="timeRange"
                @change="timeChange"
              />
            </t-form-item>
            <t-form-item :label="$t('状态')" name="password">
              <t-select
                size="small"
                v-model="formData.status"
                :options="orderStatus"
                :keys="{ label: 'title', value: 'value' }"
                :placeholder="$t('请选择状态')"
              >
              </t-select>
            </t-form-item>
            <t-form-item>
              <t-space>
                <t-button size="small" theme="primary" type="submit">{{ $t('查询') }}</t-button>
                <t-button size="small" theme="default" variant="base" type="reset">{{ $t('重置') }}</t-button>
              </t-space>
            </t-form-item>
          </t-form>
        </t-card>
        <t-space class="space">
          <t-button size="small" shape="circle" variant="outline" @click="refresh">
            <template #icon><t-icon name="refresh" /></template>
          </t-button>
          <t-button size="small" shape="circle" variant="outline" @click="columnControllerVisible = true">
            <template #icon><t-icon name="setting" /></template>
          </t-button>
        </t-space>
        <t-table
          row-key="index"
          hover
          size="small"
          maxHeight="500"
          v-model:displayColumns="displayColumns"
          v-model:columnControllerVisible="columnControllerVisible"
          :column-controller="columnControllerConfig"
          :loading="loading"
          :data="orderList"
          :columns="columns"
          :pagination="pagination"
          lazy-load
        >
          <template #customer_name="{ row }">
            <span>{{ row?.customer_name }}</span>
          </template>
          <template #end_rest_day="{ row }">
            <t-tag
              size="small"
              shape="round"
              :color="row.end_rest_day < 0 ? '#ff0000' : row.end_rest_day < 10 ? '#ffc000' : '#339966'"
              >{{
                row.end_rest_day < 0
                  ? `${$t('逾期')}${Math.abs(row.end_rest_day)}`
                  : `${$t('剩余')}${Math.abs(row.end_rest_day)}`
              }}{{ $t('天') }}</t-tag
            >
          </template>
          <template #item_price="{ row }">
            <span
              >{{
                row.item_price
                  .split('/')
                  .map((it) => formatCurrency(it * 1000, row.currency_id))
                  .join('/')
              }}
            </span>
          </template>
          <template #item_quantity="{ row }">
            <span
              >{{
                row.item_quantity
                  .split('/')
                  .map((it) => formatNumber(it / 1000))
                  .join('/')
              }}
              t</span
            >
          </template>
          <template #item_amount="{ row }">
            <span>{{ formatCurrency(row.item_amount, row.currency_id) }}</span>
          </template>

          <template #rest_quantity="{ row }">
            <t-tag size="small" color="#ff0000">{{ formatNumber(row.rest_quantity / 1000) }} t</t-tag>
          </template>
          <template #delivery_type="{ row }">
            <span>{{ pickMethod?.[row.delivery_type] }}</span>
          </template>
          <template #link="{ row }">
            <t-space>
              <t-link theme="primary" @click="toContractDetail(row)">{{ $t('详情') }}</t-link>
            </t-space>
          </template>
        </t-table>
      </t-tab-panel>
    </t-tabs>
    <Empty v-if="!saleContractList.length && !loading" :description="$t('暂无数据')" />
  </t-loading>
</template>
<style scoped lang="less">
  .space {
    justify-content: flex-end;
    margin: 20px 0;
  }

  .t-tabs {
    height: 100%;
    padding: 16px;
    box-sizing: border-box;
    :deep(.t-tabs__content) {
      height: 100%;
    }
  }
  .tab-label {
    width: fit-content;
    display: flex;
    align-items: center;
    column-gap: 16px;
  }
  :deep(.t-table) {
    flex: 1;
  }
  .t-tab-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :deep(.t-text-ellipsis) {
    text-align: center;
  }
</style>

<script setup lang="ts">
  import useIndex from './hooks'
  const {
    userRegisterList,
    lodaing,
    columns,
    pagination,
    visible,
    openDetail,
    currentUserInfo,
    dialogClose,
    onConfirm,
    onClose,
    isAgent,
    isVendor,
    isCustomer,
    subsidiaryIds,
    subCompany,
    images,
    pdfs,
    showImage,
    previewPdf,
    confirmLoading,
    cancelLoading,
    districtMap,
  } = useIndex()
  const $t = useI18n().t
</script>
<template>
  <div class="container">
    <t-card>
      <t-table
        row-key="index"
        max-height="500"
        :data="userRegisterList"
        :columns="columns"
        :loading="lodaing"
        size="small"
        :pagination="pagination"
        lazy-load
      >
        <template #countryArea="{ row }">
          {{ districtMap[row.countryArea] }}
        </template>
        <template #link="{ row }">
          <t-link theme="primary" @click="openDetail(row)">{{ $t('详情') }}</t-link>
        </template>
      </t-table>
    </t-card>
    <t-dialog v-model:visible="visible" :header="$t('申请详情')" width="35%" @close="onClose">
      <template #confirmBtn>
        <t-button theme="primary" :loading="confirmLoading" @click="onConfirm">{{ $t('同意') }}</t-button>
      </template>
      <template #cancelBtn>
        <t-button theme="warning" :loading="cancelLoading" @click="dialogClose">{{ $t('拒绝') }}</t-button>
      </template>
      <div class="dialog-boay">
        <div class="cell">
          <div class="label">{{ $t('用户名称') }}：</div>
          <div>
            <t-typography-text mark>{{ currentUserInfo?.user?.userName }}</t-typography-text>
          </div>
        </div>
        <div class="cell">
          <div class="label">{{ $t('地区') }}：</div>
          <div>
            <t-typography-text mark>{{ districtMap[currentUserInfo?.countryArea] }}</t-typography-text>
          </div>
        </div>
        <div class="cell">
          <div class="label">{{ $t('公司名称') }}：</div>
          <div>
            <t-typography-text mark>{{ currentUserInfo?.entityName }}</t-typography-text>
          </div>
        </div>
        <div class="cell">
          <div class="label">{{ $t('税号/身份证号') }}：</div>
          <div>
            <t-typography-text mark>{{ currentUserInfo?.serialNo }}</t-typography-text>
          </div>
        </div>
        <div class="cell">
          <div class="label">{{ $t('联系方式') }}：</div>
          <div>
            <t-typography-text mark>{{ currentUserInfo?.entityTel }}</t-typography-text>
          </div>
        </div>
        <div v-if="currentUserInfo?.zaloId" class="cell">
          <div class="label">ZaloId：</div>
          <div>
            <t-typography-text mark>{{ currentUserInfo?.zaloId }}</t-typography-text>
          </div>
        </div>
        <div class="cell">
          <div class="label">{{ $t('联系地址') }}：</div>
          <div>
            <t-typography-text mark>{{ currentUserInfo?.entityAddress }}</t-typography-text>
          </div>
        </div>
        <div class="cell">
          <div class="label">{{ $t('银行账号') }}：</div>
          <div>
            <t-typography-text mark>{{ currentUserInfo?.bankAccount }}</t-typography-text>
          </div>
        </div>
        <div class="cell">
          <div class="label">{{ $t('资质证明') }}：</div>
          <div v-if="images?.length">
            <div class="img-wrap">
              <t-image-viewer v-model:visible="showImage" :images="images">
                <template #trigger>
                  <div class="img-content">
                    <img :src="images[0]" class="img" />
                    <div class="image--hover" @click="showImage = true">
                      <span>{{ $t('预览') }}</span>
                    </div>
                  </div>
                </template>
              </t-image-viewer>
            </div>
            <div class="pdf" v-for="(p, index) in pdfs" :key="index" @click="previewPdf(p)">
              <t-icon name="file-1" />
              <div>{{ p.split('=')[0] }}</div>
            </div>
          </div>
        </div>
        <div class="cell" v-if="!currentUserInfo.vendors && !currentUserInfo.customers">
          <div class="label">{{ $t('客商类型') }}：</div>
          <div>
            <t-space>
              <t-checkbox v-model="isCustomer">{{ $t('客户') }}</t-checkbox>
              <t-checkbox v-model="isVendor">{{ $t('供应商') }}</t-checkbox>
            </t-space>
          </div>
        </div>
        <div class="cell" v-if="!currentUserInfo.vendors && !currentUserInfo.customers">
          <div class="label">{{ $t('代理商/底商') }}：</div>
          <div>
            <t-checkbox v-model="isAgent" />
          </div>
        </div>
        <div class="cell" v-if="!currentUserInfo.vendors && !currentUserInfo.customers">
          <div class="label">{{ $t('关系子公司') }}：</div>
          <div>
            <t-select size="small" v-model="subsidiaryIds" :placeholder="$t('关系子公司')" multiple>
              <t-option label="全选" :check-all="true" />
              <t-option
                v-for="item in subCompany"
                :key="item.subsidiaryId"
                :value="item.subsidiaryId"
                :label="item.subsidiaryName"
              ></t-option>
            </t-select>
          </div>
        </div>
        <div
          v-if="
            (currentUserInfo.customers && currentUserInfo.customers.length) ||
            (currentUserInfo.vendors && currentUserInfo.vendors.length)
          "
        >
          <div class="cell" v-if="currentUserInfo.customers && currentUserInfo.customers.length">
            <div>{{ $t('关联客户') }}：</div>
            <div>
              <div v-for="item in currentUserInfo.customers" :key="item.customerId">{{ item.customerName }}</div>
            </div>
          </div>
          <div class="cell" v-if="currentUserInfo.vendors && currentUserInfo.vendors.length">
            <div>{{ $t('关联供应商') }}：</div>
            <div>
              <div v-for="item in currentUserInfo.vendors" :key="item.vendorId">{{ item.vendorName }}</div>
            </div>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>
<style scoped lang="less">
  .cell {
    display: flex;
    > div {
      line-height: 26px;
      overflow: hidden;
    }
  }
  .label {
    min-width: fit-content;
  }
  .pdf {
    color: var(--td-brand-color-7);
    text-decoration: underline;
    display: flex;
    align-items: center;

    > div {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .image--hover {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: var(--td-text-color-anti);
    line-height: 22px;
    transition: 0.2s;
  }
  .img-wrap {
    width: 40px;
    height: 40px;
    .img-content {
      width: 100%;
      height: 100%;
      position: relative;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    .img {
      width: 100%;
      height: 100%;
    }
  }
  :deep(.t-dialog__footer) {
    text-align: center;
  }
</style>

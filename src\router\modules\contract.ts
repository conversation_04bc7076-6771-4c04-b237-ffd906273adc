import Layout from '@/layouts/index.vue'

export default [
  {
    path: '/contract',
    name: 'contract',
    component: Layout,
    redirect: '/contract/procurement',
    meta: { title: '合同管理', icon: 'catalog', orderNo: 2 },
    children: [
      {
        path: 'procurement',
        name: 'ContractProcurement',
        component: () => import('@/pages/contract/procurement/index.vue'),
        meta: { title: '采购合同', dynamicTitle: true },
      },
      {
        path: 'sales',
        name: 'ContractSales',
        component: () => import('@/pages/contract/sales/index.vue'),
        meta: { title: '销售合同', dynamicTitle: true },
      },

      {
        path: 'shipments/:id',
        name: 'ContractShipments',
        component: () => import('@/pages/contract/shipments/index.vue'),
        meta: { title: '运单列表', hidden: true },
      },
      {
        path: 'shipments-details',
        name: 'ContractShipmentsDetails',
        component: () => import('@/pages/contract/shipments/details/index.vue'),
        meta: { title: '运单详情', hidden: true },
      },
      {
        path: 'purchase-details',
        name: 'ContractPurchaseDetails',
        component: () => import('@/pages/contract/procurement/components/index.vue'),
        meta: { title: '采购合同详情', hidden: true },
      },
      {
        path: 'sales-details',
        name: 'ContractSalesDetails',
        component: () => import('@/pages/contract/sales/components/details.vue'),
        meta: { title: '销售合同详情', hidden: true },
      },
    ],
  },
]

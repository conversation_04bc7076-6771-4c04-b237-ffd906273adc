import { useProjectStore } from '@/use/useProject'
import { groupBy } from 'lodash-es'
import { getPrepayTotal, getCurrency, bankRest, getCreditRest } from '@/api/capital'
import { useNsListLabelRaw } from '@/use/dict'
import { useRouter } from 'vue-router'

export default () => {
  const { t, locale } = useI18n()
  const $t = t
  const router = useRouter()
  const projectHook = useProjectStore()
  /* 请求参数 */
  const prepayParams = ref<any>({
    sumCols: ['occupy_amount'],
    groupCols: ['risk_category', 'control_point', 'company_project', 'currency_id'],
    riskCategory: '3',
    companyProject: '',
    normal: true,
  })
  /* 金额转换请求参数 */
  let currencyParams = {
    fromCurrency: '',
    fromCurrencyList: [],
    toCurrency: 'USD',
  }
  /* 状态id  */
  const statusId = ref<number>(0)
  /* 状态列表 */
  const dateTabs = computed(() => [
    { id: 0, name: $t('全部') },
    { id: 1, name: $t('临期') },
    { id: 2, name: $t('超期') },
  ])

  const loading = ref<boolean>(false)
  const bankRestLoading = ref<boolean>(false)
  const creditRestLoading = ref<boolean>(false)
  /* 卡片列表 */
  const cardList = ref([
    {
      id: 0,
      title: computed(() => $t('项目预付总额')),
      type: 'prepay',
      list: [],
      isDetail: true,
      loading: () => loading.value,
    },
    {
      id: 1,
      title: computed(() => $t('项目应付总额')),
      type: 'payable',
      list: [],
      isDetail: true,
      loading: () => loading.value,
    },
    {
      id: 2,
      title: computed(() => $t('项目预收总额')),
      type: 'save',
      list: [],
      isDetail: true,
      loading: () => loading.value,
    },
    {
      id: 3,
      title: computed(() => $t('项目应收总额')),
      type: 'receivables',
      list: [],
      isDetail: true,
      loading: () => loading.value,
    },
    {
      id: 4,
      title: computed(() => $t('现金余额')),
      list: [],
      isDetail: false,
      loading: () => bankRestLoading.value,
    },
    {
      id: 5,
      title: computed(() => $t('可用贷款额度')),
      list: [],
      isDetail: false,
      loading: () => creditRestLoading.value,
    },
  ])
  const projectId = ref<any>(projectHook.activeProjectId)
  /* 货币类型 */
  const currencyType = ref<any>({})
  /* 项目列表 */
  const projectList = computed(() => projectHook.menuOptions)

  /* 金额转换数组 */
  const currencyTypeList = ref<any>([])
  /* 获取字典label */
  const nsListLabelRaw = async () => {
    const { currency } = await useNsListLabelRaw('currency')
    currencyType.value = currency
  }
  /* 获取金额汇率 */
  const getCurrencyData = async () => {
    const { data } = await getCurrency(currencyParams)
    currencyTypeList.value = data
  }

  /* 获取项目金额信息  */
  const getPrepayData = async (params: any) => {
    loading.value = true
    params.companyProject = String(projectHook.activeProjectId)
    try {
      const { data } = await getPrepayTotal(params)
      const controlPointGroup = groupBy(data, 'controlPoint')
      /* 预付总金额 */
      cardList.value[0].list = controlPointGroup['30001'] || []
      /* 应付总金额 */
      cardList.value[1].list = controlPointGroup['30002'] || []
      /* 应收总金额 */
      cardList.value[3].list = controlPointGroup['30003'] || []
      /* 预收总金额 */
      cardList.value[2].list = controlPointGroup['30004'] || []
    } finally {
      loading.value = false
    }
  }

  /* 现金余额 */
  const getBankRest = async (params: any) => {
    bankRestLoading.value = true
    try {
      const { data } = await bankRest(params)
      cardList.value[4].list = Object.entries(groupBy(data || [], 'currency')).map((e) => {
        return {
          currencyId: Number(e[0]),
          occupyAmount: e[1].reduce((a, b) => a + Number(b.amount), 0),
          creditAmount: e[1].reduce((a, b) => a + Number(b.credit_amount), 0),
          debitAmount: e[1].reduce((a, b) => a + Number(b.debit_amount), 0),
        }
      })
    } finally {
      bankRestLoading.value = false
    }
  }

  /* 可用贷款额度 */
  const getLoanlimit = async (params: any) => {
    creditRestLoading.value = true
    try {
      const { data } = await getCreditRest(params)
      cardList.value[5].list = Object.entries(groupBy(data || [], 'currency')).map((e) => {
        return {
          currencyId: Number(e[0]),
          occupyAmount: e[1].reduce((a, b) => a + Number(b.minus_balance), 0),
        }
      })
    } finally {
      creditRestLoading.value = false
    }
  }

  /* 金额转换 */
  const formatCurrency = (value: any, currencyId: number) => {
    if (value === null || value === undefined) return ''
    const number = parseFloat(value)
    if (isNaN(number)) return ''

    return new Intl.NumberFormat(locale.value, {
      style: 'currency',
      currencyDisplay: 'code',
      currency: currencyType.value[currencyId],
      notation: 'compact',
      compactDisplay: 'long',
      minimumFractionDigits: 3,
      roundingMode: 'halfFloor',
    }).format(value)
  }
  /* 改变状态 */
  const changeStatus = async (id: number) => {
    statusId.value = id
    switch (id) {
      case 0:
        prepayParams.value.dataLevel = ''
        break
      case 1:
        prepayParams.value.dataLevel = 1
        break
      case 2:
        prepayParams.value.dataLevel = 2
        break

      default:
        break
    }
    getPrepayData(prepayParams.value)
  }

  /* 表单搜索 */
  const onSubmit = () => {
    projectHook.activeProjectId = Number(projectId.value)
    prepayParams.value.companyProject = projectId.value
    Promise.all([
      getPrepayData(prepayParams.value),
      getBankRest({
        projectId: projectHook.activeProjectId,
      }),
      getLoanlimit({
        projectId: projectHook.activeProjectId,
      }),
    ])
  }

  /* 查看详情 */
  const lookDetail = (type: any) => {
    const info = {
      type,
      projectId: projectHook.activeProjectId,
    }
    router.push({ path: '/capital/detail', state: { info: JSON.stringify(info) } })
  }

  onMounted(async () => {
    await nsListLabelRaw()
    await getCurrencyData()
    Promise.all([
      getPrepayData(prepayParams.value),
      getBankRest({
        projectId: projectHook.activeProjectId,
      }),
      getLoanlimit({
        projectId: projectHook.activeProjectId,
      }),
    ])
  })
  return {
    projectId,
    currencyType,
    projectList,
    formatCurrency,
    statusId,
    dateTabs,
    changeStatus,
    cardList,
    onSubmit,
    loading,
    lookDetail,
  }
}

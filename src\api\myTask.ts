import { get } from '@/utils/request'

/* * 获取待办任务 */
export const taskTodo = (data: { id: string; pageNum: number; pageSize: number }) =>
  get<{ list: any[]; total: number }>(`/admin/task/list/todo/${data.id}`, {
    pageNum: data.pageNum,
    pageSize: data.pageSize,
  })
/* * 获取已办任务 */
export const taskDone = (data: { id: string; pageNum: number; pageSize: number }) =>
  get<{ list: any[]; total: number }>(`/admin/task/list/all/${data.id}`, {
    pageNum: data.pageNum,
    pageSize: data.pageSize,
  })

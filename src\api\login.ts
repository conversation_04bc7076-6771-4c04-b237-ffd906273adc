import request, { post } from '@/utils/request'

const Api = {
  captcha: '/auth/code',
  login: '/auth/basic/scm-mobile/login',
  info: '/auth/getInfo',
  routes: '/auth/scm/getMobileViewAccess',
}

/* 验证码 */
export function getCaptcha() {
  return request({
    url: Api.captcha,
    method: 'get',
  })
}

/* 用户登录 */
export function userLogin(data: Record<string, unknown>) {
  return request({
    url: Api.login,
    method: 'post',
    data,
  })
}

/* 用户信息 */
export function userInfo() {
  return request({
    url: Api.info,
    method: 'get',
  })
}

// 获取路由
export function getRouters() {
  return request({
    url: Api.routes,
    method: 'get',
  })
}

/* 上传资质证明 */
export const uploadFIle = (file: FormData) => post<string>(`/scm/oss/v2/upload/deliveryDocuments`, file)

export type UserBasicInfo = Nullable<{
  userId: number
  userName: string
  nickName: string
  phoneNumber: string
  email: string
}>
export type UserCpInfo = Nullable<{
  userId: string
  name: string
  mobile: string
  email: string
}>
export type UserMpInfo = Nullable<{
  userId: string
  nickName: string
  memoName: string
  mainMpOpenId: string
  tmsMaOpenId: string
}>
export type StaffPostList = Nullable<
  {
    postId: number
    positionKey: string
    positionName: string
    subsidiaryId: Nullable<number[]>
    allSubsidiary: boolean
    deptId: Nullable<number>
    isManagerPosition: boolean
    dataScope: string
  }[]
>

export type UserEntityList = Nullable<
  {
    id: number
    code: string
    name: string
    serialNo: string
    subsidiaryIds: number[]
  }[]
>

export function redirectWxCpLogin() {
  location.replace(
    import.meta.env.VITE_APP_BASE_API + '/auth/wx/cp/portal/scm/redirect-login?redirect=' + location.href,
  )
}

export function wxCpLogin(code: string): Promise<any> {
  return request({
    url: '/auth/wx/cp/portal/scm/login',
    method: 'get',
    params: {
      code,
    },
  })
}

export function redirectWxMpLogin() {
  location.replace(import.meta.env.VITE_APP_BASE_API + '/auth/wx/mp/portal/redirect-login?redirect=' + location.href)
}

export function wxMpLogin(code: string): Promise<any> {
  return request({
    url: '/auth/wx/mp/portal/login',
    method: 'get',
    params: {
      code,
    },
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post',
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword: string, newPassword: string) {
  const data = {
    oldPassword,
    newPassword,
  }
  return request({
    url: '/admin/system/user/profile/updatePwd',
    method: 'put',
    params: data,
  })
}

/* 提交注册 */
export function register(data: object) {
  return request({
    url: '/admin/system/entity/reg-app',
    method: 'post',
    data,
  })
}

/* 查询当前用户是否已经注册申请 */
export function userRegistered(userId: number) {
  return request({
    url: `/admin/system/entity/reg-app/${userId}`,
    method: 'get',
  })
}

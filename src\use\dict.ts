import { reactive } from 'vue'
import { getDicts, getNsList } from '@/api/dict'
import useDictStore from '@/store/modules/dict'
/**
 * 获取字典数据
 */
export const useDict = (...args: string[]): { [key: string]: DictDataOption[] } => {
  const res = reactive<{
    [key: string]: DictDataOption[]
  }>({})
  return (() => {
    args.forEach(async (dictType) => {
      res[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res[dictType] = dicts
      } else {
        await getDicts(dictType).then((resp) => {
          res[dictType] = resp.data.map(
            (p): DictDataOption => ({
              label: p.dictLabel,
              value: p.dictValue,
              elTagType: p.listClass,
              elTagClass: p.cssClass,
              remark: p.remark,
            }),
          )
          useDictStore().setDict(dictType, res[dictType])
        })
      }
    })
    return res
  })()
}

export const useDictLabel = (...args: string[]): { [key: string]: Record<string, string> } => {
  const res = reactive<{
    [key: string]: Record<string, string>
  }>({})
  return (() => {
    args.forEach(async (dictType) => {
      let dicts = useDictStore().getDict(dictType)
      if (!dicts) {
        const dictRes = await getDicts(dictType)
        dicts = (dictRes.data || []).map(
          (p): DictDataOption => ({
            label: p.dictLabel,
            value: p.dictValue,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
            remark: p.remark,
          }),
        )
        useDictStore().setDict(dictType, dicts)
      }
      res[dictType] = dicts.reduce((prev, curr) => {
        prev[curr.value] = curr.label
        return prev
      }, {} as Record<string, string>)
    })
    return res
  })()
}

export const useDictLabelRaw = async (...args: string[]): Promise<{ [key: string]: Record<string, string> }> => {
  const dictEntries = await Promise.all(
    args.map(async (dictType) => {
      let dicts = useDictStore().getDict(dictType)
      if (!dicts) {
        const dictRes = await getDicts(dictType)
        dicts = (dictRes.data || []).map(
          (p): DictDataOption => ({
            label: p.dictLabel,
            value: p.dictValue,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
            remark: p.remark,
          }),
        )
        useDictStore().setDict(dictType, dicts)
      }
      return [
        dictType,
        dicts.reduce((prev, curr) => {
          prev[curr.value] = curr.label
          return prev
        }, {} as Record<string, string>),
      ]
    }),
  )
  return Object.fromEntries(dictEntries)
}

/**
 * 获取字典数据
 */
export const useNsList = (...args: string[]): { [key: string]: DictDataOption[] } => {
  const res = reactive<{
    [key: string]: DictDataOption[]
  }>({})
  return (() => {
    args.forEach(async (dictType) => {
      res[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res[dictType] = dicts
      } else {
        await getNsList(dictType).then((resp) => {
          res[dictType] = resp.data.map(
            (p): DictDataOption => ({
              label: p.listLabel,
              value: p.listValue,
            }),
          )
          useDictStore().setDict(dictType, res[dictType])
        })
      }
    })
    return res
  })()
}

export const useNsListLabel = (...args: string[]): { [key: string]: Record<string, string> } => {
  const res = reactive<{
    [key: string]: Record<string, string>
  }>({})
  return (() => {
    args.forEach(async (dictType) => {
      let dicts = useDictStore().getDict(dictType)
      if (!dicts) {
        const dictRes = await getNsList(dictType)
        dicts = (dictRes.data || []).map(
          (p): DictDataOption => ({
            label: p.listLabel,
            value: p.listValue,
          }),
        )
        useDictStore().setDict(dictType, dicts)
      }
      res[dictType] = dicts.reduce((prev, curr) => {
        prev[curr.value] = curr.label
        return prev
      }, {} as Record<string, string>)
    })
    return res
  })()
}

export const useNsListRaw = async (...args: string[]): Promise<{ [key: string]: DictDataOption[] }> => {
  const dictEntries = await Promise.all(
    args.map(async (dictType) => {
      let dicts = useDictStore().getDict(dictType)
      if (!dicts) {
        const dictRes = await getNsList(dictType)
        dicts = (dictRes.data || []).map(
          (p): DictDataOption => ({
            label: p.listLabel,
            value: p.listValue,
          }),
        )
        useDictStore().setDict(dictType, dicts)
      }
      return [dictType, dicts]
    }),
  )
  return Object.fromEntries(dictEntries)
}

export const useNsListLabelRaw = async (...args: string[]): Promise<{ [key: string]: Record<string, string> }> => {
  const dictEntries = await Promise.all(
    args.map(async (dictType) => {
      let dicts = useDictStore().getDict(dictType)
      if (!dicts) {
        const dictRes = await getNsList(dictType)
        dicts = (dictRes.data || []).map(
          (p): DictDataOption => ({
            label: p.listLabel,
            value: p.listValue,
          }),
        )
        useDictStore().setDict(dictType, dicts)
      }
      return [
        dictType,
        dicts.reduce((prev, curr) => {
          prev[curr.value] = curr.label
          return prev
        }, {} as Record<string, string>),
      ]
    }),
  )
  return Object.fromEntries(dictEntries)
}

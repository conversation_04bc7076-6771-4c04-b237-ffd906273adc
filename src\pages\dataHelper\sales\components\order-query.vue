<script setup lang="ts">
  import { executedOrder, executingOrder } from '@/api/data/customer'
  import { useUserStore } from '@/store'
  import { init, type EChartsType } from 'echarts'
  import { useYearDataStore } from '../../purchase/hooks/dataYearStore'
  import { executingOrderBarChartConfig, executedOrderBarChartConfig } from '../config'
  import { divideScale, parseNumber } from '@/utils/digit'
  import Empty from '@/components/Empty/index.vue'
  const { t } = useI18n()
  const $t = t

  const chartState: Record<string, Nullable<EChartsType>> = {
    executingChart: null,
    executedChart: null,
  }
  const { slectVal, yearStart, yearEnd } = storeToRefs(useYearDataStore())

  const executingDataLoading = ref(false)
  const executingPageData = ref<any>()
  const executingTotalReport = ref<{ totalQua: number; waitQua: number }>()
  const executedDataLoading = ref(false)
  const executedPageData = ref<any>()
  const executedTotalReport = ref<{ totalQua: number; waitQua: number }>()
  const executingEmpty = ref(false)
  const executedEmpty = ref(false)

  const globalStore = useUserStore()

  const loadExecutingOrder = async () => {
    if (!globalStore.customerIds?.length) return
    executingDataLoading.value = true
    executingEmpty.value = false
    const queryParams: any = {
      startDate: yearStart.value,
      endDate: yearEnd.value,
      customerIdList: globalStore.customerIds,
    }
    const res = await executingOrder(queryParams)
    if (!(res.data && res.data.length)) {
      executingEmpty.value = true
      executingDataLoading.value = false
      return
    }
    const categories: string[] = []
    const quantitySeries: number[] = []
    const waitQuaSeries: any[] = []
    let totalQua = 0
    let waitQua = 0
    res.data.forEach((d: any) => {
      categories.push(d.item_name)
      const quantity = parseNumber(d.item_quantity)
      const quantitywaitship = parseNumber(d.rest_quantity)
      quantitySeries.push(divideScale(quantity, 1000, 2))
      waitQuaSeries.push(divideScale(quantitywaitship, 1000, 2))
      totalQua += quantity
      waitQua += quantitywaitship
    })
    const series = [quantitySeries, waitQuaSeries]
    executingPageData.value = { categories, series }
    executingTotalReport.value = {
      totalQua: divideScale(totalQua, 1000, 2),
      waitQua: divideScale(waitQua, 1000, 2),
    }
    executingDataLoading.value = false
  }

  const loadExecutedOrder = async () => {
    if (!globalStore.customerIds?.length) return
    executedDataLoading.value = true
    executedEmpty.value = false
    const queryParams: any = {
      startDate: yearStart.value,
      endDate: yearEnd.value,
      customerIdList: globalStore.customerIds,
    }
    const res = await executedOrder(queryParams)
    if (!(res.data && res.data.length)) {
      executedEmpty.value = true
      return
    }
    const categories: string[] = []
    const quantitySeries: any[] = []
    const waitQuaSeries: any[] = []
    const billedQuaSeries: any[] = []
    let totalQua = 0
    let waitQua = 0
    let billedQua = 0
    res.data.forEach((d: any) => {
      categories.push(d.item_name)
      const quantity = parseNumber(d.item_quantity)
      const quantitywaitship = parseNumber(d.rest_quantity)
      const quantitybilled = parseNumber(d.billed_quantity)
      quantitySeries.push(divideScale(quantity, 1000, 2))
      waitQuaSeries.push(divideScale(quantitywaitship, 1000, 2))
      billedQuaSeries.push(divideScale(quantitybilled, 1000, 2))
      totalQua += quantity
      waitQua += quantitywaitship
      billedQua += quantitybilled
    })
    const series = [quantitySeries, billedQuaSeries]
    executedPageData.value = { categories, series }
    executedTotalReport.value = {
      totalQua: divideScale(totalQua, 1000, 2),
      waitQua: divideScale(waitQua, 1000, 2),
    }
    executedDataLoading.value = false
  }

  const executingChartRef = useTemplateRef<HTMLDivElement>('executingChart')
  const executedChartRef = useTemplateRef<HTMLDivElement>('executedChart')

  const createExecutingBarChart = (categories: string[], series: number[][]) => {
    let chart = chartState.executingChart
    if (!chart) {
      chart = init(executingChartRef.value)
      chartState.executingChart = chart
    }
    chart.setOption(executingOrderBarChartConfig(categories, series, $t))
  }
  const createExecutedBarChart = (categories: string[], series: number[][]) => {
    let chart = chartState.executedChart
    if (!chart) {
      chart = init(executedChartRef.value)
      chartState.executedChartRef = chart
    }
    chart.setOption(executedOrderBarChartConfig(categories, series, $t))
  }

  watchEffect(() => {
    if (executingPageData.value)
      createExecutingBarChart(executingPageData.value.categories, executingPageData.value.series)
    else if (chartState.executingChart) {
      chartState.executingChart.dispose()
      chartState.executingChart = null
    }
  })

  watchEffect(() => {
    if (executedPageData.value) createExecutedBarChart(executedPageData.value.categories, executedPageData.value.series)
    else if (chartState.executedChart) {
      chartState.executedChart.dispose()
      chartState.executedChart = null
    }
  })

  const loadData = async () => {
    await nextTick()
    loadExecutingOrder()
    loadExecutedOrder()
  }
  onMounted(() => loadData())
  watch(
    () => slectVal.value,
    () => loadData(),
  )
</script>

<template>
  <div class="page-container">
    <div class="chart-card">
      <div class="chart-title">
        <div class="title-icon">
          <!-- <Icon icon="mdi-light:chart-bar" width="1.2em" height="1.2em" style="color: #006c5d" /> -->
        </div>
        <div class="title-content">{{ $t('履行中订单') }}</div>
      </div>
      <t-loading size="small" :loading="executingDataLoading">
        <div class="chart-content">
          <Empty v-if="executingEmpty" :description="$t('暂无数据')" />
          <template v-else>
            <div ref="executingChart" class="echart-line-container"></div>
            <div class="chart-summary"></div>
            <div class="chart-summary"></div>
          </template>
        </div>
      </t-loading>
    </div>
    <div class="chart-card">
      <div class="chart-title">
        <div class="title-icon">
          <!-- <Icon icon="mdi-light:chart-bar" width="1.2em" height="1.2em" style="color: #006c5d" /> -->
        </div>
        <div class="title-content">{{ $t('已完结订单') }}</div>
      </div>
      <t-loading size="small" :loading="executedDataLoading">
        <div class="chart-content">
          <Empty v-if="executedEmpty" :description="$t('暂无数据')" />
          <template v-else>
            <div ref="executedChart" class="echart-line-container"></div>
            <div class="chart-summary"></div>
            <div class="chart-summary"></div>
          </template>
        </div>
      </t-loading>
    </div>
  </div>
</template>

<style scoped lang="less">
  @import url(../styles/common.less);
</style>

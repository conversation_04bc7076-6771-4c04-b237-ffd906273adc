/* eslint-disable no-lonely-if */
// import { MessagePlugin } from 'tdesign-vue-next';
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth'
import { vueI18n } from '@/i18n'

import { getUserStore } from '@/store'
import router from '@/router'

NProgress.configure({ showSpinner: false })

const loginPath = 'login'
const whitelist = ['/login', '/vn-raw-procure-rfq-anno']

router.beforeEach((to, from, next) => {
  NProgress.start()

  const userStore = getUserStore()
  if (getToken()) {
    if (whitelist.indexOf(to.path) !== -1) {
      next({ path: '/' })
    } else {
      if (userStore.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        userStore
          .getUserInfo()
          .then(() => {
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
          .catch(() => {
            userStore.logout().then(() => {
              next({ path: '/' })
            })
          })
      } else {
        next()
      }
    }
  } else {
    // 没有token
    if (to.path.indexOf(loginPath) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next({ path: `/${loginPath}` }) // 否则全部重定向到登录页
    }
  }
})

router.afterEach((to) => {
  if (to?.meta?.title) document.title = vueI18n.global.t(to.meta.title as any)
  if (to.path === '/login') {
    const userStore = getUserStore()
    userStore.logout()
  }
  NProgress.done()
})

// 监听语言变化，更新标题
watch(
  () => vueI18n.global.locale.value,
  () => {
    const title = router.currentRoute.value.meta?.title
    if (title) {
      document.title = vueI18n.global.t(title as string)
    }
  },
)

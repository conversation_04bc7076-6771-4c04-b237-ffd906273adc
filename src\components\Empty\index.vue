<script setup lang="ts">
  defineProps<{
    description?: string
  }>()

  const { t } = useI18n()
  const $t = t
</script>

<template>
  <div class="shell">
    <img src="https://telegram.honoroad.com.cn/honoroad_static/tabbar/empty.png" alt="" />
    <p>{{ description || $t('暂无信息') }}</p>
  </div>
</template>

<style scoped lang="less">
  .shell {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #969799;
    font-size: 14px;
    > img {
      width: 190px;
      height: 190px;
    }
  }
</style>

export const thousandBitSeparatorUtil = (sourceStr: string = '') => {
  if (sourceStr) {
    let result = ''
    let realContent = ''
    if (sourceStr.indexOf('.') === -1) {
      realContent = sourceStr
    } else {
      const [content] = sourceStr.split('.')
      realContent = content
    }
    if (realContent.indexOf(',') !== -1) {
      realContent = realContent.split(',').join('')
    }
    const len = realContent.length - 1
    for (let i = len; i >= 0; i--) {
      const val = realContent[i]
      result = `${(len - i + 1) % 3 === 0 && i !== 0 ? ',' : ''}${val}${result}`
    }
    return result
  }
  return ''
}

export const getdatestringdmy = (dateStr: string = '') => {
  if (dateStr) {
    var datelist = dateStr.split('-')
    return datelist[2] + '/' + datelist[1] + '/' + datelist[0]
  } else {
    return null
  }
}

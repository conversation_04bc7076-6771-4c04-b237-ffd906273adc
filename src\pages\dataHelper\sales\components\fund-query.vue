<script setup lang="ts">
  import { init, type EChartsType } from 'echarts'
  import Empty from '@/components/Empty/index.vue'
  import { useYearDataStore } from '../../purchase/hooks/dataYearStore'
  import { useUserStore } from '@/store'

  import { fundBarChartConfig } from '../config'
  import { getPrepayTotal } from '@/api/capital'
  import { groupBy } from 'lodash-es'
  import { useNsListLabelRaw } from '@/use/dict'
  const { t, locale } = useI18n()
  const $t = t

  const chartState: Record<string, Nullable<EChartsType[]>> = {
    fundChart: null,
  }
  const { slectVal, yearStart, yearEnd } = storeToRefs(useYearDataStore())
  const globalStore = useUserStore()
  const fundQueryChartRef = useTemplateRef<HTMLDivElement[]>('fundQueryChart')
  const loading = ref(false)
  const pageData = ref<any>()
  const dataEmpty = ref(false)

  /* 货币类型 */
  const currencyType = ref<any>({})
  /* 获取字典label */
  const nsListLabelRaw = async () => {
    const { currency } = await useNsListLabelRaw('currency')
    currencyType.value = currency
  }

  const queryParams = computed(() => ({
    sumCols: ['occupy_amount'],
    groupCols: ['risk_category', 'control_point', 'currency_id', 'entity_id', 'entity_name'],
    riskCategory: '3',
    normal: true,
    dataDateStart: yearStart.value,
    dataDateEnd: yearEnd.value,
    entityIdList: globalStore.customerIds,
  }))

  const loadCustomerData = async () => {
    const res = await getPrepayTotal(queryParams.value)
    const currencyGroup = groupBy(res.data, 'currencyId')

    if (!res.data || !res.data.length) {
      dataEmpty.value = true
      loading.value = false
      return
    }
    return Object.entries(currencyGroup).map((d) => {
      const currencyData = d[1]
      const entityGroup = groupBy(currencyData, 'entityId')
      const controlPointGroup = groupBy(currencyData, 'controlPoint')
      const categories = Object.values(entityGroup).map((g) => g[0].entityName)
      const emptySerie: number[] = []
      emptySerie.length = categories.length
      emptySerie.fill(0)
      return {
        currency: currencyType.value[d[0]],
        categories,
        series: [
          controlPointGroup['30004']?.map((i) => Number(i.occupyAmount)) || emptySerie,
          controlPointGroup['30003']?.map((i) => Number(i.occupyAmount)) || emptySerie,
        ],
      }
    })
  }

  const loadData = async () => {
    await nextTick()
    if (!globalStore.customerIds?.length) return
    loading.value = true
    const groupResult = await loadCustomerData()

    if (!groupResult) {
      return
    }

    pageData.value = groupResult || []
    loading.value = false
  }

  const createFundBarChart = () => {
    let chart = chartState.fundChart
    if (!chart?.length) {
      chart = fundQueryChartRef.value?.map((c) => init(c)) || []
      chartState.fundChart = chart
    }
    chart.forEach((c, i) => {
      c.setOption(
        fundBarChartConfig(
          pageData.value[i].currency,
          pageData.value[i].categories,
          pageData.value[i].series,
          $t,
          locale,
        ),
      )
    })
  }

  watchEffect(() => {
    if (pageData.value) createFundBarChart()
    else if (chartState.fundChart) {
      chartState.fundChart.forEach((c) => c.dispose())
      chartState.fundChart = null
    }
  })

  onMounted(async () => {
    await nsListLabelRaw()
    loadData()
  })
  watch(
    () => slectVal.value,
    () => loadData(),
  )
</script>

<template>
  <div class="page-container">
    <div class="chart-card">
      <div class="chart-title">
        <div class="title-icon">
          <!-- <Icon icon="mdi-light:chart-bar" width="1.2em" height="1.2em" style="color: #006c5d" /> -->
        </div>
        <div class="title-content">{{ $t('应收应付') }}</div>
      </div>
      <t-loading size="small" :loading="loading">
        <div class="chart-content">
          <Empty v-if="dataEmpty" :description="$t('暂无数据')" />
          <template v-else>
            <div v-for="c in pageData" :key="c.currency" ref="fundQueryChart" class="echart-line-container">
              {{ c.currency }}
            </div>
          </template>
        </div>
      </t-loading>
    </div>
  </div>
</template>

<style scoped lang="less">
  @import url(../styles/common.less);
</style>

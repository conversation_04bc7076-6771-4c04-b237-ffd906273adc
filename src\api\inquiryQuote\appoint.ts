import { get, put } from '@/utils/request'

/** 查询当前时段是否有预约，是否领号 */
export const appointMonth = (itemId: string, dateStart: string, dateEnd: string) => {
  return get<any[]>(`/scm/rfqAppoint`, {
    itemId,
    appointTimeStart: dateStart,
    appointTimeEnd: dateEnd,
  })
}

/** 刷新队列 */
export const refreshAppoint = (appointId: any) => {
  return get<any>(`/scm/rfqAppoint/refresh-serial`, {
    id: appointId,
  })
}

/* 取消预约 */
export const cancelAppoint = (id: number, contractId: number) =>
  put(`/scm/ns/connect/procureCancel/11061`, { id, contractId, needNotice: true })

export const checkoutAppoint = (appointId: any) => {
  return get<any>(`/scm/rfqAppoint/checkout/${appointId}`)
}

/** 查询当前时段是否有预约，是否领号 */
export const vehicleDateAppoint = (vehicleNo: any, date: any) => {
  return get<any>(`/scm/rfqAppoint/today`, {
    vehicleNo,
    appointTime: date,
  })
}

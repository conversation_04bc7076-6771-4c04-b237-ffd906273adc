import { useYearDataStore } from '../hooks/dataYearStore'
import { computed, ref } from 'vue'

const timeEffect = ($t: any) => {
  const { slectVal } = storeToRefs(useYearDataStore())

  const timeType = ref<string>('2')
  const showTimeTypeSelect = computed(() => slectVal.value !== 'all')

  const TimeTypeSelect = [
    { label: $t('季度'), value: '2' },
    { label: $t('月'), value: '3' },
  ]

  return {
    timeType,
    TimeTypeSelect,
    showTimeTypeSelect,
  }
}

export default timeEffect

import { PrimaryTableCol, TableRowData, FormInstanceFunctions, FormProps } from 'tdesign-vue-next'
import { getFileListApi, createWeComDoc, deleteFileApi, renameFileApi, wxCpUserList } from '@/api/myFile'
import { pinyin } from 'pinyin-pro'

export default () => {
  const { t: $t } = useI18n()

  const router = useRouter()

  /* 表格配置 */
  const columns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'docName', title: $t('文件名称'), width: '100', ellipsis: true },
    { colKey: 'docType', title: $t('文件类型'), width: '100', ellipsis: true },
    { colKey: 'createTime', title: $t('创建时间'), width: '100', ellipsis: true },
    { colKey: 'updateTime', title: $t('更新时间'), width: '100', ellipsis: true },
    {
      title: $t('操作'),
      colKey: 'link',
      fixed: 'right',
      ellipsis: true,
      width: 140,
    },
  ])

  /* 表格分页 */
  const pagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  /*文件列表 */
  const fileList = ref<any[]>([])

  /* 企业微信人列表 */
  const userList = ref<any[]>([])

  /* 选中的企业微信联系人 */
  const selectedIds = ref([])

  /* 新建文档form实列 */
  const form = ref<FormInstanceFunctions>(null)

  /* 新建文档form数据 */
  const formData: FormProps['data'] = reactive({
    docName: '',
    docType: '4',
    adminUsers: [] as string[],
  })

  /* 新建文档form校验规则 */
  const rules = computed<FormProps['rules']>(() => ({
    docName: [
      {
        required: true,
        message: $t('请输入文件名称'),
        type: 'error',
        trigger: 'blur',
      },
    ],
    docType: [
      { required: true, message: $t('请选择文件类型'), type: 'error', trigger: 'blur' },
      { required: true, message: $t('请选择文件类型'), type: 'error', trigger: 'change' },
    ],
  }))

  /* 重命名form实列 */
  const renameForm = ref<FormInstanceFunctions>(null)

  /* 重命名form数据 */
  const renameFormData: FormProps['data'] = reactive({
    docId: '',
    newName: '',
  })

  /* 重命名form校验规则 */
  const renameRules = computed<FormProps['rules']>(() => ({
    newName: [
      {
        required: true,
        message: $t('请输入文件名称'),
        type: 'error',
        trigger: 'blur',
      },
    ],
  }))

  /* loading */
  const loading = ref<boolean>(false)
  const renameLoading = ref<boolean>(false)
  const confirmLoading = ref<boolean>(false)

  /* 新建文档弹窗 */
  const visible = ref<boolean>(false)

  /* 重命名弹窗 */
  const renameVisible = ref<boolean>(false)

  /* 企业微信联系人弹窗 */
  const wxCpUserVisible = ref<boolean>(false)

  /* 获取表格数据 */
  const getFileList = async () => {
    try {
      loading.value = true
      const res = await getFileListApi({
        pageSize: pagination.value.defaultPageSize,
        pageNum: pagination.value.defaultCurrent,
      })
      pagination.value.total = res.data.total
      fileList.value = res.data.records
      loading.value = false
    } catch (error: any) {
      loading.value = false
      MessagePlugin.error(`获取文件列表失败: ${error.errmsg || error.message}`)
    }
  }

  /* 取消 */
  const cancelForm = () => {
    form.value.reset()
    selectedIds.value = []
  }

  /* 确认新建 */
  const confirmForm = async () => {
    const res = await form.value.validate()
    if (res === true) {
      try {
        confirmLoading.value = true
        const { data } = await createWeComDoc(formData)
        confirmLoading.value = false
        getFileList()
        toViewFile(data)
      } catch (error) {
        console.error('创建文档失败', error)
      } finally {
        loading.value = false
        visible.value = false
      }
    }
  }

  /* 删除文档 */
  const deleteFile = async (row: any) => {
    try {
      await deleteFileApi(row.docId)
      MessagePlugin.success($t('删除成功'))
      getFileList()
    } catch (error) {}
  }

  /* 重命名文档 */
  const renameFile = (row: any) => {
    renameVisible.value = true
    renameFormData.docId = row.docId
  }

  /* 重命名取消 */
  const cancelRenameForm = () => renameForm.value.reset()

  /* 重命名确认 */
  const confirmRenameForm = async () => {
    const res = await renameForm.value.validate()
    if (res === true) {
      try {
        renameLoading.value = true
        await renameFileApi(renameFormData)
        MessagePlugin.success($t('操作成功'))
        getFileList()
      } catch (error) {
        console.error('操作失败', error)
      } finally {
        loading.value = false
        renameVisible.value = false
      }
    }
  }

  /* 查看文档 */
  const toViewFile = (row: any) => {
    if (row.docType === 4) {
      router.push(`/myFiles/sheet?docId=${row.docId}`)
    } else {
      router.push(`/myFiles/docs?docId=${row.docId}`)
    }
  }

  /* 获取中文名字的拼音首字母的函数 */
  const getFirstLetter = (str: string) => {
    const firstLetter = pinyin(str, {
      pattern: 'first',
      toneType: 'none',
      type: 'array',
    })
    return firstLetter[0]
  }

  /* 获取企业微信联系人列表 */
  const getUserList = async () => {
    try {
      const res = await wxCpUserList()
      userList.value = res.rows
        .filter((it: any) => it.name)
        .reduce((acc: any, curr: any) => {
          const firstChar = getFirstLetter(curr.name).toUpperCase()
          let group = acc.find((item: any) => item.index === firstChar)
          if (!group) {
            group = { index: firstChar, children: [] }
            acc.push(group)
          }
          const temp = {
            name: curr.name,
            userId: curr.userId,
            avatar: curr.avatar,
          }
          group.children.push(temp)
          return acc
        }, [])
        .sort((a: any, b: any) => a.index.localeCompare(b.index))
    } catch (error) {}
  }

  /* 企业微信联系人弹窗取消 */
  const handleCancel = () => {
    wxCpUserVisible.value = false
  }

  /* 企业微信联系人弹窗确认 */
  const handleConfirm = () => {
    wxCpUserVisible.value = false
    formData.adminUsers = selectedIds.value
  }

  onMounted(async () => {
    getFileList()
    getUserList()
  })

  return {
    form,
    formData,
    rules,
    visible,
    columns,
    pagination,
    fileList,
    loading,
    cancelForm,
    confirmForm,
    confirmLoading,
    deleteFile,
    toViewFile,
    renameFile,
    renameVisible,
    cancelRenameForm,
    confirmRenameForm,
    renameForm,
    renameFormData,
    renameRules,
    renameLoading,
    wxCpUserVisible,
    userList,
    selectedIds,
    handleCancel,
    handleConfirm,
  }
}

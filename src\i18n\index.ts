import { createI18n } from 'vue-i18n'

import vi from './lang/vi'
import zh from './lang/zh'
import en from './lang/en'
import ru from './lang/ru'
// 引入英文语言包
export const localeKey = 'scm-lang'
import { useLocalStorage } from '@vueuse/core'
// 获取浏览器语言
const browserLanguage = navigator.language.split('-')[0] || navigator.languages[0].split('-')[0]
// localStorage.setItem('scm-app-lang', browserLanguage)
const language = useLocalStorage(localeKey, 'zh')
// 优先使用localStorage中的语言，如果没有则使用浏览器的语言
const selectedLanguage = language.value || browserLanguage || 'zh' // 默认使用'zh'

export const vueI18n = createI18n({
  legacy: false,
  locale: selectedLanguage,
  messages: {
    en: { ...en },
    zh: { ...zh },
    vi: { ...vi },
    ru: { ...ru },
  },
  missingWarn: false,
  fallbackWarn: false,
  fallbackLocale: 'zh',
})

import { get, post, remove } from '@/utils/request'

/* 获取现有库尊总量 */
export const positionTotal = (positionProject: number, activeBomList?: number[]) =>
  get('/tms/position/statement', { positionProject, activeBomList })

/* 获取库分布列表 */
export const getOwnedWarehouseList = (projectId: number) =>
  get('/scm/ns/transfer/602/getInventoryDetail', { projectId })
/* 自有库详情 */
export const getGoods = (companyProject: number, binId?: string) =>
  get('/scm/inventoryInfo/inventory-max-age', { companyProject, binId })
/* 外租库详情 */
export const getRent = (companyProject: number) => get('/tms/tempBin/bin/all', { companyProject })
/* 获取帐龄列表 */
export const agingList = (params: object) => get('/admin/warn/data/list', params)
/* 生效配方列表 */
export const loadBomByProject = (projectId: number) => get<any[]>(`tms/position/bom/${projectId}`)
/* 编辑配方 */
export const edit = (params: any) => post<any>('/tms/position/bom', params)
/*  配方详情 */
export const detail = (id: number) => get(`/tms/position/bom/detail/${id}`)
/* 删除配方 */
export const removeRecipe = (id: number) => remove(`/tms/position/bom/${id}`)

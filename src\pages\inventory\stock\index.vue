<script setup lang="ts">
  import useStock from './hooks'
  const { t } = useI18n()
  const $t = t
  const {
    form,
    projectList,
    loading,
    finishedGoods,
    rawMaterials,
    onsubmit,
    onReset,
    ownedWarehouse,
    offsiteWarehouse,
    projectId,
    ownedWarehouseList,
    ownedWarehouseSearch,
    offsiteWarehouseId,
    offsiteWarehouseList,
    offsiteWarehousSearch,
    stock,
    stockId,
    stockList,
    stockSearch,
  } = useStock()
</script>
<template>
  <div class="container">
    <t-card>
      <t-form label-width="auto" layout="inline" @submit="onsubmit" @reset="onReset">
        <t-form-item :label="$t('项目名称')">
          <t-radio-group variant="primary-filled" v-model="form.projectId" @change="onsubmit">
            <t-radio-button v-for="p in projectList" :value="p.value">{{ p.text }}</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item>
          <t-button size="small" theme="primary" type="submit">{{ $t('查询') }}</t-button>
          <t-button size="small" theme="default" type="reset">{{ $t('重置') }}</t-button>
        </t-form-item>
      </t-form>
      <div class="chart-wrap">
        <t-loading :loading="loading" show-overlay>
          <div class="pie-chart">
            <h1 class="title">{{ $t('现有库存总量') }}（T）</h1>
            <t-row :gutter="16">
              <t-col :span="6">
                <div ref="finishedGoods" class="chart"></div>
              </t-col>
              <t-col :span="6">
                <div ref="rawMaterials" class="chart"></div>
              </t-col>
            </t-row>
          </div>
          <div class="bar-chart">
            <div class="select">
              <div>
                <h1>{{ $t('自有库分布&账龄') }}</h1>
                <!-- <t-select v-model="projectId" :options="ownedWarehouseList" /> -->
                <t-radio-group
                  variant="primary-filled"
                  v-model="projectId"
                  @change="ownedWarehouseSearch"
                  style="max-width: 35vw"
                >
                  <t-radio-button v-for="p in ownedWarehouseList" :value="p.value">{{ p.label }}</t-radio-button>
                </t-radio-group>
                <t-button size="small" theme="primary" @click="ownedWarehouseSearch">{{ $t('查询') }}</t-button>
              </div>
              <div>
                <h1>{{ $t('外租库分布&账龄') }}</h1>
                <!-- <t-select v-model="offsiteWarehouseId" :options="offsiteWarehouseList" /> -->
                <t-radio-group variant="primary-filled" v-model="offsiteWarehouseId" @change="offsiteWarehousSearch">
                  <t-radio-button v-for="p in offsiteWarehouseList" :value="p.value">{{ p.label }}</t-radio-button>
                </t-radio-group>
                <t-button size="small" theme="primary" @click="offsiteWarehousSearch">{{ $t('查询') }}</t-button>
              </div>
            </div>
            <t-row :gutter="16">
              <t-col :span="6">
                <div ref="ownedWarehouse" class="chart-bar"></div>
              </t-col>
              <t-col :span="6">
                <div ref="offsiteWarehouse" class="chart-bar"></div>
              </t-col>
            </t-row>
          </div>
          <div class="select">
            <div>
              <h1>{{ $t('库存分布') }}（T）</h1>
              <!-- <t-select v-model="stockId" :options="stockList" :keys="{ label: 'itemName', value: 'itemNo' }" /> -->
              <t-radio-group variant="primary-filled" v-model="stockId" @change="stockSearch">
                <t-radio-button v-for="p in stockList" :value="p.itemNo">{{ p.itemName }}</t-radio-button>
              </t-radio-group>
              <t-button size="small" theme="primary" @click="stockSearch">{{ $t('查询') }}</t-button>
            </div>
          </div>
          <div ref="stock" class="stock-chart"></div>
        </t-loading>
      </div>
    </t-card>
  </div>
</template>
<style scoped lang="less">
  .chart-wrap {
    margin-top: 40px;
    .title {
      margin-bottom: 20px;
    }
    .chart {
      width: 100%;
      height: 400px;
    }
    .bar-chart {
      margin: 40px 0;
      .chart-bar {
        width: 100%;
        height: 480px;
      }
    }
    .select {
      width: 100%;
      display: flex;
      column-gap: 16px;
      > div {
        flex: 1;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        column-gap: 16px;
      }
      .t-select__wrap {
        width: 160px;
      }
    }
    .stock-chart {
      width: 400px;
      height: 400px;
      margin: 0 auto;
    }
  }
</style>

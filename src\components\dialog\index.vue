<script setup lang="ts">
  /* 是否展示弹窗 */
  const visible = defineModel<boolean>({ default: false })
  const emit = defineEmits<{
    confirm: []
    'after-close': []
  }>()
  /* 确定事件 */
  const confirm = () => emit('confirm')
  /* 取消事件 */
  const close = () => {
    visible.value = false
    nextTick(() => {
      emit('after-close')
    })
  }
</script>
<template>
  <t-dialog :visible="visible" :on-confirm="confirm" :on-close="close">
    <template #header>
      <slot name="header" />
    </template>
    <template #body>
      <slot name="body" />
    </template>
  </t-dialog>
</template>
<style scoped></style>

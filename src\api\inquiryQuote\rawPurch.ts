import { get, post } from '@/utils/request'

const Apis = {
  rfqSetting: '/scm/rfqSetting',
  recordList: '/scm/ns/transfer/570/QueryInquiryList',
  company: '/admin/ns/subsidiary',
  addInquiry: '/scm/ns/transfer/570/InquireCreate',
  quotationRecord: '/scm/ns/transfer/570/QueryQuotationList',
}
/* 获取产品列表 */
export const getList = (useFor: string) => get(Apis.rfqSetting, { useFor })

/* 获取询价记录列表 */
export const recordList = (item: number) => get(`${Apis.recordList}?item=${item}`)

/* 招标公司 */
export const getCompany = () => get(Apis.company)

/* 提交报价 */
export const addInquiry = (data: any) => post(Apis.addInquiry, data)

/* 报价记录 */
export const quotationRecord = (params: object) => get(Apis.quotationRecord, params)

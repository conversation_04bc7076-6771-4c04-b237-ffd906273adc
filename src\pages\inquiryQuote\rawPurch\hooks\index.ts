import { getList, recordList, getCompany, addInquiry, quotationRecord } from '@/api/inquiryQuote/rawPurch'
import { MessagePlugin } from 'tdesign-vue-next'
import { useNsListLabel } from '@/use/dict'
import { getNsList } from '@/api/dict'
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'

export default () => {
  const { t } = useI18n()
  const $t = t
  /*状态列表 */
  const nsListDict = useNsListLabel('CUSTOMLIST_IT_SRM_APPROVE_STATUS')
  const value = ref<number>(1)
  /* 当前选中的选项 */
  const selected = ref<number>()
  /* 下拉配置 */
  const options = ref<any[]>([
    {
      label: $t('越南'),
      type: 'vn-raw-purch-rfq',
      options: [],
    },
    {
      label: $t('中亚'),
      type: 'ma-raw-purch-rfq',
      options: [],
    },
  ])
  /* 询价记录表格数据 */
  const inquiryData = ref<any[]>([])
  /* 询价记录表格配置 */
  const inquiryColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'altname', title: $t('标书名称'), width: 120, ellipsis: true },
    { colKey: 'custrecord_it_enquiry_com', title: $t('招标公司'), width: 120, ellipsis: true },
    { colKey: 'custrecord_it_enquiry_qty', title: $t('招标数量'), width: 120, ellipsis: true },
    { colKey: 'custrecord_it_enquiry_memo', title: $t('货品'), width: 120, ellipsis: true },
    { colKey: 'custrecord_it_enquiry_endtime', title: $t('报价截止时间'), width: 150, ellipsis: true },
    { colKey: 'custrecord_it_enquiry_country', title: $t('招标区域'), width: 120, ellipsis: true },
    { colKey: 'custrecord_it_enquiry_status', title: $t('状态'), width: 120, ellipsis: true },
  ])
  /* 主动询价列表 */
  const activeList = computed(() => [
    { id: 0, text: $t('中亚') },
    { id: 1, text: $t('越南') },
  ])
  /*  */
  const activeBtn = ref<number>(0)
  /* 主动询价弹窗 */
  const visible = ref<boolean>(false)
  /* 招标公司 */
  const companyList = ref<any[]>([])
  /* 交货地点、质量标准、包装方式 */
  const optionsData = { location: [], way: [], condition: [], company: [] }
  /* 货品名称 */
  const productName = ref<string>('')
  /* 招标区域 */
  const region = ref<string>('')
  /* loading */
  const loading = ref<boolean>(false)
  /* 主动询价弹窗表单 */
  const formData = ref({
    altname: '',
    custrecord_it_enquiry_com: '',
    custrecord_it_enquiry_item: null,
    custrecord_it_enquiry_endtime: '',
    custrecord_it_delivery_enddate: '',
    custrecord_it_enquiry_price: '',
    custrecord_it_enquiry_qty: '',
    custrecord_it_enquiry_addr: '',
    custrecord_it_enquiry_spec: '',
    custrecord_it_enquiry_unit: '44',
    custrecord_it_enquiry_pack: '',
    custrecord_it_enquiry_payment: '',
    custrecord_it_bargaining_endtime: '',
    custrecord_it_enquiry_country: null,
  })
  /* 询价记录分页 */
  const inquiryPagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })
  /* 表格loading */
  const tableLoading = ref<boolean>(false)
  /* 主动询价表单校验规则 */
  const formRules = {
    altname: [{ required: true, message: $t('请输入标书名称') }],
    custrecord_it_enquiry_com: [{ required: true, message: $t('请选择招标公司') }],
    custrecord_it_enquiry_endtime: [{ required: true, message: $t('请选择报价截止时间') }],
    custrecord_it_bargaining_endtime: [{ required: true, message: $t('请选择议价截止时间') }],
    custrecord_it_delivery_enddate: [{ required: true, message: $t('请选择最晚提货日期') }],
    custrecord_it_enquiry_price: [{ required: true, message: $t('请输入单价') }],
    custrecord_it_enquiry_qty: [{ required: true, message: $t('请输入可供数量') }],
    custrecord_it_enquiry_addr: [{ required: true, message: $t('请选择交货地点') }],
    custrecord_it_enquiry_spec: [{ required: true, message: $t('请输入质量标准') }],
    custrecord_it_enquiry_pack: [{ required: true, message: $t('请选择包装方式') }],
    custrecord_it_enquiry_payment: [{ required: true, message: $t('请选择付款条件') }],
  }
  /* 报价记录产品状态 */
  const select = computed(() => [
    { label: $t('待开标'), value: 1 },
    { label: $t('已中标'), value: 2 },
    { label: $t('议价'), value: 3 },
    { label: $t('未中标'), value: 4 },
  ])
  /* 报价记录form */
  const DEFAULT_QUOTATION_FORM = {
    status: 1,
    item: '',
  }
  const quotationForm = ref({ ...DEFAULT_QUOTATION_FORM })
  /* 报价记录列表 */
  const quotationData = ref<any[]>([])
  /* 报价记录表格配置 */
  const quotationColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'custrecord_it_quote_update', title: $t('报价日期'), width: 120, ellipsis: true },
    { colKey: 'custrecord_it_quote_goods', title: $t('产品'), width: 120, ellipsis: true },
    { colKey: 'custrecord_it_quote_modifyprice', title: $t('单价'), width: 120, ellipsis: true },
    { colKey: 'custrecord_it_quote_modifysupply', title: $t('数量'), width: 150, ellipsis: true },
    { colKey: 'vendor_name', title: $t('供应商/客户'), width: 120, ellipsis: true },
  ])
  /* 报价记录分页 */
  const quotationPagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  /* 获取产品列表 */
  const getProductList = async () => {
    try {
      await Promise.all(
        options.value.map(async (item, index) => {
          const res = await getList(item.type)
          if (res.code === 200) {
            options.value[index].options = res.data
            selected.value = options.value[0].options[0].itemId
          } else {
            MessagePlugin.error(res.msg)
          }
        }),
      )
    } catch (error) {}
  }
  /* 获取询价记录 */
  const getInquiryList = async () => {
    tableLoading.value = true
    try {
      const res = await recordList(selected.value)
      if (res.code === 200) {
        inquiryData.value = res.data as any[]
        inquiryPagination.value.total = (res.data as any).length
      } else {
        MessagePlugin.error(res.msg)
      }
    } finally {
      tableLoading.value = false
    }
  }

  /* 搜索询价记录 */
  const searchInquiry = () => getInquiryList()

  /* 打开主动询价弹窗 */
  const openInquiry = (item: any) => {
    visible.value = true
    productName.value = item.itemName
    region.value = item.useFor.startsWith('vn') ? $t('越南') : $t('中亚')
    formData.value.custrecord_it_enquiry_item = item.id
    formData.value.custrecord_it_enquiry_country = item.useFor.startsWith('vn') ? '5' : '7'
  }
  /* 获取招标公司列表 */
  const getCompanyList = async () => {
    const res = await getCompany()
    if (res.code !== 200) return MessagePlugin.error(res.msg)
    companyList.value = res.data as any[]
  }
  /* 获取交货地点、质量标准、包装方式 */
  const getPack = async () => {
    const oldData = {
      location: { type: 'CUSTOMLIST465', arr: [] },
      way: { type: 'CUSTOMLIST413', arr: [] },
      condition: { type: 'CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE', arr: [] },
    }
    for (const key in oldData) {
      const item = oldData[key]
      const res = await getNsList(item.type)
      if ((res as any).code !== 200) return MessagePlugin.error((res as any).msg)
      if (item.type) item.arr.push(...res.data)
      optionsData[key] = item.arr
    }
  }

  /* 获取报价记录 */
  const getQuotationRecord = async () => {
    tableLoading.value = true
    try {
      const res = await quotationRecord(quotationForm.value)
      if (res.code === 200) {
        quotationData.value = res.data as any[]
        quotationPagination.value.total = (res.data as any).length
      } else {
        MessagePlugin.error(res.msg)
      }
    } finally {
      tableLoading.value = false
    }
  }
  /* 提交报价 */
  const submitInquiry = async ({ validateResult, firstError }) => {
    if (validateResult === true) {
      loading.value = true
      try {
        const res = await addInquiry(formData.value)
        if (res.code === 200) {
          MessagePlugin.success($t('报价成功'))
          visible.value = false
          getInquiryList()
        } else {
          MessagePlugin.error(res.msg)
        }
      } finally {
        loading.value = false
        visible.value = false
      }
    }
  }

  /* 搜索报价记录 */
  const searchQuotation = async () => {
    await getQuotationRecord()
  }

  /* 表单重置 */
  const resetForm = () => (quotationForm.value = { ...DEFAULT_QUOTATION_FORM })
  const resetInquiryForm = () => (selected.value = options.value[0].options[0].itemId)

  onMounted(async () => {
    await getProductList()
    await getInquiryList()
    getPack()
    getCompanyList()
  })

  return {
    value,
    selected,
    options,
    inquiryData,
    inquiryColumns,
    nsListDict,
    searchInquiry,
    activeList,
    activeBtn,
    visible,
    openInquiry,
    formData,
    region,
    productName,
    companyList,
    optionsData,
    formRules,
    submitInquiry,
    loading,
    select,
    quotationForm,
    searchQuotation,
    quotationData,
    quotationColumns,
    quotationPagination,
    inquiryPagination,
    tableLoading,
    resetForm,
    resetInquiryForm,
  }
}

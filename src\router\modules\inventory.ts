import Layout from '@/layouts/index.vue'

export default [
  {
    path: '/inventory',
    name: 'inventory',
    component: Layout,
    redirect: '/inventory/position',

    meta: { title: '库存管理', icon: 'chart-radial', orderNo: 5 },

    children: [
      {
        path: 'stock',
        name: 'InventoryStock',
        component: () => import('@/pages/inventory/stock/index.vue'),
        meta: { title: '库存看板' },
      },
      {
        path: 'position',
        name: 'InventoryPosition',
        component: () => import('@/pages/inventory/position/index.vue'),
        meta: { title: '头寸看板' },
      },
      {
        path: 'aging',
        name: 'InventoryAging',
        component: () => import('@/pages/inventory/stock/components/aginsDetails.vue'),
        meta: { title: '账龄明细', hidden: true },
      },
    ],
  },
]

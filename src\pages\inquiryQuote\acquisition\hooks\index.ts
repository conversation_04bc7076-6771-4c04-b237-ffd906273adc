import { useUserStore } from '@/store'
import dayjs from 'dayjs'
import { MessagePlugin, type TableProps } from 'tdesign-vue-next'
import { $submit, updateActivePlan, $history } from '@/api/inquiryQuote/acquisition'
import { loadNoticeList, saveNotice, removeNoticeById } from '@/api/inquiryQuote/notice'
import { encode } from 'js-base64'
import { useRequest } from 'vue-request'
import { quotationRecord } from '@/api/inquiryQuote/rawPurch'

import { useAcquisitionStore } from '../store/useAcquisitionStore'

export default () => {
  const { t } = useI18n()
  const $t = t
  const userStore = useUserStore()
  const acquisitionStore = useAcquisitionStore()

  const value = ref<number>(1)
  /* 报价弹窗 */
  const visible = ref<boolean>(false)
  /* 采购弹窗 */
  const purchaseVisible = ref<boolean>(false)
  /* 扣款标准 */
  const deductionStandard = ref<any[]>([])
  /* 可选时间方位 */
  const disableDate = {
    before: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    after: dayjs().add(7, 'day').format('YYYY-MM-DD'),
  }
  /* 产品名称 */
  const productName = ref<string>('')
  /* loading */
  const loading = ref<boolean>(false)

  const countryIdMap: Record<string, number> = {
    vn: 5,
    zh: 1,
    ru: 3,
    kz: 4,
    ma: 7,
  }
  /* 报价表单 */
  const formData = ref<any>({
    custrecord_it_quote_enquiryid: 1313,
    custrecord_it_quote_addr: '1',
    custrecord_it_quote_location: '',
    custrecord_it_quote_lasttrandate: '',
    custrecord_it_quote_price: '',
    custrecord_it_quote_supply: '',
    custrecord_it_quote_item: '',
    custrecord_it_quote_goods: '',
    custrecord_it_quote_country: null,
    custrecord_it_quote_supplier: userStore.vendorIds?.[0],
    custrecord_it_quote_payment: 108,
    custrecord_it_quote_currency: 6,
  })

  /* 表单校验规则 */
  const FORM_RULES = computed(() => ({
    custrecord_it_quote_supply: [{ required: true, message: $t('请输入供应量') }],
    custrecord_it_quote_location: [{ required: true, message: $t('请输入交货地点') }],
    custrecord_it_quote_lasttrandate: [{ required: true, message: $t('最晚交货日期') }],
  }))

  // 只有采购经理才能编辑
  const canEdit = computed(() => {
    return (
      (userStore.userType === 1 && userStore.posts?.some((p) => p.positionKey.includes('PurchManager'))) ||
      userStore.isAdmin
    )
  })

  /* 采购表单 */
  const purchaseForm = ref<any>({})

  /* 报价表单 */
  const quotationForm = ref({
    itemId: '',
  })
  /* 报价记录表格数据 */
  const quotationList = ref<any[]>([])
  const quotationLoading = ref<boolean>(false)
  /* 表格配置 */
  const columns = computed(() => [
    {
      colKey: 'custrecord_it_quote_appoint_time',
      title: $t('预约时间'),
      width: '100',
      cell: (h: Function, { col, row }) => dayjs(row[col.colKey]).format('YYYY-MM-DD HH:mm:ss'),
    },
    { colKey: 'custrecord_it_quote_vehicle_no', title: $t('车号'), width: '100' },
    { colKey: 'custrecord_it_quote_driver', title: $t('司机'), width: '100' },
    {
      colKey: 'custrecord_it_quote_supply',
      title: $t('数量'),
      width: '100',
      cell: (h: Function, { col, row }) => row[col.colKey] + $t('吨'),
    },
    { colKey: 'vendor_name', title: $t('供应商'), width: '100' },
  ])
  /* 报价记录表格分页 */
  const pagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })
  /* 产品列表 */
  const productList = computed(() => acquisitionStore.productList)

  /* 报价记录表格 */
  const loadList = async () => {
    quotationLoading.value = true
    const res = await quotationRecord({
      inquiryId: 1313,
      procureItem: quotationForm.value.itemId,
      vendorIdList: userStore.vendorIds,
    })
    quotationList.value = res.data
    pagination.value.total = quotationList.value.length
    quotationLoading.value = false
  }

  /* 打开报价弹窗 */
  const offer = (info: any) => {
    visible.value = true
    productName.value = info.name
    deductionStandard.value = info.metricsConfig
    formData.value.custrecord_it_quote_price = info.standardPrice
    formData.value.custrecord_it_quote_supply = info.demandQuantity
    formData.value.custrecord_it_quote_item = info.itemId
    formData.value.custrecord_it_quote_goods = info.itemName
    formData.value.custrecord_it_quote_country = countryIdMap[(info.useFor as string).slice(0, 2)]
  }

  /* 采购弹窗 */
  const purchase = (info: any) => {
    productName.value = info.name
    const metricsMap = (info?.metricsConfig || []).reduce((acc: Record<string, any>, val: any) => {
      acc[val.key] = val.value
      return acc
    }, {} as Record<string, any>)

    purchaseForm.value = {
      ...info,
      itemName: info.configName,
      metrics1: metricsMap.metrics1,
      metrics2: metricsMap.metrics2,
      metrics3: metricsMap.metrics3,
      metrics1standard: metricsMap.metrics1standard,
      metrics2standard: metricsMap.metrics2standard,
      metrics3standard: metricsMap.metrics3standard,
    }
    purchaseVisible.value = true
  }

  /* 确认报价 */
  const onConfirm = async ({ validateResult }) => {
    if (validateResult === true) {
      if (!userStore.vendorIds?.[0]) return MessagePlugin.error($t('无供应商信息') + '，' + $t('不能提交'))
      loading.value = true
      try {
        const res = await $submit(formData.value)
        if (res.code !== 200) return MessagePlugin.error(res.msg)
        else MessagePlugin.success(res.msg)
        visible.value = false
      } finally {
        loading.value = false
        visible.value = false
      }
    }
  }

  /* 提交采购需求 */
  const submitProcurementReq = async () => {
    const data = {
      ...purchaseForm.value,
      metricsConfig: [
        { key: 'metrics1', label: '杂质%', value: encode(purchaseForm.value.metrics1) },
        { key: 'metrics2', label: '霉变粒%', value: encode(purchaseForm.value.metrics2) },
        { key: 'metrics3', label: '容重(g/L)', value: encode(purchaseForm.value.metrics3) },
        { key: 'metrics1standard', label: '杂质%', value: encode(purchaseForm.value.metrics1standard) },
        { key: 'metrics2standard', label: '霉变粒%', value: encode(purchaseForm.value.metrics2standard) },
        { key: 'metrics3standard', label: '容重(g/L)', value: encode(purchaseForm.value.metrics3standard) },
      ],
    }
    try {
      loading.value = true
      await updateActivePlan(data)
      MessagePlugin.success($t('提交成功'))
      await acquisitionStore.loadProductList()
    } finally {
      purchaseVisible.value = false
      loading.value = false
    }
  }

  const historyDialog = ref(false)
  const {
    data: historyData,
    loading: historyLoading,
    runAsync: fetchHistory,
  } = useRequest(async (useFor: string, itemId: number) => {
    const res = await $history(useFor, itemId)
    return (res.data || []).map((item: any) => ({
      ...item,
      ...(item.metricsConfig || []).reduce((acc: Record<string, any>, val: any) => {
        acc[val.key] = val.value
        return acc
      }, {}),
    }))
  })
  const historyTableBaseCols = computed<TableProps['columns']>(() => [
    { colKey: 'effectiveDate', title: $t('生效日期') },
    { colKey: 'expiryDate', title: $t('失效日期') },
    { colKey: 'standardPrice', title: $t('建议价') },
    { colKey: 'demandQuantity', title: $t('需求量') },
  ])
  const historyTableCols = computed<TableProps['columns']>(() => {
    return [
      ...historyTableBaseCols.value,
      ...(historyData.value?.[0]?.metricsConfig || []).map((item: any) => ({
        colKey: item.key,
        title: item.label,
      })),
    ]
  })
  const openRecordHistory = async (row: any) => {
    await fetchHistory(row.useFor, row.itemId)
    historyDialog.value = true
  }
  /* 搜索 */
  const onSubmit = async () => await loadList()
  /* 表单重置 */
  const resetForm = () => (quotationForm.value.itemId = productList.value[0]?.itemId)

  const noticeList = ref<any[]>([])
  const loadNoticeData = async () => {
    const res = await loadNoticeList()
    noticeList.value = (res.data || []).map((d) => ({
      ...d,
      content: decodeURI(atob(d.content)),
    }))
  }

  const newNoticeDialog = ref(false)
  const handleOpenNewNotice = () => {
    newNoticeDialog.value = true
  }

  const noticeForm = ref<any>({})
  const toNoticeDetail = (item: any) => {
    noticeForm.value = {
      ...item,
    }
    handleOpenNewNotice()
  }

  const closeNoticeForm = async () => {
    newNoticeDialog.value = false
    await nextTick()
    noticeForm.value = {}
  }

  const submitNotice = async () => {
    await saveNotice({
      id: noticeForm.value.id,
      content: btoa(encodeURI(noticeForm.value.content)),
      useFor: 'vn-raw-procure-rfq',
    })
    closeNoticeForm()
    loadNoticeData()
  }

  const removeNotice = async (id: any) => {
    if (id) {
      await removeNoticeById(id)
      loadNoticeData()
    }
  }

  onMounted(async () => {
    await acquisitionStore.loadProductList()
    quotationForm.value.itemId = productList.value[0]?.itemId
    await loadList()
    loadNoticeData()
  })

  return {
    value,
    offer,
    visible,
    productName,
    deductionStandard,
    formData,
    onConfirm,
    disableDate,
    FORM_RULES,
    purchaseVisible,
    purchase,
    purchaseForm,
    canEdit,
    submitProcurementReq,
    loading,
    quotationForm,
    productList,
    pagination,
    quotationList,
    quotationLoading,
    columns,
    resetForm,
    onSubmit,
    openRecordHistory,
    historyData,
    historyDialog,
    historyLoading,
    historyTableCols,

    noticeList,
    loadNoticeData,
    newNoticeDialog,
    handleOpenNewNotice,
    noticeForm,
    closeNoticeForm,
    submitNotice,
    removeNotice,
    toNoticeDetail,
  }
}

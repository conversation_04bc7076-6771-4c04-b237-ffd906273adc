import type { EChartsType } from 'echarts'
import { divideScale } from '@/utils/digit'

export interface PieType {
  quantity: string
  amount: string
  product: string
  item_id: number
  item_name: string
  currency: string
}

export type DealState = {
  pie?: EChartsType | null
  pieEchart?: EChartsType | null
  lineEchart?: EChartsType | null
}

export const DefaultPieResult: PieType[] = []

export const pieEchartConfig = (pies: PieType[], t: any, locale: any) => {
  return {
    color: [
      '#3fc9cb',
      '#215273',
      '#89e8b1',
      '#38a3a5',
      '#d2f7d5',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ],
    tooltip: {
      hideDelay: 100,
      trigger: 'item',
      formatter: ({ name, dataIndex }: any) => {
        return `${name}
				<br>${t('采购量')}:${new Intl.NumberFormat().format(divideScale(Number(pies[dataIndex].quantity), 1000, 3))}(吨)
				<br>${t('交易额')}:${new Intl.NumberFormat(locale, {
          style: 'currency',
          currencyDisplay: 'code',
          currency: pies[dataIndex].currency,
          notation: 'compact',
          compactDisplay: 'long',
          minimumFractionDigits: 3,
          roundingMode: 'halfFloor',
        }).format(Number(pies[dataIndex].amount || '0'))}`
      },
    },
    series: [
      {
        type: 'pie',
        radius: '85%',
        center: ['50%', '50%'],
        selectedMode: 'single',
        data: pies.map((p) => {
          return {
            value: p.quantity,
            name: p.item_name,
            label: {},
          }
        }),
        emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } },
      },
    ],
  }
}

export interface LineType {
  quantity: string
  date_unit: string
  amount: string
  count: string
  price: string
  currency: string
}

export const DefaultLineResult: LineType[] = []

export const dealEchartConfig = (lines: LineType[], t: any, locale: any) => {
  return {
    grid: {
      top: '10%',
      bottom: 60,
      left: '5%',
      right: '5%',
    },
    dataZoom: [
      {
        type: 'inside',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 100,
        bottom: '10',
        zoomLock: true,
      },
    ],
    tooltip: {
      hideDelay: 100,
      trigger: 'axis',

      formatter: function (params: any[]) {
        return `${params[0].name}
				<br>${t('采购量')}:${new Intl.NumberFormat().format(divideScale(Number(params[0].data), 1000, 3))}(吨)
				<br>${t('平均单价')}:${new Intl.NumberFormat(locale, {
          style: 'currency',
          currencyDisplay: 'code',
          currency: lines[params[0].dataIndex].currency,
          notation: 'compact',
          compactDisplay: 'long',
          minimumFractionDigits: 3,
          roundingMode: 'halfFloor',
        }).format(Number(params[1].data || '0'))}`
      },
    },
    legend: { data: [t('采购量'), t('平均单价')], bottom: 5 },
    xAxis: [
      {
        type: 'category',
        data: lines.map((i) => i.date_unit),
        axisPointer: { type: 'shadow' },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: `${t('采购量')}/${t('吨')}`,
        minInterval: 1,
        axisLabel: {
          formatter: function (value: any) {
            return new Intl.NumberFormat().format(divideScale(Number(value), 1000, 3))
          },
          width: 47,
          margin: 3,
          overflow: 'breakAll',
        },
        nameTextStyle: {
          color: '#999',
          align: 'left',
        },
        axisLine: {
          lineStyle: {
            width: 1,
            color: '#999',
            type: 'dotted',
          },
        },
      },
      {
        type: 'value',
        name: t('平均单价'),
        minInterval: 1,
        axisLabel: {
          formatter: function (value: any) {
            return new Intl.NumberFormat().format(Number(value))
          },
          width: 47,
          margin: 3,
          overflow: 'breakAll',
        },
        nameTextStyle: {
          color: '#aaa',
          align: 'right',
        },
        axisLine: {
          lineStyle: {
            width: 1,
            color: '#aaa',
            type: 'solid',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: t('采购量'),
        type: 'bar',
        data: lines.map((i) => i.quantity),
        barWidth: '30',
      },
      {
        name: t('平均单价'),
        type: 'line',
        yAxisIndex: 1,
        data: lines.map((i) => i.price),
      },
    ],
  }
}

<template>
  <div>
    <template v-if="setting.layout.value === 'side'">
      <t-layout key="side" :class="mainLayoutCls">
        <t-aside><layout-side-nav /></t-aside>
        <t-layout>
          <t-header><layout-header /></t-header>
          <t-content><layout-content /></t-content>
        </t-layout>
      </t-layout>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, watch } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useRoute } from 'vue-router'
  import { useSettingStore, useTabsRouterStore } from '@/store'

  import LayoutHeader from './components/LayoutHeader.vue'
  import LayoutContent from './components/LayoutContent.vue'
  import LayoutSideNav from './components/LayoutSideNav.vue'

  import { prefix } from '@/config/global'

  import '@/style/layout.less'

  const route = useRoute()
  const settingStore = useSettingStore()
  const tabsRouterStore = useTabsRouterStore()
  const setting = storeToRefs(settingStore)

  const mainLayoutCls = computed(() => [
    {
      't-layout--with-sider': settingStore.showSidebar,
    },
  ])

  const appendNewRoute = () => {
    const {
      path,
      query,
      meta: { title },
      name,
    } = route
    tabsRouterStore.appendTabRouterList({ path, query, title: title as string, name, isAlive: true, meta: route.meta })
  }

  onMounted(() => {
    appendNewRoute()
  })

  watch(
    () => route.path,
    () => {
      appendNewRoute()
      document.querySelector(`.${prefix}-layout`).scrollTo({ top: 0, behavior: 'smooth' })
    },
  )
</script>

<style lang="less" scoped></style>

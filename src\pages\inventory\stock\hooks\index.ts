import { getNsList } from '@/api/dict'
import { ECharts, EChartsOption, init } from 'echarts'
import { positionTotal, getOwnedWarehouseList, getGoods, getRent } from '@/api/inventory'
import { groupBy, sum } from 'lodash-es'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/use/useProject'

export default () => {
  const { t, locale } = useI18n()
  const $t = t
  const router = useRouter()
  const { menuOptions, activeProjectId } = storeToRefs(useProjectStore())
  /* form表单 */
  const form = ref({
    projectId: activeProjectId.value,
  })
  /* 项目列表 */
  const projectList = ref(menuOptions)
  /* loading */
  const loading = ref(false)
  /* 成品chart */
  const finishedGoods = ref<HTMLDivElement>(null)
  /* 原料chart */
  const rawMaterials = ref<HTMLDivElement>(null)
  let finishedGoodsChart: ECharts | null
  let rawMaterialsChart: ECharts | null
  /* 自有库chart */
  const ownedWarehouse = ref<HTMLDivElement>(null)
  /* 外租库chart */
  const offsiteWarehouse = ref<HTMLDivElement>(null)
  let ownedWarehouseChart: ECharts | null
  let offsiteWarehouseChart: ECharts | null
  /* 库存分布 */
  const stock = ref<HTMLDivElement>(null)
  let stockChart: ECharts | null
  const stockId = ref<number>(null)
  const stockList = ref<any>([])

  /* 饼图配置 */
  const pieChartOption = ref<EChartsOption>({
    color: [
      '#3fc9cb',
      '#215273',
      '#89e8b1',
      '#38a3a5',
      '#d2f7d5',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ],
    highlight: {},
    tooltip: {
      show: true,
      trigger: 'item',
      extraCssText: 'z-index: 1',
      textStyle: {
        fontSize: 14,
      },
      formatter: function (params: any) {
        const value = params.value
        const name = params.name
        return `
					<div>
							<p>${$t('产品')}：${name}</p>
							<p>${$t('数量')}：${value} T</p>
					</div>`
      },
    },
    legend: {
      bottom: 0,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 10,
      },
    },

    series: [
      {
        type: 'pie',
        radius: '80%',
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },

        label: {
          show: true,
          position: 'inside',
          formatter: '{c}',
        },
      },
    ],
  })
  /* 柱状图配置 */
  const barChartOption = ref<EChartsOption>({
    grid: {
      left: '64px',
    },
    tooltip: {
      show: false,
    },
    legend: {
      data: [$t('库存数') + '(T)', $t('最大账龄')],
      textStyle: {
        fontSize: 9,
      },
      selectedMode: false,
    },
    xAxis: [
      {
        type: 'value',
        alignTicks: true,
        axisLabel: {
          fontSize: 9,
          margin: 4,
        },
      },
      {
        type: 'value',
        alignTicks: true,
        axisLabel: {
          fontSize: 9,
          margin: 4,
        },
      },
    ],
    yAxis: [{ type: 'category' }],
    series: [
      {
        name: $t('库存数') + '(T)',
        type: 'bar',
        barWidth: '25%',
        xAxisIndex: 1,
        label: {
          show: true,
          position: 'right',
          fontSize: 12,
        },
        labelLayout: {
          align: 'left',
        },
      },
      {
        name: $t('最大账龄'),
        type: 'bar',
        xAxisIndex: 0,
        barWidth: '20%',
        label: {
          show: true,
          position: 'right',
          fontSize: 12,
        },
        labelLayout: {
          align: 'left',
        },
      },
    ],
  })
  let options = {
    ...barChartOption.value,
  }
  const stockOption = ref<EChartsOption>({
    legend: {
      show: true,
    },
    series: [
      {
        type: 'pie',
        radius: '80%',
        data: [],
        emphasis: {
          scale: false,
          disabled: true,
        },
        label: {
          show: true,
          position: 'inside',
          formatter: '{b}: {c}', // 使用 {d} 来表示百分比
        },
      },
    ],
  })
  /* 原料列表 */
  const rawInventoryList = ref([])
  /* 成品列表 */
  const finishedGoodsList = ref([])
  /* 自有库列表 */
  const ownedWarehouseList = ref<any[]>([])
  /* 参数id */
  const projectId = ref<number>(0)
  const offsiteWarehouseId = ref<number>(0)
  const list = ref<any>([])
  /* 外租库列表 */
  const offsiteWarehouseList = ref([])
  const rentList = reactive<any[]>([])
  const chartList = ref<any[]>([])
  let binIdNameOptions: any
  const originItemDayMap = reactive<{ [keyof: string]: number }>({})
  const tempItemDayMap = reactive<{ [keyof: string]: number }>({})
  /* 初始化echarts */
  const initEcharts = () => {
    finishedGoodsChart = init(finishedGoods.value)
    rawMaterialsChart = init(rawMaterials.value)
    ownedWarehouseChart = init(ownedWarehouse.value)
    offsiteWarehouseChart = init(offsiteWarehouse.value)
    stockChart = init(stock.value)
    finishedGoodsChart.setOption(pieChartOption.value)
    rawMaterialsChart.setOption(pieChartOption.value)
    ownedWarehouseChart.setOption(barChartOption.value)
    offsiteWarehouseChart.setOption(options)
    stockChart.setOption(stockOption.value)
  }
  /* 更新原料数据 */
  const initRawCharts = () => {
    if (!rawMaterialsChart) return
    const series = [
      {
        ...(pieChartOption.value.series as any[])[0],
        data: rawInventoryList.value.map((item) => {
          return {
            value: ((Number(item.quantityOrigin) + Number(item.quantityTemp)) / 1000).toFixed(3),
            name: item.itemName,
          }
        }),
      },
    ]
    rawMaterialsChart.setOption({ title: { text: $t('原料'), textStyle: { fontSize: 12 } }, series })
  }
  /* 更新成品数据 */
  const initFinishedCharts = () => {
    if (!finishedGoodsChart) return
    const series = [
      {
        ...(pieChartOption.value.series as any[])[0],
        data: finishedGoodsList.value.map((item) => {
          return {
            value: ((Number(item.quantityOrigin) + Number(item.quantityTemp)) / 1000).toFixed(3),
            name: item.itemName,
          }
        }),
      },
    ]
    finishedGoodsChart.setOption({ title: { text: $t('成品'), textStyle: { fontSize: 12 } }, series })
  }
  /* 获取现有库尊总量 */
  const getInventory = async () => {
    ownedWarehouseList.value = []
    offsiteWarehouseId.value = 0
    loading.value = true
    try {
      const { data } = await positionTotal(form.value.projectId)
      rawInventoryList.value = data?.filter((item: any) => item.positionType !== '2')
      finishedGoodsList.value = data?.filter((item: any) => item.positionType === '2')
      stockList.value = data
      stockId.value = stockList.value?.[0]?.itemNo

      initRawCharts()
      initFinishedCharts()
      await getDetails()
      await getRentDetail()
      await getLibrary(form.value.projectId)
      ownedWarehouseSearch()
      offsiteWarehousSearch()
      stockSearch()
    } finally {
      loading.value = false
    }
  }

  const getLibrary = async (id: number, binId?: string) => {
    const res = await getGoods(id, binId)
    const obj = (res.data.origin || []).reduce((acc: any, val: any) => {
      acc[val.itemCode] = val
      return acc
    }, {} as Record<string, number>)
    Object.assign(originItemDayMap, obj)
    const map = (res.data.temp || []).reduce((acc, val) => {
      acc[val.itemCode] = val
      return acc
    }, {} as Record<string, number>)
    Object.assign(tempItemDayMap, map)
  }
  const getRentDetail = async () => {
    offsiteWarehouseList.value = []
    rentList.length = 0
    const res = await getRent(form.value.projectId)
    rentList.push(...res.data)
    let binItemFlat: any[] = rentList.flatMap((it) => it.children)

    const test = binItemFlat.reduce((pre: any, acc: any) => {
      pre[acc.itemCode] = (pre[acc.itemCode] ?? 0) + Number(acc.quantity)
      return pre
    }, {})
    const result = binItemFlat.reduce((pre: any, acc: any) => {
      pre[acc.itemCode] = acc.itemName
      return pre
    }, {})
    chartList.value = Object.keys(test).map((it) => ({
      itemCode: it,
      itemName: result[it],
      quantity: test[it],
    }))

    const arr = rentList.map((item) => ({ label: item.binName, value: item.nsBinId }))
    offsiteWarehouseList.value.push({ label: $t('全部'), value: 0 }, ...arr)
    await getLibrary(form.value.projectId)
  }

  /* 自有库分布详情 */
  const getDetails = async () => {
    const res = await getOwnedWarehouseList(form.value.projectId)
    list.value = res.data
    list.value.forEach((item: any) => {
      item.quantity = (Number(item.quantity) / 1000).toFixed(2)
    })
    const availableBinNameMap = (list.value || []).reduce((map: Map<number, string>, val: any) => {
      map.set(val.bin_id, val.bin_name)
      return map
    }, new Map<number, string>())
    binIdNameOptions = [...availableBinNameMap].map((val) => ({ value: val[0], label: val[1] }))

    ownedWarehouseList.value.push({ value: 0, label: $t('全部') }, ...binIdNameOptions)
  }
  /* 搜索自有库 */
  const ownedWarehouseSearch = () => {
    let current = []
    let yData: any
    let arr: any
    if (projectId.value === 0) {
      current = Object.entries(groupBy(list.value, 'item_id')).map((val: any) => {
        return {
          item_id: val[0],
          item_code: val[1][0].item_code,
          item_name: val[1][0].item_name,
          project_id: val[1][0].project_id,
          quantity: sum(val[1].map((val: any) => Number(val.quantity))).toFixed(2),
        }
      })

      yData = current.filter((val: any) => Number(val.quantity) > 1).map((val: any) => val.item_name)

      arr = current
        .map((item: any) => originItemDayMap[item.item_code])
        .map((item: any) =>
          item === undefined
            ? {}
            : {
                ...item,
                value: item.maxAge,
                name: item.itemName,
                item_code: item.itemCode,
                project_id: form.value.projectId,
              },
        )
    } else {
      const binItemGroup = groupBy(list.value || [], (d) => d.bin_id)
      current = binItemGroup[projectId.value]
      yData = current.filter((val: any) => Number(val.quantity) > 1).map((val: any) => val.item_name)
      arr = current
        .map((item: any) => originItemDayMap[item.item_code])
        .map((item: any) =>
          item === undefined
            ? {}
            : {
                ...item,
                value: item.maxAge,
                name: item.itemName,
                item_code: item.itemCode,
                project_id: form.value.projectId,
              },
        )
    }

    // 设置图表
    if (barChartOption.value.yAxis) {
      ;(barChartOption.value.yAxis as any[])[0] = {
        data: yData,
        axisLabel: { interval: 0, rotate: 0, fontSize: 10, margin: 2 },
      }
    }
    if (barChartOption.value.series) {
      if (projectId.value == 0) {
        if (Array.isArray(barChartOption.value.legend)) {
          barChartOption.value.legend.forEach((legendItem: any) => {
            if (Array.isArray(legendItem.data)) {
              legendItem.data = legendItem.data.push($t('最大账龄'))
            }
          })
        } else if (barChartOption.value.legend && Array.isArray(barChartOption.value.legend.data)) {
          barChartOption.value.legend.data.push($t('最大账龄'))
        }
        ;(barChartOption.value.series as any[])[1].data = arr
      } else {
        if (Array.isArray(barChartOption.value.legend)) {
          barChartOption.value.legend.forEach((legendItem: any) => {
            if (Array.isArray(legendItem.data)) {
              legendItem.data = legendItem.data.filter((val: any) => val !== $t('最大账龄'))
            }
          })
        } else if (barChartOption.value.legend && Array.isArray(barChartOption.value.legend.data)) {
          barChartOption.value.legend.data = barChartOption.value.legend.data.filter(
            (val: any) => val !== $t('最大账龄'),
          )
        }

        ;(barChartOption.value.series as any[])[1].data = []
      }
      ;(barChartOption.value.series as any[])[0].data = current
        .filter((val: any) => Number(val.quantity) > 1)
        .map((val: any) => ({
          ...val,
          name: val.item_name,
          value: Number(val.quantity),
        }))
    }
    if (ownedWarehouseChart) {
      ownedWarehouseChart.on('click', (params: any) => {
        const { project_id, item_code } = params.data
        router.push({ path: '/inventory/aging', query: { active: 403, projectId: project_id, itemCode: item_code } })
      })

      ownedWarehouseChart.setOption(barChartOption.value)
    }
  }
  /* 搜索外租库 */
  const offsiteWarehousSearch = async () => {
    let current: any
    let arr: any[] = []
    let numList: any[] = []
    let yData: any[] = []
    let rowData: any
    if (offsiteWarehouseId.value == 0) {
      await getLibrary(form.value.projectId)
      current = chartList.value
      arr = current
        .filter((value: any) => Number(value.quantity) > 1)
        .map((item: any) => ({
          ...item,
          name: item.itemName,
          value: Number(item.quantity) / 1000,
          project_id: form.value.projectId,
          item_code: item.itemCode,
        }))
      numList = current
        .map((item: any) => tempItemDayMap[item.itemCode])
        .map((val: any) =>
          val === undefined
            ? {}
            : {
                ...val,
                value: val.maxAge,
                name: val.itemName,
                item_code: val.itemCode,
                project_id: form.value.projectId,
              },
        )

      yData = current.filter((value: any) => Number(value.quantity) > 1).map((item: any) => item.itemName)
    } else {
      await getLibrary(form.value.projectId, String(offsiteWarehouseId.value))
      current = rentList.filter((item: any) => item.nsBinId == offsiteWarehouseId.value)[0]

      arr = current.children
        .filter((value: any) => Number(value.quantity) > 1)
        .map((item: any) => ({
          ...item,
          name: item.itemName,
          value: Number(item.quantity) / 1000,
          project_id: form.value.projectId,
          item_code: item.itemCode,
        }))
      numList = current.children
        .map((item: any) => tempItemDayMap[item.itemCode])
        .map((val: any) =>
          val === undefined
            ? {}
            : {
                ...val,
                value: val.maxAge,
                name: val.itemName,
                item_code: val.itemCode,
                project_id: form.value.projectId,
              },
        )

      yData = current.children.filter((value: any) => Number(value.quantity) > 1).map((item: any) => item.itemName)
    }

    options.yAxis = options.yAxis as []
    options.yAxis[0] = {
      data: yData,
      axisLabel: { interval: 0, rotate: 0, fontSize: 10, margin: 2 },
    }
    options.series = options.series as []
    options.series[0].data = arr
    options.series[1].data = numList

    if (offsiteWarehouseChart) {
      offsiteWarehouseChart.on('click', (params) => {
        const { project_id, item_code } = params.data as any
        router.push({ path: '/inventory/aging', query: { active: 404, projectId: project_id, itemCode: item_code } })
      })
      offsiteWarehouseChart.setOption(options)
    }
  }
  /* 库存搜索 */
  const stockSearch = () => {
    const current = stockList.value.filter((item: any) => item.itemNo == stockId.value)[0]
    if (current) {
      stockOption.value.series[0].data = [
        {
          value: (Number(current.quantityTemp) / 1000).toFixed(3),
          name: $t('外租库'),
        },
        { value: (Number(current.quantityOrigin) / 1000).toFixed(3), name: $t('自有库') },
      ]
    } else {
      stockOption.value.series[0].data = [
        {
          value: 0,
          name: $t('外租库'),
        },
        { value: 0, name: $t('自有库') },
      ]
    }
    stockChart.setOption(stockOption.value)
  }
  /* 切换项目 */
  const onsubmit = async () => {
    activeProjectId.value = form.value.projectId
    await getInventory()
  }
  /* 重置form */
  const onReset = async () => {
    form.value = {
      projectId: projectList.value?.[0].value,
    }
    await getInventory()
  }

  watch(
    () => locale.value,
    () => {
      if (rawMaterialsChart) {
        rawMaterialsChart.setOption({ title: { text: $t('原料') } })
      }
      if (finishedGoodsChart) {
        finishedGoodsChart.setOption({ title: { text: $t('成品') } })
      }
      if (ownedWarehouseChart) {
        ownedWarehouseChart.setOption({
          legend: {
            data: [$t('库存数') + '(T)', $t('最大账龄')],
          },
          series: [{ name: $t('库存数') + '(T)' }, { name: $t('最大账龄') }],
        })
      }
      if (offsiteWarehouseChart) {
        offsiteWarehouseChart.setOption({
          legend: {
            data: [$t('库存数') + '(T)', $t('最大账龄')],
          },
          series: [{ name: $t('库存数') + '(T)' }, { name: $t('最大账龄') }],
        })
      }
      stockSearch()
    },
  )

  onMounted(async () => {
    initEcharts()
    await getInventory()
  })

  return {
    form,
    projectList,
    loading,
    finishedGoods,
    rawMaterials,
    onsubmit,
    onReset,
    ownedWarehouse,
    offsiteWarehouse,
    projectId,
    ownedWarehouseList,
    ownedWarehouseSearch,
    offsiteWarehouseId,
    offsiteWarehouseList,
    offsiteWarehousSearch,
    stock,
    stockId,
    stockList,
    stockSearch,
  }
}

import Layout from '@/layouts/index.vue'

export default [
  {
    path: '/capital',
    name: 'capital',
    component: Layout,

    meta: { title: '资金管理', icon: 'creditcard', orderNo: 6 },

    children: [
      {
        path: '',
        name: 'Capital',
        component: () => import('@/pages/capital/index.vue'),
      },
      {
        path: 'detail',
        name: 'CapitalDetail',
        component: () => import('@/pages/capital/components/details.vue'),
        meta: { title: '金额明细', hidden: true },
      },
      {
        path: 'paymentTerm',
        name: 'CapitalPaymentTerm',
        component: () => import('@/pages/capital/components/paymentTerm.vue'),
        meta: { title: '账期明细', hidden: true },
      },
    ],
  },
]

import { saleContractGoodsApi, customerListApi, saleContractListApi } from '@/api/contract'
import { useNsListLabelRaw } from '@/use/dict'
import { useUserStore } from '@/store'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import { debounce } from 'lodash-es'
import { useRouter } from 'vue-router'
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'
import { useProjectStore } from '@/use/useProject'

export default () => {
  const router = useRouter()
  const { t, locale } = useI18n()
  const $t = t
  const globalStore = useUserStore()
  const { activeProjectId } = storeToRefs(useProjectStore())
  /* 销售合同列表 */
  const saleContractInfoLoading = ref<boolean>(true)
  const saleContractList = ref([])
  /* 销售合同订单列表 */
  const orderList = ref([])
  /* 项目类型 */
  const projectType = ref<any>([])
  /* 金额币种 */
  const currencyType = ref<any>({})
  /* 提货方式 */
  const pickMethod = ref<any>({})
  /* 交易方式 */
  const tradeType = ref<{ [key: string]: any }>({})
  /* 查询参数 */
  const formData = ref({
    status: 'pending',
    itemId: null,
    dataStart: undefined,
    dataEnd: undefined,
    customerIdList: globalStore.customerIds,
    customerId: null,
  })
  /* 订单状态列表 */
  const orderStatus = computed(() => [
    { title: $t('待履行'), id: 0, value: 'pending' },
    { title: $t('已完成'), id: 1, value: 'over' },
    { title: $t('已取消'), id: 2, value: 'canceled' },
  ])
  /* 初始时间 */
  const timeRange = ref([])
  /* 可选时间范围 */
  const disableDate = {
    before: dayjs().subtract(3, 'year').format('YYYY-MM-DD'),
    after: dayjs().format('YYYY-MM-DD'),
  }
  /* 输入框绑定值（显示 label） */
  const inputValue = ref('')
  /* 模糊搜索初始数据 */
  const filteredOptions = ref([])
  /* 客户列表 */
  const customerList = ref<any[]>([])

  /* 选项卡切换 */
  const tabChange = async () => await getSaleContractOrderList()
  /* 选择时间 */
  const timeChange = (value: string[]) => {
    formData.value.dataStart = value[0]
    formData.value.dataEnd = value[1]
  }

  // 模糊搜索

  const filterOptions = (keyword: string) => {
    if (!keyword) return []
    const lowerKeyword = keyword.trim().toLowerCase()
    return customerList.value.filter((item) => item.label.trim().toLowerCase().includes(lowerKeyword))
  }

  // 添加防抖处理（300ms）
  const debouncedSearch = debounce((value: string) => {
    filteredOptions.value = filterOptions(value)
  }, 300)
  /* 处理搜索输入 */
  const handleSearch = (value: string) => debouncedSearch(value)
  // 处理选项选择
  const handleSelect = (value: any) =>
    (formData.value.customerId = customerList.value.find((item) => item.label === value)?.value)
  /* 表单提交 */
  const onSubmit = async () => await getSaleContractOrderList()
  /* 重置表单 */
  const onReset = async () => {
    timeRange.value = []
    inputValue.value = ''
    formData.value = {
      ...formData.value,
      itemId: null,
      dataStart: null,
      dataEnd: null,
      status: 'pending',
    }
    await getSaleContractOrderList()
  }
  /* 加载状态 */
  const loading = ref<boolean>(true)

  /* 表格配置 */
  const columns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'order_no', title: $t('销售单号'), ellipsis: true, width: 120, fixed: 'left' },
    {
      colKey: 'customer_name',
      title: $t('客户'),
      ellipsis: true,
      width: 150,
    },
    // { colKey: 'end_customer', title: $t('终端客户'), ellipsis: true, width: 150 },
    {
      colKey: 'end_rest_day',
      title: $t('到期时间'),
      ellipsis: true,
      width: 120,
    },
    { colKey: 'item_price', title: $t('单价'), ellipsis: true, width: 100 },
    { colKey: 'item_quantity', title: $t('数量') + '(t)', ellipsis: true, width: 120 },
    { colKey: 'delivery_date_to', title: $t('最晚交货日期'), width: 120, ellipsis: true },
    { colKey: 'staff_name', title: $t('销售代表'), ellipsis: true, width: 160 },
    { colKey: 'delivery_address', title: $t('交货地点'), ellipsis: true, width: 100 },
    { colKey: 'rest_quantity', title: $t('待交货数量') + '(t)', ellipsis: true, width: 110 },
    { colKey: 'signing_date', title: $t('签订日期'), ellipsis: true, width: 150 },
    {
      title: $t('操作'),
      colKey: 'link',
      fixed: 'right',
      ellipsis: true,
      width: 80,
    },
  ])
  /* 表格分页配置 */
  const pagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  /* 是否展示列配置 */
  const columnControllerVisible = ref<boolean>(false)
  /* 列配置 */
  const DEFAULT_FIELDS = [
    'order_no',
    'end_rest_day',
    'customer_name',
    // 'end_customer',
    'item_price',
    'item_quantity',
    'delivery_address',
    'delivery_date_to',
    'staff_name',
    'rest_quantity',
    'signing_date',
  ]
  const columnControllerConfig: any = computed(() => ({
    dialogProps: { preventScrollThrough: true },
    fields: [...DEFAULT_FIELDS],
    hideTriggerButton: true,
  }))
  /* 表格配置 */
  const DEFAULT_COLUMNS = [
    'order_no',
    'end_rest_day',
    'customer_name',
    // 'end_customer',
    'item_price',
    'item_quantity',
    'delivery_address',
    'delivery_date_to',
    'staff_name',
    'rest_quantity',
    'signing_date',
    'link',
  ]
  const displayColumns = ref<any>([...DEFAULT_COLUMNS])

  /* 刷新表格 */
  const refresh = async () => await getSaleContractOrderList()
  /* 金额转换 */
  const formatCurrency = (value: any, currencyId: number) => {
    if (value === null || value === undefined) return ''
    const number = parseFloat(value)
    if (isNaN(number)) return ''
    return new Intl.NumberFormat(locale.value, {
      style: 'currency',
      currencyDisplay: 'code',
      currency: currencyType.value[currencyId],
      notation: 'compact',
      compactDisplay: 'long',
      minimumFractionDigits: 2,
      roundingMode: 'halfFloor',
    }).format(value)
  }

  /* 获取客户列表 */
  const getCustomerList = async () => {
    const { data } = await customerListApi(formData.value.itemId)
    customerList.value = data.map((it: any) => ({
      label: it.customerName,
      value: it.customerId,
    }))
  }
  /* 获取销售合同列表 */
  const getSaleContractList = async () => {
    try {
      saleContractInfoLoading.value = true
      const { data } = await saleContractGoodsApi()
      saleContractList.value = data
      activeProjectId.value = data[0]?.projectId
    } finally {
      saleContractInfoLoading.value = false
    }
  }
  /* 获取订单列表 */
  const getSaleContractOrderList = async () => {
    loading.value = true
    try {
      const { data } = await saleContractListApi(formData.value)
      orderList.value = data.filter((it: any) => it.project_id === activeProjectId.value)
      pagination.value.total = orderList.value.length
      if (formData.value.status !== 'pending') {
        columnControllerConfig.value.fields = DEFAULT_FIELDS.filter(
          (field) => !['rest_quantity', 'end_rest_day'].includes(field),
        )
        displayColumns.value = DEFAULT_COLUMNS.filter((field) => !['rest_quantity', 'end_rest_day'].includes(field))
      } else {
        columnControllerConfig.value.fields = DEFAULT_FIELDS
        displayColumns.value = [...DEFAULT_COLUMNS]
      }
    } catch (error) {
    } finally {
      loading.value = false
    }
  }
  /* 获取项目label字典 */
  const getProjectLabel = async () => {
    const {
      CUSTOMLIST_PROJECT_LIST,
      currency,
      CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE,
      CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI,
    } = await useNsListLabelRaw(
      'CUSTOMLIST_PROJECT_LIST',
      'currency',
      'CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE',
      'CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI',
    )
    projectType.value = CUSTOMLIST_PROJECT_LIST
    currencyType.value = currency
    tradeType.value = CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE
    pickMethod.value = CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI
  }

  /* 订单详情弹窗 */
  const toContractDetail = async (row: any) =>
    router.push({ path: '/contract/sales-details', state: { info: JSON.stringify(row) } })

  onMounted(async () => {
    getProjectLabel()
    getCustomerList()
    await getSaleContractList()
    await getSaleContractOrderList()
  })

  return {
    activeProjectId,
    saleContractList,
    saleContractInfoLoading,
    projectType,
    pickMethod,
    formData,
    timeRange,
    disableDate,
    timeChange,
    orderStatus,
    inputValue,
    filteredOptions,
    handleSearch,
    handleSelect,
    onSubmit,
    onReset,
    displayColumns,
    columnControllerVisible,
    columnControllerConfig,
    loading,
    columns,
    pagination,
    orderList,
    refresh,
    formatCurrency,
    tabChange,
    toContractDetail,
  }
}

<template>
  <t-layout :class="`${prefix}-layout`">
    <t-tabs
      v-if="settingStore.isUseTabsRouter"
      theme="card"
      :class="`${prefix}-layout-tabs-nav`"
      :value="$route.path"
      :style="{ position: 'sticky', top: 0, width: '100%' }"
      @change="handleChangeCurrentTab"
      @remove="handleRemove"
    >
      <t-tab-panel
        v-for="(routeItem, index) in tabRouters"
        :key="`${routeItem.path}_${index}`"
        :value="routeItem.path"
        :removable="!routeItem.isHome"
      >
        <template #label>
          <t-dropdown
            trigger="context-menu"
            :min-column-width="128"
            :popup-props="{
              overlayClassName: 'route-tabs-dropdown',
              onVisibleChange: (visible, ctx) => handleTabMenuClick(visible, ctx, routeItem.path),
              visible: activeTabPath === routeItem.path,
            }"
          >
            <template v-if="!routeItem.isHome">
              {{ getDynamicTitle(routeItem.meta) }}
            </template>
            <t-icon v-else name="home" />
            <template #dropdown>
              <t-dropdown-menu>
                <t-dropdown-item @click="() => handleRefresh(routeItem, index)">
                  <t-icon name="refresh" />
                  {{ $t('刷新') }}
                </t-dropdown-item>
                <t-dropdown-item v-if="index > 1" @click="() => handleCloseAhead(routeItem.path, index)">
                  <t-icon name="arrow-left" />
                  {{ $t('关闭左侧') }}
                </t-dropdown-item>
                <t-dropdown-item
                  v-if="index < tabRouters.length - 1"
                  @click="() => handleCloseBehind(routeItem.path, index)"
                >
                  <t-icon name="arrow-right" />
                  {{ $t('关闭右侧') }}
                </t-dropdown-item>
                <t-dropdown-item v-if="tabRouters.length > 2" @click="() => handleCloseOther(routeItem.path, index)">
                  <t-icon name="close-circle" />
                  {{ $t('关闭其它') }}
                </t-dropdown-item>
              </t-dropdown-menu>
            </template>
          </t-dropdown>
        </template>
      </t-tab-panel>
    </t-tabs>
    <t-content :class="`${prefix}-content-layout`">
      <l-breadcrumb v-if="settingStore.showBreadcrumb" />
      <l-content />
    </t-content>
  </t-layout>
</template>

<script setup lang="ts">
  import { nextTick, ref, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useSettingStore, useTabsRouterStore } from '@/store'
  import { prefix } from '@/config/global'
  import type { TRouterInfo } from '@/types/interface'
  import { useUserStore } from '@/store'

  const globalStore = useUserStore()
  const { t } = useI18n()
  const $t = t

  import LContent from './Content.vue'
  import LBreadcrumb from './Breadcrumb.vue'

  const route = useRoute()
  const router = useRouter()

  const settingStore = useSettingStore()
  const tabsRouterStore = useTabsRouterStore()
  const tabRouters = computed(() => tabsRouterStore.tabRouters.filter((route) => route.isAlive || route.isHome))
  const activeTabPath = ref('')

  // 动态计算标题
  const getDynamicTitle = (meta) => {
    if (meta.dynamicTitle) {
      const key =
        globalStore.userType === 1
          ? meta.title
          : meta.title === '采购合同'
          ? '销售合同'
          : meta.title === '销售合同'
          ? '采购合同'
          : meta.title

      return $t(key)
    }
    return $t(meta.title)
  }

  const handleChangeCurrentTab = (path: string) => {
    const { tabRouters } = tabsRouterStore
    const route = tabRouters.find((i) => i.path === path)
    router.push({ path, query: route.query })
  }

  const handleRemove = ({ value: path, index }) => {
    const { tabRouters } = tabsRouterStore
    const nextRouter = tabRouters[index + 1] || tabRouters[index - 1]

    tabsRouterStore.subtractCurrentTabRouter({ path, routeIdx: index })
    if (path === route.path) router.push({ path: nextRouter.path, query: nextRouter.query })
  }

  const handleRefresh = (route: TRouterInfo, routeIdx: number) => {
    tabsRouterStore.toggleTabRouterAlive(routeIdx)
    nextTick(() => {
      tabsRouterStore.toggleTabRouterAlive(routeIdx)
      router.replace({ path: route.path, query: route.query })
    })
    activeTabPath.value = null
  }
  const handleCloseAhead = (path: string, routeIdx: number) => {
    tabsRouterStore.subtractTabRouterAhead({ path, routeIdx })

    handleOperationEffect('ahead', routeIdx)
  }
  const handleCloseBehind = (path: string, routeIdx: number) => {
    tabsRouterStore.subtractTabRouterBehind({ path, routeIdx })

    handleOperationEffect('behind', routeIdx)
  }
  const handleCloseOther = (path: string, routeIdx: number) => {
    tabsRouterStore.subtractTabRouterOther({ path, routeIdx })

    handleOperationEffect('other', routeIdx)
  }

  // 处理非当前路由操作的副作用
  const handleOperationEffect = (type: 'other' | 'ahead' | 'behind', routeIndex: number) => {
    const currentPath = router.currentRoute.value.path
    const { tabRouters } = tabsRouterStore

    const currentIdx = tabRouters.findIndex((i) => i.path === currentPath)
    // 存在三种情况需要刷新当前路由
    // 点击非当前路由的关闭其他、点击非当前路由的关闭左侧且当前路由小于触发路由、点击非当前路由的关闭右侧且当前路由大于触发路由
    const needRefreshRouter =
      (type === 'other' && currentIdx !== routeIndex) ||
      (type === 'ahead' && currentIdx < routeIndex) ||
      (type === 'behind' && currentIdx === -1)
    if (needRefreshRouter) {
      const nextRouteIdx = type === 'behind' ? tabRouters.length - 1 : 1
      const nextRouter = tabRouters[nextRouteIdx]
      router.push({ path: nextRouter.path, query: nextRouter.query })
    }

    activeTabPath.value = null
  }
  const handleTabMenuClick = (visible: boolean, ctx, path: string) => {
    if (ctx.trigger === 'document') activeTabPath.value = null
    if (visible) activeTabPath.value = path
  }
</script>

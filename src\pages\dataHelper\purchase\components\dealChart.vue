<script lang="ts" setup>
  import { init } from 'echarts'
  import { ref, watchEffect } from 'vue'
  import Empty from '@/components/Empty/index.vue'
  // import HonoroadSelect from '../../HonoroadSelect.vue'
  import productEffect from '../effect/productEffect'
  import timeEffect from '../effect/timeEffect'
  import {
    dealEchartConfig,
    DealState,
    DefaultLineResult,
    DefaultPieResult,
    LineType,
    PieType,
    pieEchartConfig,
  } from '../utils/dealConfig'

  import { purchQuantityChart } from '@/api/data/vendor'
  import { useI18n } from 'vue-i18n'
  import { useYearDataStore } from '../hooks/dataYearStore'
  import { useUserStore } from '@/store'
  import { sortBy } from 'lodash-es'

  const { t, locale } = useI18n()
  const $t = t
  const { slectVal } = storeToRefs(useYearDataStore())
  const chartElRef = useTemplateRef('chartEl')
  const chartLineElRef = useTemplateRef('chartLineEl')

  const { timeType, TimeTypeSelect, showTimeTypeSelect } = timeEffect($t)

  const { productType, ProductSelect } = productEffect()
  const charState: DealState = {}
  const globalStore = useUserStore()

  const pieLoading = ref(true)
  const pieResultEmpty = ref(false)
  const fetchPieData = async () => {
    if (!globalStore.vendorIds?.length) return
    pieLoading.value = true
    pieResultEmpty.value = false
    await nextTick()
    const { data: result } = await purchQuantityChart<PieType[]>('PurchItemStats', {
      vendorIdList: globalStore.vendorIds,
      timeType: timeType.value,
      year: slectVal.value,
    })
    pieLoading.value = false
    if (!result?.length) {
      pieResultEmpty.value = true
      charState.pieEchart?.dispose()
      charState.pieEchart = undefined
      return
    }
    const pieRes = result.length > 0 ? result : DefaultPieResult

    const itemOptions = Object.entries(
      pieRes.reduce((acc, item) => {
        acc[item.item_id] = item.item_name
        return acc
      }, {} as any),
    ).map((e) => ({ label: e[1] as any, value: e[0] as any }))
    itemOptions.unshift({ label: t('全部'), value: '' })
    ProductSelect.value = itemOptions

    if (chartElRef.value) {
      const pieChart = charState.pieEchart ? charState.pieEchart : init(chartElRef.value)
      charState.pieEchart = pieChart
      pieChart.setOption(pieEchartConfig(pieRes, t, locale.value))
    }
  }

  const lineLoading = ref(true)
  const lineResultEmpty = ref(false)
  const fetchLineData = async () => {
    if (!globalStore.vendorIds?.length) return
    lineLoading.value = true
    lineResultEmpty.value = false
    await nextTick()
    const { data: result } = await purchQuantityChart<LineType[]>('PurchaseQuarterlyStatsByItem', {
      vendorIdList: globalStore.vendorIds,
      timeType: timeType.value,
      product: productType.value,
      year: slectVal.value,
    })
    lineLoading.value = false
    if (!result?.length) {
      lineResultEmpty.value = true
      charState.lineEchart?.dispose()
      charState.lineEchart = undefined
      return
    }
    const lineRes = sortBy(result.length > 0 ? result : DefaultLineResult, 'vendor_id')
    initLineEchartHandler(slectVal.value !== 'all' ? lineRes : lineRes)
  }

  const initLineEchartHandler = (lineRes?: LineType[]) => {
    if (chartLineElRef.value && lineRes) {
      const lineEchart = charState.lineEchart ? charState.lineEchart : init(chartLineElRef.value)
      charState.lineEchart = lineEchart
      lineEchart.setOption(dealEchartConfig(lineRes, t, locale.value))
    }
  }

  watchEffect(async () => {
    if (slectVal.value) {
      await nextTick()
      fetchPieData()
      fetchLineData()
    }
  })
  watchEffect(async () => {
    if (timeType.value || productType.value) {
      await nextTick()
      fetchLineData()
      console.log(productType.value)
      console.log(timeType.value)
    }
  })
</script>
<template>
  <!-- <div class="echart-title">{{ $t('交易查询') }}</div> -->
  <t-loading size="small" :loading="pieLoading">
    <div v-if="!pieResultEmpty" class="echart-pie-container" ref="chartEl" />
    <Empty v-else :description="$t('暂无数据')" />
  </t-loading>
  <div style="margin-bottom: 0" class="select-list">
    <div class="select-container">
      <div class="label">{{ $t('产品') }}:</div>
      <t-select v-model="productType" :options="ProductSelect" />
    </div>
    <div v-if="showTimeTypeSelect" class="select-container">
      <div class="label">{{ $t('展示类型') }}:</div>
      <t-select v-model="timeType" :options="TimeTypeSelect" />
    </div>
  </div>
  <t-loading size="small" :loading="lineLoading">
    <div v-if="!lineResultEmpty" class="echart-line-container" ref="chartLineEl" />
    <Empty v-else :description="$t('暂无数据')" />
  </t-loading>
  <!-- <honoroad-select
		v-if="showTimeTypeSelect"
		v-model="timeType"
		v-model:show="showTimeType"
		:data="TimeTypeSelect"
	></honoroad-select>
	<honoroad-select
		v-model="productType"
		v-model:show="showProductType"
		:data="ProductSelect"
	></honoroad-select> -->
</template>
<style lang="less" scoped>
  @import '../style/common.less';

  .select-container {
    font-size: 14px;
    text-align: left;
    box-sizing: border-box;
    width: 280px;
    // width: 50vw;
  }
</style>

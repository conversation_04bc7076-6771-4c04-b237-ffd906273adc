<template>
  <div class="login-wrapper">
    <login-header />

    <div class="login-container">
      <div class="title-container">
        <h1 class="title margin-no">登录到</h1>
        <h1 class="title">Honoroad禾睿康SCM</h1>
        <div v-if="!userStore.canIUseWx" class="sub-title">
          <p class="tip">{{ t('没有账号吗') }}?</p>
          <p class="tip" @click="visible = true">
            <t-link theme="primary" hover="color">{{ t('注册申请') }}</t-link>
          </p>
        </div>
      </div>
      <login />
    </div>
    <!-- 注册弹窗 -->
    <Dialog v-model="visible" :footer="false" top="5%" width="360px" @close="dialogClose">
      <template #header>{{ t('注册申请') }}</template>
      <template #body>
        <t-form ref="form" :data="registerForm" :rules="FORM_RULES" @submit="onSubmit">
          <t-space direction="vertical">
            <t-form-item :label="$t('地区')" name="countryArea">
              <t-select
                size="small"
                v-model="registerForm.countryArea"
                :options="districtList"
                :placeholder="$t('请选择地区')"
                :keys="{ label: 'listLabel', value: 'listLabel' }"
              />
            </t-form-item>
            <t-form-item :label="$t('公司名称')" name="entityName">
              <t-input
                size="small"
                v-model="registerForm.entityName"
                type="text"
                clearable
                :placeholder="$t('请填写公司名称')"
              >
              </t-input>
            </t-form-item>
            <t-form-item :label="$t('税号/身份证号')" name="serialNo">
              <t-input
                size="small"
                v-model="registerForm.serialNo"
                type="text"
                clearable
                :placeholder="$t('请填写税号或身份证号')"
              >
              </t-input>
            </t-form-item>
            <t-form-item :label="$t('联系方式')" name="entityTel">
              <t-input
                size="small"
                v-model="registerForm.entityTel"
                type="text"
                clearable
                :placeholder="$t('请填写联系方式')"
              >
              </t-input>
            </t-form-item>
            <t-form-item :label="$t('联系地址')" name="entityAddress">
              <t-textarea v-model="registerForm.entityAddress" type="text" :placeholder="$t('联系地址')"> </t-textarea>
            </t-form-item>
            <t-form-item :label="$t('银行账号')" name="bankAccount">
              <t-input
                size="small"
                v-model="registerForm.bankAccount"
                type="text"
                clearable
                :placeholder="$t('银行账号')"
              >
              </t-input>
            </t-form-item>
            <t-form-item :label="$t('资质证明')">
              <t-upload
                v-model="files"
                :abridge-name="[6, 6]"
                :max="3"
                multiple
                :disabled="disabled"
                :request-method="customUpload"
                @remove="handleRemove"
              />
            </t-form-item>
            <t-button block type="submit" :loading="loading"> {{ $t('提交') }} </t-button>
          </t-space>
        </t-form>
      </template>
    </Dialog>
    <footer class="copyright">Copyright @ 2019-2025 Honoroad. All Rights Reserved</footer>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'LoginIndex',
  }
</script>
<script setup lang="ts">
  import Login from './components/Login.vue'
  import Dialog from '@/components/dialog/index.vue'
  import LoginHeader from './components/Header.vue'
  import { useUserStore } from '@/store'
  import { getNsList } from '@/api/dict'
  import { MessagePlugin, FormInstanceFunctions, FormRule, RequestMethodResponse, UploadFile } from 'tdesign-vue-next'
  import { uploadFIle, register, updateUserPwd } from '@/api/login'
  import { setToken } from '@/utils/auth'

  const { t } = useI18n()
  const $t = t
  const userStore = useUserStore()
  const visible = ref<boolean>(false)
  const formRef = useTemplateRef<FormInstanceFunctions>('form')

  const registerForm = reactive({
    countryArea: '',
    entityName: '',
    serialNo: '',
    entityTel: '',
    entityAddress: '',
    bankAccount: '',
    attachmentImages: [],
  })

  const FORM_RULES: Record<string, FormRule[]> = {
    countryArea: [{ required: true, message: $t('请选择地区') }],
    entityName: [{ required: true, message: $t('请填写公司名称') }],
    serialNo: [{ required: true, message: $t('请填写税号或身份证号') }],
    entityTel: [{ required: true, message: $t('请填写联系方式') }],
  }

  /* 地区列表 */
  const districtList = ref<any[]>([])
  const files = ref<UploadFile[]>([])
  const loading = ref<boolean>(false)
  /* 获取地区 */
  const nsList = async () => {
    const { data } = await getNsList('CUSTOMLIST456')
    districtList.value = data
  }
  const disabled = computed(() => files.value.length >= 3)
  /* 上传图片 */
  const customUpload = async (fileData: any): Promise<RequestMethodResponse> => {
    return new Promise(async (resolve, reject) => {
      try {
        const file = new FormData()
        if (Array.isArray(fileData)) {
          fileData.forEach((f) => {
            file.append('file', f.raw)
          })
        } else file.append('file', fileData.raw)
        const result = await uploadFIle(file)
        registerForm.attachmentImages.push(result.data)
        const fileUrl = import.meta.env.VITE_BASE_URL + `/scm/oss/v2/download/deliveryDocuments?filename=${result.data}`
        MessagePlugin.success($t('上传成功'))
        resolve({
          status: 'success',
          response: { url: fileUrl },
        })
      } catch (error) {
        MessagePlugin.error($t('上传失败'))
        reject({
          status: 'fail',
          error: error.msg,
          response: {},
        })
      }
    })
  }
  /* 删除图片 */
  const handleRemove = (contex: any) => {
    files.value.splice(contex.index, 1)
    registerForm.attachmentImages.splice(contex.index, 1)
  }
  /* 关闭注册弹窗 */
  const dialogClose = () => {
    visible.value = false
    formRef.value.reset()
    files.value = []
    registerForm.attachmentImages = []
  }
  /* 提交注册 */
  const onSubmit = async ({ validateResult }) => {
    if (validateResult === true) {
      const data = {
        ...registerForm,
        userId: userStore.unionId,
      }
      loading.value = true
      try {
        const registerRes = await register(data)
        if (registerRes.data) {
          setToken(registerRes.data.token)
          userStore.unionId = registerRes.data.userId
          dialogClose()
          loading.value = false

          MessagePlugin.success($t('提交成功'))
        }
      } catch {
        loading.value = false
        formRef.value.reset()
        MessagePlugin.error($t('提交失败'))
      }
    }
  }

  onMounted(() => {
    nsList()
  })
</script>

<style lang="less" scoped>
  @import url('./index.less');
  :deep(.t-dialog__footer) {
    text-align: center;
  }
  .t-space {
    width: 100%;
  }
</style>

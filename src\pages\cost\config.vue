<script setup lang="ts">
  import {
    costTplConfigList,
    getCostTplConfigDetail,
    saveCostTplConfig,
    removeCostTplConfig,
    checkCostConfigValue,
  } from '@/api/cost'
  import { useNsListRaw } from '@/use/dict'
  import { getItemByProject, loadBomByState } from '@/api/item'
  import { useProjectStore, projectCurrency, projectQuantityUnit } from '@/use/useProject'
  import { DialogPlugin, type FormInstanceFunctions, type FormRules } from 'tdesign-vue-next'
  import { useRequest } from 'vue-request'
  import { AddIcon, ChevronLeftCircleIcon } from 'tdesign-icons-vue-next'

  const quantityUnitOpts = [
    {
      label: 'KG',
      value: 1,
    },
    {
      label: 'T',
      value: 2,
    },
  ]

  const { t } = useI18n()
  const route = useRoute()
  const router = useRouter()

  // 成本模板配置编辑进入新的页面
  const templateId = computed(() => route.params.id)
  const templateName = ref(history.state?.name)

  const { activeProjectId } = storeToRefs(useProjectStore())

  const initConfigForm = () => {
    return {
      id: null,
      nsId: templateId.value,
      costName: '',
      itemId: '', // 物料选择器
      itemCode: '',
      isProduceCost: false, // 是否为生产成本
      bomId: '', // 根据选择的物料加载BOM
      isExp: false, // 是否为表达式
      costValue: '',
      currencyId: String(projectCurrency[activeProjectId.value]),
      quantityUnit: projectQuantityUnit[activeProjectId.value], // 1:KG 2:T
      costExp: '',
    }
  }
  const configFormInstance = useTemplateRef<FormInstanceFunctions>('configFormRef')
  const configForm = ref<any>(initConfigForm())
  const configFormRules: FormRules = {
    itemId: [{ required: true, message: t('请选择货品') }],
    costName: [{ required: true, message: t('请输入成本名称') }],
    bomId: [{ validator: (val: any) => (configForm.value?.isProduceCost ? !!val : true), message: t('请选择BOM') }],
    costValue: [{ validator: (val: any) => (!configForm.value?.isExp ? !!val : true), message: t('请输入成本值') }],
    costExp: [{ validator: (val: any) => (configForm.value?.isExp ? !!val : true), message: t('请输入表达式') }],
  }
  const selectedItemCode = computed(() => configForm.value.itemCode)

  // 加载文件列表
  const {
    data: configList,
    refresh,
    loading,
  } = useRequest(() => costTplConfigList(templateId.value as string).then((res) => res.data), {
    manual: false,
    initialData: [],
    refreshDeps: [templateId],
  })
  const valueNeedCheck = computed(() => (configList.value || []).filter((c) => !!c.bomId || !!c.costExp))
  const rowLoading = reactive<Record<number, boolean>>({})
  const checkLoading = computed(() => Object.values(rowLoading).some((v) => v))

  watchSyncEffect(async () => {
    if (checkLoading.value || !valueNeedCheck.value?.length) {
      return
    }
    for (const item of valueNeedCheck.value) {
      rowLoading[item.id] = true
      checkCostConfigValue(item.id).then((res) => {
        rowLoading[item.id] = false
        if (res.data?.result) {
          item.costValue = Number(res.data.result)
        }
      })
    }
  })

  // 按项目加载货品列表
  const { data: itemList, loading: itemLoading } = useRequest(
    async () => {
      if (!activeProjectId.value) {
        return []
      }

      const res = await getItemByProject(activeProjectId.value)
      return (res.data || []).map((i) => ({
        code: i.itemCode,
        label: `${i.itemCode} ${i.itemName}`,
        value: i.itemId,
      }))
    },
    {
      manual: false,
      initialData: [],
      refreshDeps: [activeProjectId],
    },
  )
  const handleItemChange = (_: any, context: any) => {
    configForm.value.itemCode = context.option.code
  }

  // 按物料加载BOM列表
  const {
    data: bomList,
    loading: bomLoading,
    refresh: refreshBom,
  } = useRequest(
    async () => {
      if (!selectedItemCode.value || !activeProjectId.value) {
        return []
      }
      const statementId = `${activeProjectId.value}-${selectedItemCode.value}`
      const res = await loadBomByState(statementId)
      return (res.data || []).map((i) => ({
        label: i.bomName,
        value: i.bomId,
        cost: i.cost,
      }))
    },
    {
      manual: false,
      initialData: [],
      refreshDeps: [activeProjectId, selectedItemCode],
    },
  )
  const handleBomChange = (_: any, context: any) => {
    if (context.option.cost) {
      configForm.value.costValue = Number(context.option.cost)
    }
  }

  const currencyList = ref<any[]>([])
  const currencyMap = ref<Record<string, any>>({})
  const loadNsCurrency = async () => {
    const res = await useNsListRaw('currency')
    currencyList.value = res.currency
    currencyMap.value = res.currency.reduce((prev, cur) => {
      prev[cur.value] = cur.label
      return prev
    }, {})
  }

  const configColumns = [
    { colKey: 'item', title: t('货品') },
    { colKey: 'costName', title: t('成本项') },
    { colKey: 'bom', title: t('BOM') },
    { colKey: 'costValue', title: t('成本值') },
    { colKey: 'action', title: t('操作'), width: '180', ellipsis: true },
  ]

  const formDialog = ref<boolean>(false)
  const handleNewConfig = () => {
    formDialog.value = true
    configForm.value = initConfigForm()
  }
  const handleEditConfig = async (item: any) => {
    console.log('🚀 ~ handleEditConfig ~ item:', item)
    formDialog.value = true
    const res = await getCostTplConfigDetail(item.id)
    configForm.value = {
      ...res.data,
      currencyId: String(res.data.currencyId),
      itemCode: item.itemCode,
      isProduceCost: !!res.data.bomId, // 是否为生产成本
      isExp: !!res.data.costExp, // 是否为表达式
    }
    await nextTick()
    refreshBom()
  }
  const saveLoading = ref<boolean>(false)
  const submitSaveConfig = async ({ validateResult }) => {
    if (validateResult !== true) return
    saveLoading.value = true
    await saveCostTplConfig(configForm.value)
    saveLoading.value = false
    formDialog.value = false
    refresh()
  }
  const handleRemoveConfig = (item: any) => {
    const dialog = DialogPlugin.confirm({
      theme: 'warning',
      header: t('提示'),
      body: t('确定删除该成本项吗？'),
      cancelBtn: t('取消'),
      confirmBtn: t('确定'),
      onConfirm: async () => {
        dialog.setConfirmLoading(true)
        await removeCostTplConfig(item.id)
        dialog.setConfirmLoading(false)
        dialog.hide()
        refresh()
      },
    })
  }

  onMounted(() => {
    loadNsCurrency()
    configForm.value = initConfigForm()
  })
</script>

<template>
  <div class="container">
    <t-card>
      <template #title>
        <t-space align="center">
          <t-button shape="circle" theme="primary" variant="text" @click="router.replace('/cost')">
            <template #icon><ChevronLeftCircleIcon /></template>
          </t-button>
          <span style="font-size: 16px">{{ templateName }}</span>
        </t-space>
      </template>
      <t-form label-width="auto" layout="inline">
        <t-form-item>
          <t-button size="small" theme="primary" @click="handleNewConfig">
            <template #icon><add-icon /></template>
            <span>{{ $t('新增成本项') }}</span>
          </t-button>
        </t-form-item>
      </t-form>
      <t-table
        style="margin-top: 20px"
        row-key="index"
        size="small"
        maxHeight="500"
        :data="configList"
        :columns="configColumns"
        :loading="loading"
      >
        <template #item="{ row }"> {{ row.itemCode }} {{ row.itemName }} </template>
        <template #bom="{ row }"> {{ row.bomName }} </template>
        <template #costValue="{ row }">
          <t-loading size="small" :loading="!!rowLoading[row.id]" show-overlay>
            <div>{{ row.costValue }} {{ currencyMap[row.currencyId] }}/{{ row.quantityUnit === 2 ? 'T' : 'KG' }}</div>
          </t-loading>
        </template>
        <template #action="{ row }">
          <t-space>
            <t-button size="small" theme="primary" variant="outline" @click.stop="handleEditConfig(row)">{{
              $t('编辑')
            }}</t-button>
            <t-button size="small" theme="danger" variant="outline" @click.stop="handleRemoveConfig(row)">{{
              $t('删除')
            }}</t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <t-dialog
      v-model:visible="formDialog"
      :header="$t('成本项')"
      confirm-on-enter
      :confirm-loading="saveLoading"
      @closed="() => configFormInstance.reset()"
      @confirm="() => configFormInstance.submit()"
    >
      <t-form
        ref="configFormRef"
        :data="configForm"
        :rules="configFormRules"
        reset-type="initial"
        label-width="9em"
        label-align="left"
        layout="inline"
        @submit="submitSaveConfig"
      >
        <t-form-item class="form-item" :label="$t('货品')" name="itemId">
          <t-select
            v-model="configForm.itemId"
            :loading="itemLoading"
            :options="itemList"
            filterable
            @change="handleItemChange"
          />
        </t-form-item>
        <t-form-item class="form-item" :label="$t('成本名称')" name="costName">
          <t-input v-model="configForm.costName" />
        </t-form-item>
        <t-form-item :label="$t('是否为生产成本')">
          <t-radio-group v-model="configForm.isProduceCost">
            <t-radio-button :value="true">{{ $t('是') }}</t-radio-button>
            <t-radio-button :value="false">{{ $t('否') }}</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item v-if="configForm.isProduceCost" class="form-item" :label="$t('货品配方')" name="bomId">
          <t-select
            v-model="configForm.bomId"
            :loading="bomLoading"
            :options="bomList"
            filterable
            @change="handleBomChange"
          />
        </t-form-item>
        <t-form-item :label="$t('是否为表达式')">
          <t-radio-group v-model="configForm.isExp">
            <t-radio-button :value="true">{{ $t('是') }}</t-radio-button>
            <t-radio-button :value="false">{{ $t('否') }}</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item v-if="configForm.isExp" class="form-item" :label="$t('成本表达式')" name="costExp">
          <t-input v-model="configForm.costExp" />
        </t-form-item>
        <t-form-item v-else class="form-item" :label="$t('成本值')" name="costValue">
          <t-input-number :readonly="!!configForm.bomId" v-model="configForm.costValue" align="left" theme="normal" />
        </t-form-item>
        <t-form-item :label="$t('成本单位')">
          <t-select borderless autoWidth size="small" v-model="configForm.currencyId" :options="currencyList" />
          <span>/</span>
          <t-select borderless autoWidth size="small" v-model="configForm.quantityUnit" :options="quantityUnitOpts" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<style scoped lang="less">
  .form-item {
    width: 100%;
  }
</style>

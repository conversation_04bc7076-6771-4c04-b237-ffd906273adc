import { use<PERSON>out<PERSON>, createRouter, RouteRecordRaw, createWebHistory } from 'vue-router'
import { uniq } from 'lodash-es'
import Layout from '@/layouts/index.vue'
import DashboardIcon from '@/assets/assets-slide-dashboard.svg'

// 自动导入modules文件夹下所有ts文件
const modules = import.meta.glob(['./modules/**/*.ts', './modules/**/*.tsx'], { eager: true })

// 路由暂存
const routeModuleList: Array<RouteRecordRaw> = []

Object.keys(modules).forEach((key) => {
  const mod = (modules[key] as any).default || {}
  const modList = Array.isArray(mod) ? [...mod] : [mod]
  routeModuleList.push(...modList)
})

// 关于单层路由，meta 中设置 { single: true } 即可为单层路由，{ hidden: true } 即可在侧边栏隐藏该路由

// 存放动态路由
export const asyncRouterList: Array<RouteRecordRaw> = [...routeModuleList]

// 存放固定的路由
const defaultRouterList: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/pages/login/index.vue'),
  },
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/home',
    component: Layout,
    meta: { title: '首页', icon: DashboardIcon },
    children: [
      {
        path: '',
        name: 'home',
        component: () => import('@/pages/home/<USER>'),
      },
    ],
  },
  {
    path: '/:w+',
    name: '404Page',
    redirect: '/result/404',
  },
]

export const allRoutes = [...defaultRouterList, ...asyncRouterList]

export const getRoutesExpanded = () => {
  const expandedRoutes = []

  allRoutes.forEach((item) => {
    if (item.meta && item.meta.expanded) {
      expandedRoutes.push(item.path)
    }
    if (item.children && item.children.length > 0) {
      item.children
        .filter((child) => child.meta && child.meta.expanded)
        .forEach((child: RouteRecordRaw) => {
          expandedRoutes.push(item.path)
          expandedRoutes.push(`${item.path}/${child.path}`)
        })
    }
  })

  return uniq(expandedRoutes)
}

export const getActive = (maxLevel = 3): string => {
  const route = useRoute()
  if (!route?.path) {
    return ''
  }

  return route.path
    .split('/')
    .filter((_item: string, index: number) => index <= maxLevel && index > 0)
    .map((item: string) => `/${item}`)
    .join('')
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: allRoutes,
  scrollBehavior() {
    return {
      el: '#app',
      top: 0,
      behavior: 'smooth',
    }
  },
})

export default router

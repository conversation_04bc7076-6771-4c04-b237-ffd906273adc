import { calcDiv1000 } from '@/utils/util'
import dayjs from 'dayjs'
import { type EChartsOption } from 'echarts'
import { min, minBy } from 'lodash-es'

export const productPieEchartConfig = (datas: any[], $t: any): EChartsOption => {
  return {
    color: [
      '#3fc9cb',
      '#215273',
      '#89e8b1',
      '#38a3a5',
      '#d2f7d5',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ],
    tooltip: {
      hideDelay: 100,
      trigger: 'item',
      formatter: ({ name, dataIndex }: any) => {
        return `${name} <br>${$t('交易量')}: ${calcDiv1000(datas[dataIndex].value)} (T)`
      },
    },
    series: [
      {
        type: 'pie',
        radius: '90%',
        top: '30px',
        center: ['50%', '50%'],
        selectedMode: 'single',
        data: datas,
        label: {
          show: true,
          position: 'inside',
          formatter: '{b}\n{d}%', // 使用 {d} 来表示百分比
        },
      },
    ],
  }
}

export const priceMixEchartConfig = (datas: any[], $t: any): EChartsOption => {
  const minYm = (minBy(datas, 'ym') as any)?.ym as string
  const now = dayjs()
  const maxYm = now.format('YYYY-MM')
  const categories = getCategories(minYm, maxYm)
  const lineData = getSingleData(categories.length)
  const barData = getSingleData(categories.length, 0)
  let maxQuantity = 0
  let maxRate = 0
  datas.forEach((d: any) => {
    let { quantity, rate, ym } = d
    quantity = Number((Number(quantity) || 0).toFixed(2))
    rate = Number((Number(rate) || 0).toFixed(2)) * 1000
    if (quantity > maxQuantity) maxQuantity = Number(quantity)
    if (rate > maxRate) maxRate = rate
    const m = Number(ym.substring(5, 7))
    const index = categories.indexOf(m)
    if (index < 0) return
    lineData.fill(rate, index)
    barData[index] = Number((quantity / 1000).toFixed(2))
  })
  const rmax: number = Number((maxRate * 1.2 || 10).toFixed(2)),
    qmax: number = Number(((maxQuantity * 1.8) / 1000 || 200).toFixed(2))
  return {
    grid: {
      left: '3%',
      right: '5%',
      bottom: '5%',
      containLabel: true,
    },
    color: [
      '#3fc9cb',
      '#215273',
      '#89e8b1',
      '#38a3a5',
      '#d2f7d5',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ],
    tooltip: {
      show: true,
    },
    legend: {
      data: [$t('单价'), $t('数量')],
      textStyle: {
        fontSize: 9,
      },
      selectedMode: false,
    },
    xAxis: [
      {
        type: 'category',
        data: categories.map((item) => item + '月'),
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: $t('单价') + '(CNY/T)',
        alignTicks: true,
        min: 0,
        max: rmax,
        interval: Number((rmax / 5).toFixed(2)),
      },
      {
        type: 'value',
        name: $t('数量') + '(T)',
        alignTicks: true,
        min: 0,
        max: qmax,
        interval: Number((qmax / 5).toFixed(2)),
      },
    ],
    series: [
      {
        name: $t('单价'),
        type: 'line',
        yAxisIndex: 0,
        data: lineData,
        label: {
          show: true,
          fontSize: 12,
        },
        labelLayout: {
          align: 'right',
          verticalAlign: 'middle',
        },
      },
      {
        name: $t('数量'),
        type: 'bar',
        yAxisIndex: 1,
        data: barData,
      },
    ],
  }
}

const getCategories = (minYm: string, maxYm: string) => {
  let y = Number(minYm.substring(0, 4))
  let m = Number(minYm.substring(5, 7))
  let execYm = String(y) + '-' + String(m).padStart(2, '0')
  const categories = []
  while (execYm <= maxYm) {
    categories.push(m)
    if (m == 12) {
      y++
      m = 1
    } else {
      m++
    }
    execYm = String(y) + '-' + String(m).padStart(2, '0')
  }
  return categories
}

const getSingleData = (length: number, content: string | number = '') => {
  const arr: any[] = []
  arr.length = length
  arr.fill(content, 0, length)
  return arr
}

export const executingOrderBarChartConfig = (categories: string[], series: number[][], $t: any): EChartsOption => {
  return {
    grid: {
      left: '3%',
      right: '5%',
      bottom: '15%',
      containLabel: true,
    },
    color: [
      '#3fc9cb',
      '#215273',
      '#89e8b1',
      '#38a3a5',
      '#d2f7d5',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ],
    tooltip: {
      show: true,
    },
    legend: {
      data: [$t('合同量'), $t('待交货量')],
      textStyle: {
        fontSize: 9,
      },
      selectedMode: false,
    },
    xAxis: [
      {
        type: 'category',
        data: categories,
        axisLabel: {
          rotate: 30,
          overflow: 'break',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: $t('数量') + '(T)',
        alignTicks: true,
        min: min([min(series[0]), min(series[1])]),
      },
    ],
    series: [
      {
        name: $t('合同量'),
        type: 'bar',
        data: series[0],
      },
      {
        name: $t('待交货量'),
        type: 'bar',
        data: series[1],
      },
    ],
  }
}

export const executedOrderBarChartConfig = (categories: string[], series: number[][], $t: any): EChartsOption => {
  return {
    grid: {
      left: '3%',
      right: '5%',
      bottom: '15%',
      containLabel: true,
    },
    color: [
      '#3fc9cb',
      '#215273',
      '#89e8b1',
      '#38a3a5',
      '#d2f7d5',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ],
    tooltip: {
      show: true,
    },
    legend: {
      data: [$t('合同量'), $t('开票量')],
      textStyle: {
        fontSize: 9,
      },
      selectedMode: false,
    },
    xAxis: [
      {
        type: 'category',
        data: categories,
        axisLabel: {
          rotate: 30,
          overflow: 'break',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: $t('数量') + '(T)',
        alignTicks: true,
        min: min([min(series[0]), min(series[1])]),
      },
    ],
    series: [
      {
        name: $t('合同量'),
        type: 'bar',
        data: series[0],
      },
      {
        name: $t('开票量'),
        type: 'bar',
        data: series[1],
      },
    ],
  }
}

export const fundBarChartConfig = (
  currencyType: string,
  categories: string[],
  series: number[][],
  $t: any,
  locale: any,
): EChartsOption => {
  // 格式化 X 轴数值
  const formatXAxisValue = (value: number) => {
    if (value >= 100000000) {
      return `${(value / 100000000).toFixed(1)}${$t('亿')}` // 大于 1 亿时显示为 "X亿"
    }
    return value.toString() // 否则正常显示
  }
  /* 字体自适应 */
  const fontSize = (size: number) => {
    let docEl = document.documentElement,
      clientWidth = window.innerWidth || docEl.clientWidth || document.body.clientWidth
    // 窗口的宽度
    if (!clientWidth) return
    let fontSize = 10 * (clientWidth / 375)
    return size * fontSize
  }
  return {
    grid: {
      left: '3%',
      right: '5%',
      bottom: '15%',
      containLabel: true,
    },
    title: {
      text: `${currencyType}`,
      left: 0,
      top: 0,
      textStyle: {
        fontSize: 14,
      },
    },
    color: ['#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
    legend: {
      data: [$t('预付金额'), $t('应付金额')],
      textStyle: {
        fontSize: 9,
      },
      selectedMode: false,
    },
    xAxis: [
      {
        type: 'value',
        name: $t('金额'),
        axisLabel: {
          rotate: 45,
          formatter: (value: number) => formatXAxisValue(value),
        },

        axisTick: {
          show: false,
        },
        min: 0,
      },
    ],
    yAxis: [
      {
        type: 'category',
        data: categories,
        axisLabel: {
          width: 92,
          interval: 0,
          formatter: function (value: string) {
            const match = value.match(/[\u4e00-\u9fa5]+/g)
            return match ? match.join('') : value
          },
          fontSize: 14,
          overflow: 'break',
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: $t('预付金额'),
        type: 'bar',
        label: {
          show: true,
          position: 'inside',
          fontSize: 12,
          formatter: function ({ value }: any) {
            return new Intl.NumberFormat(locale.value, {
              style: 'decimal',
              notation: 'compact',
              compactDisplay: 'long',
              minimumFractionDigits: 3,
              roundingMode: 'halfFloor',
            }).format(Number(value))
          },
        },
        labelLayout: {
          align: 'left',
          verticalAlign: 'middle',
        },
        data: series[0],
      },
      {
        name: $t('应付金额'),
        type: 'bar',
        label: {
          show: true,
          position: 'inside',
          fontSize: 12,
          formatter: function ({ value }: any) {
            return new Intl.NumberFormat(locale.value, {
              style: 'decimal',
              notation: 'compact',
              compactDisplay: 'long',
              minimumFractionDigits: 3,
              roundingMode: 'halfFloor',
            }).format(Number(value))
          },
        },
        labelLayout: {
          align: 'left',
          verticalAlign: 'middle',
        },
        data: series[1],
      },
    ],
  }
}

export const quoteBarChartConfig = (
  categories: string[],
  series: number[][],
  rmax = 10,
  qmax = 100,
  $t: any,
): EChartsOption => {
  return {
    grid: {
      left: '3%',
      right: '5%',
      bottom: '15%',
      containLabel: true,
    },
    color: [
      '#3fc9cb',
      '#215273',
      '#89e8b1',
      '#38a3a5',
      '#d2f7d5',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
    ],
    tooltip: {
      show: true,
    },
    legend: {
      data: [$t('报价次数'), $t('中标次数'), $t('中标率')],
      textStyle: {
        fontSize: 9,
      },
      selectedMode: false,
    },
    xAxis: [
      {
        type: 'category',
        data: categories,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: $t('次数'),
        alignTicks: true,
        min: 0,
        max: rmax,
      },
      {
        type: 'value',
        name: $t('中标率'),
        alignTicks: true,
        min: 0,
        max: qmax,
      },
    ],
    series: [
      {
        name: $t('报价次数'),
        type: 'bar',
        data: series[0],
      },
      {
        name: $t('中标次数'),
        type: 'bar',
        data: series[1],
      },
      {
        name: $t('中标率'),
        type: 'line',
        data: series[2],
      },
    ],
  }
}

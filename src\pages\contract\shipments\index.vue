<script setup lang="ts">
  import useShipments from './hooks'
  const { t } = useI18n()
  const $t = t
  const {
    btnActive,
    waybillStatus,
    waybillList,
    changeStatus,
    displayColumns,
    columnControllerVisible,
    columnControllerConfig,
    pagination,
    columns,
    loading,
    refresh,
    waybillDetail,
    searchForm,
    searchOrder,
    onReset,
  } = useShipments()
</script>
<template>
  <div class="container">
    <t-card>
      <t-form layout="inline" label-width="auto" :data="searchForm" @submit="searchOrder" @reset="onReset">
        <t-form-item :label="$t('模糊搜索')">
          <t-input size="small" :placeholder="$t('请输入搜索内容')" v-model="searchForm.searchValue" />
        </t-form-item>
        <t-form-item :label="$t('车船号')">
          <t-input size="small" :placeholder="$t('请输入车船号')" v-model="searchForm.vehicleNo" />
        </t-form-item>
        <t-form-item :label="$t('运单号')">
          <t-input size="small" :placeholder="$t('请输入运单号')" v-model="searchForm.ladingBillNo" />
        </t-form-item>
        <t-form-item :label="$t('目的地')">
          <t-input size="small" :placeholder="$t('请输入目的地')" v-model="searchForm.consigneeAddress" />
        </t-form-item>
        <t-form-item :label="$t('合同号')">
          <t-input size="small" :placeholder="$t('请输入合同号')" v-model="searchForm.contractNo" />
        </t-form-item>
        <t-form-item>
          <t-button size="small" type="submit">{{ $t('搜索') }}</t-button>
          <t-button size="small" theme="default" type="reset">{{ $t('重置') }}</t-button>
        </t-form-item>
      </t-form>
      <div class="waybillStatus">
        <template v-for="w in waybillStatus" :key="w.id">
          <t-button size="small" :theme="btnActive == w.id ? 'success' : 'primary'" @click="changeStatus(w.id)">{{
            w.title
          }}</t-button>
          <img
            class="img"
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGIAAAASCAYAAACghwvPAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAACrSURBVFhH7ZixCsQgEAXz/39oY6NWgtpYGxY05OJdsbtHIuENDGzSpBhE49bAEiCEklJKn3QghJIYY8s59yc5CKGEQhhj1DEQQskIQaaU+ls+rBDOOXjRWnuE0MRghTh/EH7qvT9mSQxRiFor/CIFkMYQhQC/CSGIYmBF/NHbVwScpY17zNwIBCvE9cQAHzo1gZlH/iPAzAihiUAghBLcNS0Cbl9fBkIsQWs7z5rmJA4hcEkAAAAASUVORK5CYII="
          />
        </template>
      </div>
      <t-space class="space">
        <t-button size="small" shape="circle" variant="outline" @click="refresh">
          <template #icon><t-icon name="refresh" /></template>
        </t-button>
        <t-button size="small" shape="circle" variant="outline" @click="columnControllerVisible = true">
          <template #icon><t-icon name="setting" /></template>
        </t-button>
      </t-space>
      <t-table
        row-key="index"
        size="small"
        v-model:displayColumns="displayColumns"
        v-model:columnControllerVisible="columnControllerVisible"
        :data="waybillList"
        :columns="columns"
        :column-controller="columnControllerConfig"
        :loading="loading"
        max-height="340px"
        :pagination="pagination"
        lazy-load
      >
        <template #link="{ row }">
          <t-link theme="primary" @click="waybillDetail(row)">{{ $t('详情') }}</t-link>
        </template>
      </t-table>
    </t-card>
  </div>
</template>
<style scoped lang="less">
  .waybillStatus {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    .img:last-child {
      display: none;
    }
    .img {
      width: 50px;
      height: 10px;
    }
  }
  .space {
    width: 100%;
    justify-content: flex-end;
    margin: 20px 0;
    :deep(.t-space-item) {
      width: auto;
    }
  }
</style>

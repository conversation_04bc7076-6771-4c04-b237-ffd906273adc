import { useUserStore } from '@/store'
import { useNsListLabelRaw, useDictLabelRaw } from '@/use/dict'
import { getDispatchItem } from '@/api/shipping'
import { useSessionStorage } from '@vueuse/core'
import { groupBy } from 'lodash-es'
import { TreeProps, TreeNodeModel, PrimaryTableCol, TableRowData } from 'tdesign-vue-next'
import { purchSaleContractDetailTmsApi, dispatchCountInfoApi } from '@/api/contract'
import { formatNumber } from '@/utils/tools'
import { useRouter } from 'vue-router'

export default () => {
  const router = useRouter()
  const { t, locale } = useI18n()
  const $t = t
  const globalStore = useUserStore()

  /* tablist */
  const tabList = reactive([
    { label: globalStore.userType === 1 ? $t('待交货') : $t('待收货'), value: 1 },
    { label: globalStore.userType === 1 ? $t('待收货') : $t('待交货'), value: 2 },
  ])
  const tabLoading = ref<boolean>(false)

  /* form表单 */
  const DEFAULT_FORM = {
    searchValue: '',
    vehicleNo: '',
    ladingBillNo: '',
    consigneeAddress: '',
    contractNo: '',
  }
  const searchForm = ref({ ...DEFAULT_FORM })
  /* 状态列表 */
  const statusList = reactive([
    { id: 0, title: $t('待发货'), statusList: [1, 2, 3, 5, 6], count: 0 },
    { id: 1, title: $t('在途'), statusList: [7, 9], count: 0 },
    { id: 2, title: $t('已收货'), statusList: [8, 10], count: 0 },
  ])
  /* 收货状态id  */
  const statusId = ref<number>(0)
  const defaultValue = ref<any[]>([])
  /* 提货方式 */
  const pickMethod = ref<any>({})

  /* 字典查询方式 */
  const getLabel = async () => {
    const { transport_dispatch_status } = await useDictLabelRaw('transport_dispatch_status')
    pickMethod.value = transport_dispatch_status
  }

  /* 项目产品列表 */
  const projectItemList = ref<any[]>([])
  const orderType = useSessionStorage('scm-shipping-order-type', 1)
  /* 请求参数 */
  const params = ref({
    pageNum: 1,
    pageSize: 9999,
    statusList: statusList[statusId.value].statusList || statusList[0].statusList,
    customerIdList: globalStore.customerIds,
    vendorIdList: globalStore.vendorIds,
    projectIdList: globalStore.projectIds,
    materialName: '',
    materialCode: '',
    orderType: orderType.value,
  })

  /* 订单列表 */
  const orderList = ref<any[]>([])
  const loading = ref<boolean>(false)
  /* 表格分页 */
  const pagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })
  /* 表格配置 */
  const columns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'ladingBillNo', title: $t('运单号'), width: '120', fixed: 'left', align: 'center', ellipsis: true },
    {
      colKey: 'dispatchStatus',
      title: $t('订单状态'),
      width: '100',
      align: 'center',
      ellipsis: true,
    },
    { colKey: 'materialName', title: $t('产品'), width: '120', align: 'center', ellipsis: true },
    {
      colKey: 'materialNum',
      title: $t('数量'),
      width: '100',
      align: 'center',
      cell: (h, { col, row }) =>
        row.materialNum
          .split('/')
          .map((it: any) => formatNumber(it / 1000))
          .join('/') + 't',
    },
    { colKey: 'vehicleNo', title: $t('车船号'), width: '150', align: 'center', ellipsis: true },
    { colKey: 'cargoOwnerName', title: $t('发货人'), width: '150', align: 'center', ellipsis: true },
    { colKey: 'consigneeAddress', title: $t('目的地'), width: '150', align: 'center', ellipsis: true },
    { colKey: 'deliveryTime', title: $t('起运日期'), width: '100', align: 'center', ellipsis: true },
    { colKey: 'arriveTime', title: $t('预计到货日期'), width: '100', align: 'center', ellipsis: true },
    {
      colKey: 'nowPosition',
      title: $t('当前位置'),
      width: '100',
      ellipsis: true,
      align: 'center',
      cell: (h, { col, row }) => row.nowPosition || $t('未知'),
    },
    { colKey: 'link', title: $t('操作'), width: '100', fixed: 'right', align: 'center', ellipsis: true },
  ])

  /* 项目产品列表 */
  const getItemList = async () => {
    const { CUSTOMLIST_PROJECT_LIST } = await useNsListLabelRaw('CUSTOMLIST_PROJECT_LIST')
    projectItemList.value = []
    defaultValue.value = []
    tabLoading.value = true
    const res = await getDispatchItem({
      orderType: orderType.value,
      customerIdList: globalStore.customerIds,
    })
    projectItemList.value = Object.entries(groupBy(res.data, 'projectId')).map(([key, value]) => ({
      value: key,
      label: CUSTOMLIST_PROJECT_LIST[key],
      isParent: true,
      items: value.map((v) => ({
        ...v,
        label: v.itemName,
        value: v.itemCode,
      })),
    }))
    tabLoading.value = false
    params.value.materialCode = projectItemList.value[0].items[0].itemCode
    defaultValue.value.push(projectItemList.value[0].items[0].itemCode)
  }

  /* 切换tabs */
  const changeTab = async (tab: any) => {
    params.value.orderType = tab
    await getItemList()
    await tmsOrderNo()
  }

  /* 切换产品 */
  const handleClick: TreeProps['onClick'] = (context: { node: TreeNodeModel; e: MouseEvent }) => {
    const { data } = context.node
    if (data.items) return
    params.value.materialCode = data.itemCode
    tmsOrderNo()
  }
  /* 切换货品状态 */
  const changeStatus = async (s: any) => {
    statusId.value = s.id
    params.value.statusList = s.statusList
    await tmsOrderNo()
  }
  /* 获取TMS运单 */
  const tmsOrderNo = async () => {
    loading.value = true
    try {
      const res = await purchSaleContractDetailTmsApi({
        ...params.value,
        ...searchForm.value,
      })
      orderList.value = res.rows
      pagination.value.total = res.total

      const countInfoRes = await dispatchCountInfoApi({
        customerIdList: globalStore.customerIds,
        vendorIdList: globalStore.vendorIds,
        projectIdList: globalStore.projectIds,
        materialCode: params.value.materialCode,
        orderType: orderType.value,
      })
      statusList.forEach((item: any) => {
        item.count = 0
      })
      ;(countInfoRes.data || []).forEach((item: any) => {
        const targetMenu = statusList.find((d) => d.statusList.includes(Number(item.dispatchStatus)))
        if (targetMenu) {
          targetMenu.count += item.dispatchStatusCount
        }
      })
    } finally {
      loading.value = false
    }
  }
  /* 搜索订单 */
  const searchOrder = async () => await tmsOrderNo()
  /* 表单重置 */
  const onReset = async () => {
    searchForm.value = { ...DEFAULT_FORM }
    await tmsOrderNo()
  }
  /* 运单详情 */
  const waybillDetail = (row: any) => {
    router.push({
      path: '/contract/shipments-details',
      state: {
        info: JSON.stringify(row),
      },
    })
  }
  watch(
    () => locale.value,
    () => {
      statusList[0].title = t('待发货')
      statusList[1].title = t('在途')
      statusList[2].title = t('已收货')
    },
  )
  onMounted(async () => {
    getLabel()
    await getItemList()
    await tmsOrderNo()
  })

  return {
    tabList,
    tabLoading,
    orderType,
    globalStore,
    projectItemList,
    defaultValue,
    handleClick,
    searchForm,
    statusList,
    changeStatus,
    statusId,
    orderList,
    columns,
    pagination,
    loading,
    pickMethod,
    waybillDetail,
    searchOrder,
    onReset,
    changeTab,
  }
}

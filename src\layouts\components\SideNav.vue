<template>
  <div :class="sideNavCls">
    <t-menu
      width="230px"
      :class="menuCls"
      :theme="theme"
      :value="$route.path"
      :collapsed="collapsed"
      expandMutex
      :default-expanded="defaultExpanded"
    >
      <template #logo>
        <span v-if="showLogo" :class="`${prefix}-side-nav-logo-wrapper`" @click="goHome">
          <component :is="getLogo()" :class="`${prefix}-side-nav-logo-${collapsed ? 't' : 'tdesign'}-logo`" />
        </span>
      </template>
      <menu-content :nav-data="menu" />
      <template #operations>
        <span class="version-container"> {{ !collapsed ? 'Honoroad SCM' : '' }} {{ pgk.version }} </span>
      </template>
    </t-menu>
    <div :class="`${prefix}-side-nav-placeholder${collapsed ? '-hidden' : ''}`"></div>
  </div>
</template>

<script setup lang="tsx">
  import { computed, onMounted } from 'vue'
  import type { PropType } from 'vue'
  import { useRouter } from 'vue-router'
  import { union } from 'lodash-es'

  import { useSettingStore } from '@/store'
  import { prefix } from '@/config/global'
  import pgk from '../../../package.json'
  import type { MenuRoute } from '@/types/interface'
  import { getActive, getRoutesExpanded } from '@/router'

  // import AssetLogo from '@/assets/assets-t-logo.svg?component'
  // import AssetLogoFull from '@/assets/assets-logo-full.svg?component'
  import ShortLogo from '@/assets/cmp_logo.png'
  import LongLogo from '@/assets/assets-company-logo.png'
  import MenuContent from './MenuContent.vue'
  import { Image } from 'tdesign-vue-next'

  const MIN_POINT = 992 - 1

  const props = defineProps({
    menu: {
      type: Array as PropType<MenuRoute[]>,
      default: () => [],
    },
    showLogo: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    isFixed: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    layout: {
      type: String as PropType<string>,
      default: '',
    },
    headerHeight: {
      type: String as PropType<string>,
      default: '64px',
    },
    theme: {
      type: String as PropType<'light' | 'dark'>,
      default: 'light',
    },
    isCompact: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  })

  const collapsed = computed(() => useSettingStore().isSidebarCompact)

  const active = computed(() => getActive())

  const defaultExpanded = computed(() => {
    const path = getActive()
    const parentPath = path.substring(0, path.lastIndexOf('/'))
    const expanded = getRoutesExpanded()
    return union(expanded, parentPath === '' ? [] : [parentPath])
  })

  const sideNavCls = computed(() => {
    const { isCompact } = props
    return [
      `${prefix}-sidebar-layout`,
      {
        [`${prefix}-sidebar-compact`]: isCompact,
      },
    ]
  })

  const menuCls = computed(() => {
    const { showLogo, isFixed, layout } = props
    return [
      `${prefix}-side-nav`,
      {
        [`${prefix}-side-nav-no-logo`]: !showLogo,
        [`${prefix}-side-nav-no-fixed`]: !isFixed,
        [`${prefix}-side-nav-mix-fixed`]: layout === 'mix' && isFixed,
      },
    ]
  })

  const router = useRouter()
  const settingStore = useSettingStore()

  const autoCollapsed = () => {
    const isCompact = window.innerWidth <= MIN_POINT
    settingStore.updateConfig({
      isSidebarCompact: isCompact,
    })
  }

  onMounted(() => {
    autoCollapsed()
    window.onresize = () => {
      autoCollapsed()
    }
  })

  const goHome = () => {
    router.push('/home')
  }

  const getLogo = () => {
    if (collapsed.value) return <img src={ShortLogo} style="width: 64px; object-fit: cover;" />
    return <img src={LongLogo} style="object-fit: cover; object-position: 100% 83%" />
  }
</script>

<style lang="less" scoped>
  :deep(.t-menu__logo) {
    span {
      margin-left: var(--td-comp-margin-s);
    }
  }
  :deep(.t-default-menu.t-is-collapsed .t-menu__logo) {
    span {
      margin-left: var(--td-comp-margin-s);
    }
  }
</style>

import { useNsListLabelRaw } from '@/use/dict'
import {
  purchContractDetailApi,
  uploadOrderFileApi,
  purchContractDetailStatisticsApi,
  orderFileListApi,
  deleteOrderFileApi,
  getLicenseApi,
} from '@/api/contract'
import type { UploadFile, RequestMethodResponse } from 'tdesign-vue-next'
import { useRouter } from 'vue-router'
import Sess from '@/plugins/cache'
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'

export default () => {
  const { t, locale } = useI18n()
  const $t = t
  const router = useRouter()

  /* 订单详情 */
  const orderInfo = ref()
  Sess.session.set('purchInfo', history?.state?.info)
  const info = history.state.info
  /* 确保面包屑切换时特面不报错 */
  if (info) orderInfo.value = JSON.parse(info)
  else orderInfo.value = JSON.parse(Sess.session.get('purchInfo'))

  /* 金额币种 */
  const currencyType = ref<any>({})
  /* 交易方式 */
  const tradeType = ref<{ [key: string]: any }>({})
  /* 提货方式 */
  const pickMethod = ref<any>({})
  /* 已付款总金额 */
  const totalPayment = ref<any>([])
  /* 采购账单 */
  const purchaseInfo = ref<any>([])
  /* 预付款信息 */
  const prepaymentInfo = ref<any>([])
  /* 银行流水信息 */
  const accountStatement = ref<any>([])
  /* 总量 */
  const totalMaterialNum = ref<number>(0)
  /* 质检信息 */
  const qualityInfo = ref<any>([])
  /* 入库信息 */
  const inventoryInfo = ref<any>([])
  /* 质检总数量 */
  const totalQuality = ref<any>([])
  /* 总入库数量 */
  const totalInventory = ref<any>([])
  /* 待发运量 */
  const waitingDespatch = ref<number>(0)
  /* 已发运量 */
  const totalDespatch = ref<number>(0)
  /* 已签收数量 */
  const materialTotalSignNum = ref<number>(0)
  /* 步骤条active */
  const stepsActive = ref<number>(0)
  /* 质检弹窗 */
  const qualityVisible = ref<boolean>(false)
  /* 入库弹窗 */
  const inventoryVisible = ref<boolean>(false)
  /* 已付款弹窗 */
  const paymentVisible = ref<boolean>(false)
  /* 许可证弹窗 */
  const licenceVisible = ref<boolean>(false)
  /* 交单弹窗 */
  const orderVisible = ref<boolean>(false)
  /* 许可证信息 */
  const licenceInfo = ref<any>([])
  /* 文件列表 */
  const fileList = ref<any>([])
  const files = ref<any[]>([])
  /* loading */

  const fileLoading = ref<boolean>(false)
  const stepLoading = ref<boolean>(false)
  const desLoading = ref<boolean>(false)

  /* 表格配置 */
  const purchaseColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'number', title: $t('付款单号'), ellipsis: true, width: 100 },
    { colKey: 'grossamount', title: $t('付款金额'), ellipsis: true, width: 100 },
    { colKey: 'memo', title: $t('付款事由'), ellipsis: true, width: 150 },
    { colKey: 'created', title: $t('付款日期'), ellipsis: true, width: 80 },
  ])
  /* 文件列表配置 */
  const fileColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'orderNo', title: $t('采购单号'), ellipsis: true, width: 150 },
    { colKey: 'filename', title: $t('文件名称'), ellipsis: true, width: 150 },
    { colKey: 'updateTime', title: $t('上传时间'), ellipsis: true, width: 150 },
    { colKey: 'link', title: $t('操作'), ellipsis: true, width: 100 },
  ])
  /* 已质检表格配置 */
  const qualityColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'qa_code', title: $t('质检编号'), ellipsis: true, width: 80, align: 'center' },
    { colKey: 'quantity', title: $t('数量'), ellipsis: true, width: 50, align: 'center' },
    { colKey: 'check_result', title: $t('质检结果'), ellipsis: true, width: 150, align: 'center' },
    { colKey: 'pick_method', title: $t('提货方式'), ellipsis: true, width: 80, align: 'center' },
    { colKey: 'created', title: $t('质检时间'), ellipsis: true, width: 80, align: 'center' },
  ])
  /* 已入库表格配置 */
  const inventoryColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'tranid', title: $t('入库单号'), ellipsis: true, align: 'center' },
    { colKey: 'quantity', title: $t('数量'), ellipsis: true, align: 'center' },
    { colKey: 'created', title: $t('入库时间'), ellipsis: true, align: 'center' },
  ])
  /* 分页配置 */
  const filePagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  /* 获取项目label字典 */
  const getProjectLabel = async () => {
    const { currency, CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE, CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI } =
      await useNsListLabelRaw(
        'CUSTOMLIST_PROJECT_LIST',
        'currency',
        'CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE',
        'CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI',
      )

    currencyType.value = currency
    tradeType.value = CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE
    pickMethod.value = CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI
  }
  /* 付款、质检、入库信息 */
  const getOrderInfo = async () => {
    stepLoading.value = true
    desLoading.value = true
    try {
      const { data } = await purchContractDetailApi(orderInfo.value.order_id)
      purchaseInfo.value = data.paid.filter((item: any) => item.type == '0')
      prepaymentInfo.value = data.paid.filter((item: any) => item.type == '1')
      inventoryInfo.value = data.itemRcpt
      totalInventory.value = data.itemRcpt.reduce((a: any, b: any) => Number(a) + Number(b.quantity), 0)

      qualityInfo.value = data.qa
      totalQuality.value = data.qa.reduce((a: any, b: any) => Number(a) + Number(b.quantity), 0)
      accountStatement.value = data.files.filter(
        (item: any) => item.custrecord_pfb_filename == 62 || item.custrecord_pfb_filename == 74,
      )
      const yifukuan = purchaseInfo.value.reduce((a: any, b: any) => Number(a) + Number(b.grossamount), 0)
      const yufukuan = prepaymentInfo.value.reduce((a: any, b: any) => Number(a) + Number(b.grossamount), 0)
      totalPayment.value = Math.max(yifukuan, yufukuan)
      if (totalQuality.value) stepsActive.value = 3
      if (totalInventory.value) stepsActive.value = 4
    } finally {
      stepLoading.value = false
      desLoading.value = false
    }
  }
  /* TMS发运量统计 */
  const getTmsStatistics = async () => {
    const params = {
      pageNum: '1',
      pageSize: orderInfo.value?.item_name.split('/').length,
      orderNo: orderInfo.value?.order_no,
    }
    stepLoading.value = true

    const res = await purchContractDetailStatisticsApi(params, 'purch')
    stepLoading.value = false
    totalDespatch.value = (res as any).rows?.reduce((a: any, b: any) => Number(a) + Number(b.materialExecutedNum), 0)

    waitingDespatch.value = (res as any).rows?.reduce(
      (a: any, b: any) => Number(a) + Number(b.materialUnexecutedNum),
      0,
    )
    materialTotalSignNum.value = (res as any).rows?.reduce(
      (a: any, b: any) => Number(a) + Number(b.materialTotalSignNum),
      0,
    )
    totalMaterialNum.value = orderInfo.value?.item_quantity
      .split('/')
      .reduce((sum: number, val: string) => sum + Number(val), 0)

    if (waitingDespatch.value) stepsActive.value = 1
    if (totalDespatch.value) stepsActive.value = 2
  }
  /* 获取许可证列表 */
  const getLicense = async () => {
    const { data } = await getLicenseApi(orderInfo.value?.order_no)
    licenceInfo.value = data
  }
  /* 获取交单列表 */
  const orderFileList = async () => {
    fileLoading.value = true
    try {
      const { data } = await orderFileListApi({
        nsOrderId: orderInfo.value?.order_id,
        orderNo: orderInfo.value?.order_no,
      })
      fileList.value = data
      filePagination.value.total = fileList.value.length
    } finally {
      fileLoading.value = false
    }
  }
  /* 查看合同附件 */
  const downloadFile = () => window.open(orderInfo.value.contract_files, '_blank')
  /* 银行流水下载 */
  const downLoadFile = (url: string) => window.open(url, '_blank')
  /* 下载交单文件 */
  const donloadFile = (id: number) => {
    const fileUrl = import.meta.env.VITE_APP_BASE_API + `/tms/order/file/download/${id}`
    location.assign(fileUrl)
  }
  /* 删除交单文件*/
  const deleteFile = async (id: number) => {
    try {
      await deleteOrderFileApi(id)
      MessagePlugin.success($t('删除成功'))
      orderFileList()
    } finally {
    }
  }
  /* 交单上传附件 */
  const customUpload = async (file: UploadFile | UploadFile[]): Promise<RequestMethodResponse> => {
    return new Promise(async (resolve) => {
      try {
        const formData = new FormData()
        if (Array.isArray(file)) {
          file.forEach((f) => {
            formData.append('file', f.raw)
          })
        } else formData.append('file', file.raw)

        const result = await uploadOrderFileApi(orderInfo.value.order_id, orderInfo.value.order_no, formData)
        MessagePlugin.success($t('上传成功'))
        await orderFileList()
        files.value = []
        resolve({
          status: 'success',
          response: { url: result.msg },
        })
      } catch (error) {
        MessagePlugin.error($t('上传失败'))
        resolve({
          status: 'fail',
          error: error.msg,
          response: {},
        })
      }
    })
  }
  /* 格式化金额 */
  const formatCurrency = (value: any, currencyId: number) => {
    if (!currencyType.value[currencyId]) return ''
    if (value === null || value === undefined) return ''
    const number = parseFloat(value)
    if (isNaN(number)) return ''
    return new Intl.NumberFormat(locale.value, {
      style: 'currency',
      currencyDisplay: 'code',
      currency: currencyType.value[currencyId],
      notation: 'compact',
      compactDisplay: 'long',
      minimumFractionDigits: 2,
      roundingMode: 'halfFloor',
    }).format(value)
  }

  /* 跳转运单列表 */
  const toWaybill = () => router.push(`/contract/shipments/${orderInfo.value?.order_no}`)

  onMounted(async () => {
    await getProjectLabel()
    await getTmsStatistics()
    await getOrderInfo()
    await orderFileList()
    await getLicense()
  })

  return {
    orderInfo,
    currencyType,
    tradeType,
    totalPayment,
    stepsActive,
    totalMaterialNum,
    totalDespatch,
    qualityInfo,
    totalInventory,
    totalQuality,
    qualityVisible,
    inventoryInfo,
    inventoryVisible,
    licenceInfo,
    paymentVisible,
    accountStatement,
    orderVisible,
    downLoadFile,
    purchaseInfo,
    purchaseColumns,
    prepaymentInfo,
    formatCurrency,
    fileLoading,
    filePagination,
    fileList,
    files,
    fileColumns,
    donloadFile,
    deleteFile,
    customUpload,
    qualityColumns,
    pickMethod,
    inventoryColumns,
    licenceVisible,
    downloadFile,
    stepLoading,
    desLoading,
    toWaybill,
  }
}

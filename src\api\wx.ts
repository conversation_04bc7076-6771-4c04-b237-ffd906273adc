import request from '@/utils/request'

export function getMpSignature(url?: string) {
  return request({
    url: '/auth/wx/mp/portal/signature',
    method: 'get',
    params: {
      pageUrl: url ?? location.href,
    },
  })
}

export function getCpSignature(url?: string) {
  return request({
    url: '/auth/wx/cp/portal/scm/signature',
    method: 'get',
    params: {
      pageUrl: url ?? location.href,
    },
  })
}

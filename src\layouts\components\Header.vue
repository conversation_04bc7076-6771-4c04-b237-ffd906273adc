<template>
  <div :class="layoutCls">
    <t-head-menu :class="menuCls" :theme="theme" expand-type="popup" :value="active">
      <template #logo>
        <span v-if="showLogo" class="header-logo-container" @click="handleNav('/home')">
          <logo-full class="t-logo" />
        </span>
        <div v-else class="header-operate-left">
          <t-button theme="default" shape="square" variant="text" @click="changeCollapsed">
            <t-icon class="collapsed-icon" name="view-list" />
          </t-button>
        </div>
      </template>
      <template v-if="layout !== 'side'" #default>
        <menu-content class="header-menu" :nav-data="menu" />
      </template>
      <template #operations>
        <div class="operations-container">
          <!-- <t-select v-model="activeProjectId" label="项目：" placeholder="全部项目" clearable>
            <t-option v-for="item in menuOptions" :key="item.value" :value="item.value" :label="item.text" />
          </t-select> -->
          <t-button
            v-if="userStore.userType !== 1 && userStore.routes.includes('/feedback')"
            theme="default"
            shape="square"
            variant="text"
            @click="visible = true"
          >
            <t-icon name="mark-as-unread" />
          </t-button>
          <!-- </t-tooltip> -->

          <t-popup trigger="click" placement="bottom-left">
            <t-button theme="default" shape="square" variant="text">
              <t-icon name="mail" />
            </t-button>

            <template #content>
              <div class="message">
                <div class="mess-top">
                  <h4>{{ $t('收件箱') }}</h4>
                </div>
                <div class="mess-center">
                  <t-list size="small" style="max-height: 260px" v-if="feedbackList.length">
                    <t-list-item v-for="mess in feedbackList" :key="mess.id">
                      <t-list-item-meta :title="mess.messageTitle">
                        <template #description>
                          <div>{{ mess.messageContent }}</div>
                          <div class="info">
                            <div>{{ $t('发送') }}：{{ mess.userName }}</div>
                            <div>{{ $t('接收') }}：{{ mess.toUserName }}</div>
                          </div>
                        </template>
                      </t-list-item-meta>
                    </t-list-item>
                  </t-list>
                  <Empty v-else />
                </div>
              </div>
            </template>
          </t-popup>

          <t-dropdown trigger="click">
            <t-button theme="default" shape="square" variant="text">
              <t-icon name="translate" />
            </t-button>
            <t-dropdown-menu>
              <t-dropdown-item
                v-for="(lang, index) in langList"
                :key="index"
                :value="lang.value"
                @click="(options) => changeLang(options.value as string)"
                >{{ lang.content }}</t-dropdown-item
              ></t-dropdown-menu
            >
          </t-dropdown>

          <t-dropdown :min-column-width="120" trigger="click">
            <template #dropdown>
              <t-dropdown-menu>
                <t-dropdown-item class="operations-dropdown-container-item" @click="showChangePwd = true">
                  <t-icon name="user-password"></t-icon>{{ $t('修改密码') }}
                </t-dropdown-item>
                <t-dropdown-item class="operations-dropdown-container-item" @click="handleLogout">
                  <t-icon name="poweroff"></t-icon>{{ $t('退出登录') }}
                </t-dropdown-item>
              </t-dropdown-menu>
            </template>
            <t-button class="header-user-btn" theme="default" variant="text">
              <template #icon>
                <t-icon class="header-user-avatar" name="user-circle" />
              </template>
              <div class="header-user-account">{{ userStore.name }}</div>
              <template #suffix><t-icon name="chevron-down" /></template>
            </t-button>
          </t-dropdown>
        </div>
      </template>
    </t-head-menu>
    <t-dialog v-model:visible="visible" @close="handleCancel" :confirmLoading="loading" @confirm="handleConfirm">
      <template #header>{{ $t('意见反馈') }}</template>
      <template #body>
        <t-form label-width="auto" ref="form" :data="formData" :rules="FORM_RULES">
          <t-form-item :label="$t('发送给')" name="toUserName">
            <t-select v-model="formData.toUserName" clearable :placeholder="$t('请选择发送人')">
              <template #panelTopContent>
                <div style="padding: 6px 6px 0 6px">
                  <t-input v-model="search" placeholder="请输入关键词搜索" @change="onSearch" />
                </div>
              </template>
              <t-option v-for="item in linkmanList" :key="item.userId" :value="item.userId" :label="item.name">
                <div class="user-option">
                  <img class="avatar" :src="item.avatar" />
                  <div>{{ item.name }}</div>
                </div>
              </t-option>
            </t-select>
          </t-form-item>
          <t-form-item :label="$t('消息标题')" name="messageTitle">
            <t-input v-model="formData.messageTitle" :placeholder="$t('请输入标题')" />
          </t-form-item>
          <t-form-item :label="$t('消息内容')" name="messageContent">
            <t-textarea v-model="formData.messageContent" :placeholder="$t('请输入内容')" name="description" />
          </t-form-item>
          <t-form-item :label="$t('图片上传')" name="attachFiles">
            <t-upload
              v-model="files"
              :abridge-name="[6, 6]"
              theme="image"
              accept="image/*"
              multiple
              :max="3"
              :request-method="customUpload"
              @remove="handleRemove"
            />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
    <!-- 修改密码 -->
    <t-dialog v-model:visible="showChangePwd" :header="$t('修改密码')" :footer="false" @close="resetForm" width="360px">
      <t-form
        label-width="auto"
        ref="chang-pwd-form"
        :data="changePwdForm"
        :rules="CHANGE_PWD_FORM"
        @submit="onChangePwdSubmit"
        @reset="resetForm"
      >
        <t-space direction="vertical">
          <t-form-item :label="$t('用户名')" name="userName">
            <t-input v-model="changePwdForm.userName" type="text" clearable :placeholder="$t('请输入用户名')">
            </t-input>
          </t-form-item>
          <t-form-item :label="$t('旧密码')" name="oldPassword">
            <t-input v-model="changePwdForm.oldPassword" type="text" clearable :placeholder="$t('请输入旧密码')">
            </t-input>
          </t-form-item>
          <t-form-item :label="$t('新密码')" name="newPassword">
            <t-input v-model="changePwdForm.newPassword" type="text" clearable :placeholder="$t('请输入新密码')">
            </t-input>
          </t-form-item>
          <t-form-item :label="$t('确认密码')" name="confirmPassword">
            <t-input
              v-model="changePwdForm.confirmPassword"
              type="text"
              clearable
              :placeholder="$t('再输入一次新密码')"
            >
            </t-input>
          </t-form-item>
          <t-form-item>
            <t-button size="small" theme="default" type="reset">{{ $t('取消') }}</t-button>
            <t-button size="small" theme="primary" type="submit" :loading="loading">{{ $t('提交') }}</t-button>
          </t-form-item>
        </t-space>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
  import { feedback, linkMan, upload } from '@/api/feedback'
  import { updateUserPwd } from '@/api/login'
  import { userFeedback } from '@/api/mailbox'
  import LogoFull from '@/assets/assets-logo-full.svg?component'
  import Empty from '@/components/Empty/index.vue'
  import { prefix } from '@/config/global'
  import { getActive } from '@/router'
  import { useSettingStore, useTabsRouterStore, useUserStore } from '@/store'
  import type { MenuRoute } from '@/types/interface'
  import type { FormInstanceFunctions, FormRule, RequestMethodResponse } from 'tdesign-vue-next'
  import type { PropType } from 'vue'
  import { computed } from 'vue'
  import { useRouter } from 'vue-router'

  import { useLocale } from '@/i18n/useLocale'
  import { useProjectStore } from '@/use/useProject'
  import { removeToken } from '@/utils/auth'
  import MenuContent from './MenuContent.vue'

  const { t } = useI18n()
  const $t = t

  const userStore = useUserStore()
  const { activeProjectId, menuOptions } = storeToRefs(useProjectStore())

  const visible = ref<boolean>(false)
  const files = ref([])
  // const disabled = ref(false)
  const formRef = useTemplateRef<FormInstanceFunctions>('form')

  const formData = ref<any>({
    userId: userStore.unionId,
    toUserId: 0,
    messageTitle: '',
    messageContent: '',
    attachFiles: [],
  })
  const FORM_RULES = computed(() => ({
    toUserName: [{ required: true, message: $t('请选择发送人') }],
    messageTitle: [{ required: true, message: $t('请输入标题') }],
    messageContent: [{ required: true, message: $t('请输入内容') }],
    attachFiles: [{ required: true, message: $t('请上传图片') }],
  }))
  const loading = ref<boolean>(false)
  /* 收件箱列表 */
  const feedbackList = ref<any[]>([])
  /* 联系人列表 */
  const DEFAULT_LINKMAN = ref<any[]>()

  const linkmanList = ref<any[]>([])
  const search = ref('')
  const langList = [
    { content: '简体中文', value: 'zh' },
    { content: 'English', value: 'en' },
    { content: 'Русский Язык', value: 'ru' },
    { content: 'Tiếng Việt', value: 'vi' },
  ]
  const { changeLocale } = useLocale()
  const changeLang = (lang: string) => {
    changeLocale(lang)
  }
  /* 上传图片 */
  const customUpload = async (fileData: any): Promise<RequestMethodResponse> => {
    return new Promise(async (resolve, reject) => {
      try {
        const file = new FormData()
        if (Array.isArray(fileData)) {
          fileData.forEach((f) => {
            file.append('file', f.raw)
          })
        } else file.append('file', fileData.raw)
        const result = await upload(file)
        formData.value.attachFiles.push(result.data)
        const fileUrl = import.meta.env.VITE_BASE_URL + `/scm/oss/v2/download/userFeedback?filename=${result.data}`
        MessagePlugin.success($t('上传成功'))
        resolve({
          status: 'success',
          response: { url: fileUrl },
        })
      } catch (error) {
        MessagePlugin.error($t('上传失败'))
        reject({
          status: 'fail',
          error: error.msg,
          response: {},
        })
      }
    })
  }
  /* 删除图片 */
  const handleRemove = (contex: any) => {
    files.value.splice(contex.index, 1)
    formData.value.attachFiles.splice(contex.index, 1)
  }

  const handleConfirm = async () => {
    const validate = await formRef.value.validate()
    if (validate == true) {
      loading.value = true
      try {
        const res = await feedback(formData.value)
        MessagePlugin.success(res.msg)
        visible.value = false
        handleCancel()
      } catch (error) {
        MessagePlugin.error(error.msg)
      } finally {
        loading.value = false
      }
    }
  }
  const changeFormRef = useTemplateRef<FormInstanceFunctions>('chang-pwd-form')
  const showChangePwd = ref<boolean>(false)
  const changePwdForm = reactive<any>({
    userName: '',
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  })
  const CHANGE_PWD_FORM: Record<string, FormRule[]> = {
    userName: [{ required: true, message: $t('请输入用户名') }],
    oldPassword: [{ required: true, message: $t('请输入旧密码') }],
    newPassword: [{ required: true, message: $t('请输入新密码') }],
    confirmPassword: [
      { required: true, message: $t('再输入一次新密码') },
      { validator: (val) => val === changePwdForm.newPassword, message: $t('两次密码不一致'), trigger: 'blur' },
    ],
  }
  /* 修改密码 */
  const onChangePwdSubmit = async ({ validateResult }) => {
    if (validateResult === true) {
      console.log(changePwdForm)
      loading.value = true
      try {
        await updateUserPwd(changePwdForm.oldPassword, changePwdForm.newPassword)
        loading.value = false
        MessagePlugin.success($t('提交成功'))
        resetForm()
        router.replace('/login')
      } catch {
        loading.value = false
        resetForm()
        MessagePlugin.error($t('提交失败'))
      }
    }
  }

  /* 重置form */
  const resetForm = () => {
    showChangePwd.value = false
    changeFormRef.value!.reset()
  }

  const handleCancel = () => {
    formData.value.attachFiles = []
    files.value = []
    formRef.value.reset()
  }
  const onSearch = () =>
    (linkmanList.value = DEFAULT_LINKMAN.value.filter((item) => item.name.indexOf(search.value) !== -1))
  /* 获取联系人列表 */
  const getLinkMan = async () => {
    const res = await linkMan()
    if (res.code !== 200) return MessagePlugin.error(res.msg)
    DEFAULT_LINKMAN.value = res.data
    linkmanList.value = res.data
  }

  const props = defineProps({
    theme: {
      type: String as PropType<'light' | 'dark'>,
      default: 'light',
    },
    layout: {
      type: String,
      default: 'top',
    },
    showLogo: {
      type: Boolean,
      default: true,
    },
    menu: {
      type: Array as PropType<MenuRoute[]>,
      default: () => [],
    },
    isFixed: {
      type: Boolean,
      default: false,
    },
    isCompact: {
      type: Boolean,
      default: false,
    },
    maxLevel: {
      type: Number,
      default: 3,
    },
  })

  const router = useRouter()
  const settingStore = useSettingStore()

  const active = computed(() => getActive())

  const layoutCls = computed(() => [`${prefix}-header-layout`])

  const menuCls = computed(() => {
    const { isFixed, layout, isCompact } = props
    return [
      {
        [`${prefix}-header-menu`]: !isFixed,
        [`${prefix}-header-menu-fixed`]: isFixed,
        [`${prefix}-header-menu-fixed-side`]: layout === 'side' && isFixed,
        [`${prefix}-header-menu-fixed-side-compact`]: layout === 'side' && isFixed && isCompact,
      },
    ]
  })

  const changeCollapsed = () => {
    settingStore.updateConfig({
      isSidebarCompact: !settingStore.isSidebarCompact,
    })
  }

  const handleNav = (url) => {
    router.push(url)
  }
  const useTabStore = useTabsRouterStore()

  const handleLogout = () => {
    removeToken()
    useTabStore.removeTabRouterList()
    router.push({
      path: '/login',
      query: { redirect: encodeURIComponent(router.currentRoute.value.fullPath) },
    })
  }

  /* 获取用户收件箱 */
  const getUserFeedback = async () => {
    const res = await userFeedback({ toUserId: userStore.unionId })
    feedbackList.value = res.data?.rows
  }

  onMounted(() => {
    getUserFeedback()
    getLinkMan()
  })
</script>
<style lang="less" scoped>
  .@{starter-prefix}-header {
    &-menu-fixed {
      position: fixed;
      top: 0;
      z-index: 1001;

      &-side {
        left: 200px;
        right: 0;
        z-index: 10;
        width: auto;
        transition: all 0.3s;
        &-compact {
          left: 64px;
        }
      }
    }

    &-logo-container {
      cursor: pointer;
      display: inline-flex;
    }
  }
  .header-menu {
    flex: 1 1 1;
    display: inline-flex;

    :deep(.t-menu__item) {
      min-width: unset;
      padding: 0px 16px;
    }
  }

  .operations-container {
    display: flex;
    align-items: center;
    margin-right: 12px;

    .t-popup__reference {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .t-button {
      margin: 0 8px;
      &.header-user-btn {
        margin: 0;
      }
    }

    .t-icon {
      font-size: 20px;
      &.general {
        margin-right: 16px;
      }
    }
  }

  .header-operate-left {
    display: flex;
    margin-left: 20px;
    align-items: normal;
    line-height: 0;

    .collapsed-icon {
      font-size: 20px;
    }
  }

  .header-logo-container {
    width: 184px;
    height: 26px;
    display: flex;
    margin-left: 24px;
    color: var(--td-text-color-primary);

    .t-logo {
      width: 100%;
      height: 100%;
      &:hover {
        cursor: pointer;
      }
    }

    &:hover {
      cursor: pointer;
    }
  }

  .header-user-account {
    display: inline-flex;
    align-items: center;
    color: var(--td-text-color-primary);
    .t-icon {
      margin-left: 4px;
      font-size: 16px;
    }
  }

  :deep(.t-head-menu__inner) {
    border-bottom: 1px solid var(--td-border-level-1-color);
  }

  .t-menu--light {
    .header-user-account {
      color: var(--td-text-color-primary);
    }
  }
  .t-menu--dark {
    .t-head-menu__inner {
      border-bottom: 1px solid var(--td-gray-color-10);
    }
    .header-user-account {
      color: rgba(255, 255, 255, 0.55);
    }
    .t-button {
      --ripple-color: var(--td-gray-color-10) !important;
      &:hover {
        background: var(--td-gray-color-12) !important;
      }
    }
  }

  .operations-dropdown-container-item {
    width: 100%;
    display: flex;
    align-items: center;

    .t-icon {
      margin-right: 8px;
    }

    :deep(.t-dropdown__item) {
      .t-dropdown__item__content {
        display: flex;
        justify-content: center;
      }
      .t-dropdown__item__content__text {
        display: flex;
        align-items: center;
        font-size: 14px;
      }
    }

    :deep(.t-dropdown__item) {
      width: 100%;
      margin-bottom: 0px;
    }
    &:last-child {
      :deep(.t-dropdown__item) {
        margin-bottom: 8px;
      }
    }
  }
  .user-option {
    display: flex;
    align-items: center;
    column-gap: 16px;
    .avatar {
      width: 18px;
      height: 18px;
      border-radius: 50%;
    }
  }

  .message {
    width: 320px;

    .mess-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      box-sizing: border-box;
      h4 {
        font-size: 14px !important;
        font-weight: bold;
      }
    }
    .mess-center {
      overflow: hidden;
      :deep(.t-list-item__meta-description) {
        margin-right: 0;
      }
      .info {
        margin-top: 8px;
      }
    }
  }
  .t-space {
    width: 100%;
    :deep(:nth-last-child(1).t-space-item) {
      .t-form__controls {
        display: flex;
        justify-content: center;
      }
    }
  }

  :deep(.t-dialog__footer) {
    text-align: center;
  }
</style>

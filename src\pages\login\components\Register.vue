<template>
  <t-form
    ref="form"
    :class="['item-container']"
    :data="registerForm"
    :rules="FORM_RULES"
    label-width="0"
    @submit="onSubmit"
  >
    <t-form-item name="countryArea">
      <t-select
        v-model="registerForm.countryArea"
        :options="districtList"
        :placeholder="$t('请选择地区')"
        :keys="{ label: 'listLabel', value: 'listLabel' }"
      />
    </t-form-item>
    <t-form-item name="entityName">
      <t-input v-model="registerForm.entityName" type="text" clearable :placeholder="$t('请填写公司名称')"> </t-input>
    </t-form-item>
    <t-form-item name="serialNo">
      <t-input v-model="registerForm.serialNo" type="text" clearable :placeholder="$t('请填写税号或身份证号')">
      </t-input>
    </t-form-item>
    <t-form-item name="entityTel">
      <t-input v-model="registerForm.entityTel" type="text" clearable :placeholder="$t('请填写联系方式')"> </t-input>
    </t-form-item>
    <t-form-item>
      <t-input v-model="registerForm.entityAddress" type="text" clearable :placeholder="$t('联系地址')"> </t-input>
    </t-form-item>
    <t-form-item>
      <t-input v-model="registerForm.bankAccount" type="text" clearable :placeholder="$t('银行账号')"> </t-input>
    </t-form-item>
    <t-form-item> </t-form-item>
    <t-form-item>
      <t-button block type="submit"> 注册 </t-button>
    </t-form-item>
  </t-form>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { getNsList } from '@/api/dict'
  import { MessagePlugin, FormRule } from 'tdesign-vue-next'
  const $t = useI18n().t

  const registerForm = reactive({
    countryArea: '',
    entityName: '',
    serialNo: '',
    entityTel: '',
    entityAddress: '',
    bankAccount: '',
    attachmentImages: [],
  })

  const FORM_RULES: Record<string, FormRule[]> = {
    countryArea: [{ required: true, message: $t('请选择地区') }],
    entityName: [{ required: true, message: $t('请填写公司名称') }],
    serialNo: [{ required: true, message: $t('请填写税号或身份证号') }],
    entityTel: [{ required: true, message: $t('请填写联系方式') }],
  }

  const form = ref()

  const emit = defineEmits(['registerSuccess'])
  /* 地区列表 */
  const districtList = ref<any[]>([])
  /* 获取地区 */
  const nsList = async () => {
    const { data } = await getNsList('CUSTOMLIST456')
    districtList.value = data
  }

  const onSubmit = ({ validateResult }) => {
    if (validateResult === true) {
      console.log(registerForm)
      emit('registerSuccess')
    }
  }

  onMounted(() => {
    nsList()
  })
</script>

<style lang="less" scoped>
  @import url('../index.less');
</style>

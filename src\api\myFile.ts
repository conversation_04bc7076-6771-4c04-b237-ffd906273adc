import { get, post, remove, put } from '@/utils/request'

/* 获取文件列表 */
export const getFileListApi = (params: object) => get('/admin/wx/cp/docs', params)

/* 重命名文档 */
export const renameFileApi = (data: any) => put(`/admin/wx/cp/docs/rename`, data)

/* 删除表格、文档 */
export const deleteFileApi = (docId: number) => remove(`/admin/wx/cp/docs?docId=${docId}`)

/* 新建表格、文档 */
export const createWeComDoc = (data: any) => post(`/admin/wx/cp/docs`, data)

/* 获取表格行、列信息 */
export const getSheetProperties = (docId: string) => get(`/admin/wx/cp/docs/sheet?docId=${docId}`)

/* 获取表格范围数据 */
export const getSheetRangeData = (docId: string, sheetId: string, range: string) =>
  get(`/admin/wx/cp/docs/sheet-range`, { docId, sheetId, range })

/* 批量更新表格 */
export const batchUpdateSheet = (data: any) => put(`/admin/wx/cp/docs/sheet`, data)

/* 获取文档内容 */
export const getDocContent = (docId: string) => get(`/admin/wx/cp/docs/document?docId=${docId}`)

/* 保存文档内容 */
export const saveDocContent = (data: any) => put(`/admin/wx/cp/docs/document`, data)

/* 企业微信人列表 */
export const wxCpUserList = () => get(`/admin/system/wxcp/user/page`)

/* 保存文档配置信息 */
export const saveDocConfig = (docId: string, data: any) => put(`/admin/wx/cp/docs/config/${docId}`, data)

/* 获取文档配置信息 */
export const getDocConfig = (docId: string) => get(`/admin/wx/cp/docs/config/${docId}`)

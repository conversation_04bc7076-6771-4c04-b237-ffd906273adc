<script lang="ts" setup>
  import { init } from 'echarts'
  import { ref, watchEffect } from 'vue'
  import Empty from '@/components/Empty/index.vue'
  import { DefaultUnExchangeResult, OrderState, unExchangeEchartConfig, UnExchangeType } from '../utils/orderConfig'
  import startEffect from '../effect/startEffect'
  import endEffect from '../effect/endEffect'
  import { formatProduct } from '../utils/fomartProduct'
  import { useI18n } from 'vue-i18n'
  import { purchExchangedChart } from '@/api/data/vendor'
  import { useUserStore } from '@/store'
  import { useYearDataStore } from '../hooks/dataYearStore'

  const { t } = useI18n()
  const $t = t

  const unexchangedElement = useTemplateRef<HTMLDivElement>('unexchangedElement')

  const { slectVal } = storeToRefs(useYearDataStore())
  const { startTime, startTimeText, disableDate } = startEffect()
  const { endTime, endTimeText } = endEffect()

  const globalStore = useUserStore()

  const totalUnexchangedMoney = ref<string>('0')
  const lineChartLoading = ref<boolean>(false)
  const charState: OrderState = {}
  const resultEmpty = ref(false)
  const fetchUnexchangedData = async () => {
    if (!globalStore.vendorIds?.length) return
    lineChartLoading.value = true
    resultEmpty.value = false
    totalUnexchangedMoney.value = '0'
    await nextTick()
    const { data: result } = await purchExchangedChart<UnExchangeType[]>('unexchangedquantitylinechart', {
      vendorIdList: globalStore.vendorIds,
      year: slectVal.value,
      startTime: startTimeText.value,
      endTime: endTimeText.value,
    })
    if (!result?.length) {
      resultEmpty.value = true
      lineChartLoading.value = false
      charState.unexchangedEchart?.dispose()
      charState.unexchangedEchart = undefined
      return
    }
    const unexchangedRes = result.length > 0 ? result.map(formatProduct) : DefaultUnExchangeResult
    totalUnexchangedMoney.value = unexchangedRes
      .map((i) => parseFloat(i.unExchange))
      .reduce((p, c) => p + c)
      .toFixed(2)

    if (unexchangedElement.value) {
      const unexchangedChart = charState.unexchangedEchart
        ? charState.unexchangedEchart
        : init(unexchangedElement.value)
      charState.unexchangedEchart = unexchangedChart
      unexchangedChart.setOption(unExchangeEchartConfig(unexchangedRes))
    }
    lineChartLoading.value = false
  }

  watchEffect(() => {
    if (startTime.value && endTime.value) {
      fetchUnexchangedData()
    }
  })
</script>
<template>
  <!-- <div class="echart-title">{{ $t('订单查询') }}</div> -->
  <div style="margin-bottom: 0" class="select-list">
    <div class="select-container">
      <div class="label">{{ $t('开始') }}:</div>
      <t-date-picker v-model="startTime" :disable-date="disableDate" />
    </div>
    <div class="select-container">
      <div class="label">{{ $t('结束') }}:</div>
      <t-date-picker v-model="endTime" :disable-date="disableDate" />
    </div>
  </div>
  <t-loading size="small" :loading="lineChartLoading">
    <div v-if="!resultEmpty" class="echart-line-container" ref="unexchangedElement"></div>
    <Empty v-else :description="$t('暂无数据')" />
  </t-loading>

  <div style="margin-top: 0; padding-top: 0" class="total-container">
    {{ $t('待交货量总计') }}:<span>{{ totalUnexchangedMoney }}{{ $t('吨') }}</span>
  </div>
</template>
<style lang="less" scoped>
  @import '../style/common.less';
</style>

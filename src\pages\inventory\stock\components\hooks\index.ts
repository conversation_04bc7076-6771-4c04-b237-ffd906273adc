import { useRoute } from 'vue-router'
import { getNsList } from '@/api/dict'
import { keyBy, mapValues } from 'lodash-es'
import { agingList } from '@/api/inventory'
import { PrimaryTableCol, TableRowData, TableProps } from 'tdesign-vue-next'

export default () => {
  const { t } = useI18n()
  const $t = t
  const route = useRoute()

  /* tab列表 */
  const tabList = computed(() => [
    { label: $t('自存库账龄'), value: 403 },
    { label: $t('外租库账龄'), value: 404 },
  ])
  /* tab Active */
  const value = ref<number>(null)
  /* 项目列表 */
  const projectList = ref<any>()
  const projectObj = ref<any>({})
  /* loading */
  const Loading = ref(false)
  /* 请求参数 */
  const params = ref({
    riskCategory: 4,
    controlPoint: 40006,
    manageElement: Number(route.query?.active),
    normal: true,
    companyProject: route.query?.projectId,
    itemCode: route.query?.itemCode,
  })
  /* 表格配置 */
  const columns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'mainDataTitle', title: $t('入库单据'), width: 100, align: 'center' },
    { colKey: 'company', title: $t('项目'), width: 100, align: 'center' },
    { colKey: 'itemName', title: $t('货品'), width: 100, align: 'center' },
    {
      colKey: 'occupyQuantity',
      title: $t('数量'),
      width: 100,
      align: 'center',
      cell: (h, { col, row }) => `${(Number(row.occupyQuantity) / 1000).toFixed(3)} 吨`,
    },
    { colKey: 'dataDate', title: $t('入库时间'), width: 100, align: 'center' },
    {
      colKey: 'occupyDays',
      title: $t('库存账龄'),
      width: 100,
      align: 'center',
    },
  ])
  /* 表格分页 */
  const pagination = ref<TableProps['pagination']>({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  /* 获取帐龄列表 */
  const getAgingList = async () => {
    const { data } = await agingList(params.value)
    projectList.value = data.map((item: any) => ({
      ...item,
      company: projectObj.value[item.companyProject],
    }))
    pagination.value.total = projectList.value.length
  }
  /* 获取项目列表 */
  const getProjectList = async () => {
    Loading.value = true
    try {
      const { data } = await getNsList('CUSTOMLIST_PROJECT_LIST')
      projectObj.value = mapValues(keyBy(data, 'listValue'), (obj) => obj.listLabel)
      await getAgingList()
    } finally {
      Loading.value = false
    }
  }
  /* tab切换 */
  const handleChange = (value: number) => (params.value.manageElement = value)

  watch(
    () => params.value.manageElement,
    async () => {
      await getProjectList()
    },
  )

  onMounted(async () => {
    const query = route.query
    if (query) query.active && (value.value = Number(query.active))
    await getProjectList()
  })
  return {
    value,
    tabList,
    projectList,
    columns,
    Loading,
    pagination,
    handleChange,
  }
}

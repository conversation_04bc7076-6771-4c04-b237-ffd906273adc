<script lang="ts" setup>
  import { type EChartsType, init } from 'echarts'
  import { ref } from 'vue'
  import Empty from '@/components/Empty/index.vue'
  import { moneyEchartConfig } from '../utils/moneyConfig'
  import startEffect from '../effect/startEffect'
  import endEffect from '../effect/endEffect'
  import { useI18n } from 'vue-i18n'
  import OrderList from './orderList.vue'
  import { useUserStore } from '@/store'
  import { useYearDataStore } from '../hooks/dataYearStore'
  import { getPrepayTotal, getReport } from '@/api/capital'
  import { groupBy, uniqBy } from 'lodash-es'
  import { useNsListLabelRaw } from '@/use/dict'
  import { useRequest } from 'vue-request'

  const { t, locale } = useI18n()
  const $t = t

  const fundQueryChartRef = useTemplateRef<HTMLDivElement[]>('paymentElement')
  const { slectVal } = storeToRefs(useYearDataStore())

  const { startTime, startTimeText, disableDate } = startEffect()
  const { endTime, endTimeText } = endEffect()

  const globalStore = useUserStore()

  const { loading: prepayDataLoading, data: prepayData } = useRequest(
    async (data: any) => {
      const res = await getReport(data)
      return uniqBy(
        res.data.map((d) => ({
          orderid: d.extraInfo.orderId,
          orderno: d.extraInfo.orderNo,
        })),
        'orderid',
      )
    },
    {
      manual: false,
      defaultParams: [
        {
          riskCategory: '3',
          controlPoint: '30001',
          normal: true,
          dataDateStart: startTimeText.value,
          dataDateEnd: endTimeText.value,
          entityIdList: globalStore.vendorIds,
        },
      ],
    },
  )

  const { loading: unpaidDataLoading, data: unpaidData } = useRequest(
    async (data: any) => {
      const res = await getReport(data)
      return uniqBy(
        res.data.map((d) => ({
          orderid: d.extraInfo.orderId,
          orderno: d.extraInfo.orderNo,
        })),
        'orderid',
      )
    },
    {
      manual: false,
      defaultParams: [
        {
          riskCategory: '3',
          controlPoint: '30002',
          normal: true,
          dataDateStart: startTimeText.value,
          dataDateEnd: endTimeText.value,
          entityIdList: globalStore.vendorIds,
        },
      ],
    },
  )

  const chartState: Record<string, Nullable<EChartsType[]>> = {
    paymentEchart: null,
  }

  const loading = ref(false)
  const pageData = ref<any>()
  const dataEmpty = ref(false)

  /* 货币类型 */
  const currencyType = ref<any>({})
  /* 获取字典label */
  const nsListLabelRaw = async () => {
    const { currency } = await useNsListLabelRaw('currency')
    currencyType.value = currency
  }

  const queryParams = computed(() => ({
    sumCols: ['occupy_amount'],
    groupCols: ['risk_category', 'control_point', 'currency_id'],
    riskCategory: '3',
    normal: true,
    dataDateStart: startTimeText.value,
    dataDateEnd: endTimeText.value,
    entityIdList: globalStore.vendorIds,
  }))

  const fetchPaymentData = async () => {
    if (!globalStore.vendorIds?.length) return
    loading.value = true
    dataEmpty.value = false
    await nextTick()
    const res = await getPrepayTotal(queryParams.value)

    const currencyGroup = groupBy(res.data, 'currencyId')

    if (!res.data?.length) {
      dataEmpty.value = true
      loading.value = false
      chartState.paymentEchart?.forEach((chart) => chart.dispose())
      chartState.paymentEchart = null
      return
    }
    return Object.entries(currencyGroup).map((d) => {
      const currencyData = d[1]
      const controlPointGroup = groupBy(currencyData, 'controlPoint')
      const prepayDataList = controlPointGroup['30001'] || []
      const needPayDataList = controlPointGroup['30002'] || []
      return {
        currency: currencyType.value[d[0]],
        data: {
          unpaidAmount: needPayDataList.reduce((a, b) => a + Number(b.occupyAmount), 0),
          prepayAmount: prepayDataList.reduce((a, b) => a + Number(b.occupyAmount), 0),
        },
      }
    })
  }

  const loadData = async () => {
    await nextTick()
    if (!globalStore.vendorIds?.length) return
    loading.value = true
    const groupResult = await fetchPaymentData()

    if (!groupResult) {
      return
    }

    pageData.value = groupResult || []
    loading.value = false
  }

  const createFundBarChart = () => {
    let chart = chartState.paymentEchart
    if (!chart?.length) {
      chart = fundQueryChartRef.value?.map((c) => init(c)) || []
      chartState.fundChart = chart
    }
    chart.forEach((c, i) => {
      c.setOption(
        moneyEchartConfig(
          pageData.value[i].currency,
          {
            unpaidAmount: pageData.value[i].data.unpaidAmount,
            prepayAmount: pageData.value[i].data.prepayAmount,
          },
          $t,
          locale,
        ),
      )
    })
  }

  watchEffect(() => {
    if (pageData.value) createFundBarChart()
    else if (chartState.paymentEchart) {
      chartState.paymentEchart.forEach((c) => c.dispose())
      chartState.fundChart = null
    }
  })

  onMounted(async () => {
    await nsListLabelRaw()
    loadData()
  })
  watch(
    () => slectVal.value,
    () => loadData(),
  )

  watchEffect(() => {
    if (startTime.value && endTime.value) {
      loadData()
    }
  })
</script>
<template>
  <!-- <div class="echart-title">{{ $t('资金查询') }}</div> -->
  <div class="select-list">
    <div class="select-container">
      <div class="label">{{ $t('开始') }}:</div>
      <t-date-picker v-model="startTime" :disable-date="disableDate" />
    </div>
    <div class="select-container">
      <div class="label">{{ $t('结束') }}:</div>
      <t-date-picker v-model="endTime" :disable-date="disableDate" />
    </div>
  </div>
  <t-loading size="small" :loading="loading">
    <div class="chart-content">
      <template v-if="!dataEmpty">
        <div v-for="c in pageData" :key="c.currency" ref="paymentElement" class="echart-line-container">
          {{ c.currency }}
        </div>
      </template>
      <Empty v-else :description="$t('暂无数据')" />
    </div>
  </t-loading>
  <order-list v-if="unpaidData?.length" :title="$t('应收订单')" color="#8BC76E" :orders="unpaidData" />
  <order-list v-if="prepayData?.length" :title="$t('预收订单')" color="#EDC758" :orders="prepayData" />
</template>
<style lang="less" scoped>
  @import '../style/common.less';
</style>

import { groupBy, max, sum } from 'lodash-es'
import { getReport } from '@/api/capital'
import { useNsListLabelRaw } from '@/use/dict'
import { useRouter } from 'vue-router'
import { saleContractOrderApi, purchContractOrderApi } from '@/api/contract'
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'

export default () => {
  const { t, locale } = useI18n()
  const $t = t
  const router = useRouter()

  /* 路由传递过来的参数 */
  const routeParams = JSON.parse(history.state.info)

  /* 查询类型 */
  const type = routeParams.type

  /* 账期分类 */
  const paymentType = computed(() =>
    type == 'prepay'
      ? $t('预付')
      : type == 'payable'
      ? $t('入库')
      : type == 'receivables'
      ? $t('发票未收')
      : $t('存款'),
  )
  const overdueType = computed(() =>
    type == 'prepay' ? $t('未收货超期') : type == 'payable' ? $t('未付超期') : $t('未结超期'),
  )

  /* 请求参数 */
  const reqParams = {
    riskCategory: routeParams.riskCategory,
    controlPoint: routeParams.controlPoint,
    normal: routeParams.normal,
    companyProject: routeParams.companyProject,
    dataLevel: routeParams.dataLevel,
    entityId: routeParams.entityId,
  }
  /* loading */
  const showLoading = ref<boolean>(false)
  /* 账期分类列表 */
  const paymentList = ref<any>([])
  /* 货币类型 */

  const currencyType = ref<{ [key: string]: any }>({})
  const tabColumns = computed(() => {
    if (type == 'prepay' || type == 'payable') {
      return prepayColumns.value
    } else if (type == 'receivables') {
      return columns.value
    } else if (type == 'save') {
      return saveColumns.value
    }
  })

  /* 表格配置 */
  // prepay、payable
  const prepayColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'contractName', title: `${paymentType.value}${$t('合同')}`, ellipsis: true, align: 'center' },
    { colKey: 'itemName', title: $t('货品'), ellipsis: true, align: 'center' },
    { colKey: 'orderNo', title: $t('订单'), align: 'center' },
    { colKey: 'subsidiaryName', title: $t('公司'), ellipsis: true, align: 'center' },
    { colKey: 'entityName', title: $t('客商'), ellipsis: true, align: 'center' },
    { colKey: 'occupyDays', title: `${$t('最大')}${overdueType.value}`, ellipsis: true, align: 'center' },
    { colKey: 'currencyId', title: $t('金额'), align: 'center' },
    { colKey: 'link', title: $t('操作'), width: 80, align: 'center' },
  ])
  // receivables
  const columns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'orderNo', title: `${paymentType.value}${$t('订单')}`, ellipsis: true, align: 'center' },
    { colKey: 'itemName', title: $t('货品'), ellipsis: true, align: 'center' },
    { colKey: 'subsidiaryName', title: $t('公司'), ellipsis: true, align: 'center' },
    { colKey: 'entityName', title: $t('客商'), ellipsis: true, align: 'center' },
    { colKey: 'occupyDays', title: `${$t('最大')}${overdueType.value}`, ellipsis: true, align: 'center' },
    { colKey: 'currencyId', title: $t('金额'), align: 'center' },
    { colKey: 'link', title: $t('操作'), width: 80, align: 'center' },
  ])
  //save
  const saveColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'contractName', title: `${paymentType.value}${$t('合同')}`, ellipsis: true, align: 'center' },
    { colKey: 'itemName', title: $t('货品'), ellipsis: true, align: 'center' },
    { colKey: 'orderNo', title: $t('订单'), align: 'center' },
    { colKey: 'subsidiaryName', title: $t('公司'), ellipsis: true, align: 'center' },
    { colKey: 'entityName', title: $t('客商'), ellipsis: true, align: 'center' },
    { colKey: 'currencyId', title: $t('金额'), align: 'center' },
    { colKey: 'link', title: $t('操作'), width: 80, align: 'center' },
  ])

  /* 分页配置 */
  const pagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  /* 获取金额字典 */
  const getMonry = async () => {
    const res = await useNsListLabelRaw('currency')
    currencyType.value = res.currency
  }
  /* 获取账期数据 */
  const getPaymentList = async () => {
    showLoading.value = true
    try {
      const { data } = await getReport(reqParams)
      const orderGroupMap = groupBy(data, 'extraInfo.orderId')
      paymentList.value = Object.entries(orderGroupMap).map((e) => {
        return {
          entityId: e[1][0].entityId,
          entityName: e[1][0].entityName,
          companyProject: e[1][0].companyProject,
          subsidiaryName: e[1][0].subsidiaryName,
          itemName: e[1][0].itemName,
          orderId: e[0],
          orderNo: e[1][0].extraInfo?.orderNo,
          contractName: e[1][0].extraInfo?.contractName,
          currencyId: e[1][0].currencyId,
          occupyDays: max(e[1].map((item) => Math.abs(Number(item.occupyDays)))),
          occupyAmount: sum(e[1].map((item) => Number(item.occupyAmount))).toFixed(2),
        }
      })
      pagination.value.total = paymentList.value.length
    } finally {
      showLoading.value = false
    }
  }

  /* 金额转换 */
  const formatCurrency = (value: any, currencyId: number) => {
    if (value === null || value === undefined) return ''
    const number = parseFloat(value)
    if (isNaN(number)) return ''
    return new Intl.NumberFormat(locale.value, {
      style: 'currency',
      currencyDisplay: 'code',
      currency: currencyType.value[currencyId],
      notation: 'compact',
      compactDisplay: 'long',
      minimumFractionDigits: 3,
      roundingMode: 'halfFloor',
    }).format(value)
  }

  /* 跳转合同详情 */
  const toContractDetail = async (row: any) => {
    if (!row?.orderNo) return
    const contractType = row.orderNo.split('-')[0]
    showLoading.value = true
    if (contractType == 'SO') {
      const res = await saleContractOrderApi(row.orderId)
      showLoading.value = false
      router.push({ path: '/contract/sales-details', state: { info: JSON.stringify(res.data[0]) } })
    } else {
      const res = await purchContractOrderApi(row.orderId)
      showLoading.value = false
      router.push({ path: '/contract/purchase-details', state: { info: JSON.stringify(res.data[0]) } })
    }
  }

  onMounted(async () => {
    await getMonry()
    await getPaymentList()
  })

  return {
    paymentList,
    tabColumns,
    formatCurrency,
    toContractDetail,
    pagination,
    showLoading,
  }
}

/* eslint-disable no-empty */
import { useRouter } from 'vue-router'
import { purchContractGoodsApi, purchContractListApi, vendorListApi } from '@/api/contract'
import { useNsListLabel, useNsListLabelRaw } from '@/use/dict'
import { useUserStore } from '@/store'
import dayjs from 'dayjs'
import { debounce } from 'lodash-es'
import { useI18n } from 'vue-i18n'
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'
import { useProjectStore } from '@/use/useProject'

export default () => {
  const router = useRouter()
  const { t, locale } = useI18n()
  const $t = t
  const globalStore = useUserStore()
  const { activeProjectId } = storeToRefs(useProjectStore())
  /* 采购合同列表 */
  const purchaseInfoLoading = ref<boolean>(true)
  const purchaseList = ref<any>([])
  /* 订单列表 */
  const orderList = ref<any>([])
  /* loading */
  const loading = ref<boolean>(false)
  /* 项目类型 */
  const projectType = ref<any>([])
  /* 金额币种 */
  const currencyType = ref<any>({})

  /* 供应商列表 */
  const supplierList = ref<any[]>([])
  /* 输入框绑定值（显示 label） */
  const inputValue = ref('')
  /* 模糊搜索初始数据 */
  const filteredOptions = ref([])
  /* 交易方式 */
  const tradeType = ref<{ [key: string]: any }>({})

  /* 订单状态列表 */
  const orderStatus = computed(() => [
    { title: $t('待履行'), id: 0, value: 'pending' },
    { title: $t('已完成'), id: 1, value: 'over' },
    { title: $t('已取消'), id: 2, value: 'canceled' },
  ])

  /* 可选时间范围 */
  const disableDate = {
    before: dayjs().subtract(3, 'year').format('YYYY-MM-DD'),
    after: dayjs().format('YYYY-MM-DD'),
  }
  /* 初始时间*/
  const timeRange = ref([])

  const dicts = useNsListLabel('CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI')
  /* 表格配置 */
  const columns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    {
      colKey: 'order_no',
      title: $t('采购单号'),
      ellipsis: true,
      width: 120,
      fixed: 'left',
    },
    {
      colKey: 'delivery_type',
      title: $t('配送方式'),
      width: 80,
      render: (h, args) => {
        return <span>{dicts.CUSTOMLIST_HRKPO_QARESULT_PICKMETHODLI?.[args.row.delivery_type]}</span>
      },
    },
    {
      colKey: 'vendor_name',
      title: $t('供应商'),
      ellipsis: true,
      width: 150,
    },
    {
      colKey: 'end_rest_day',
      title: $t('到期时间'),
      ellipsis: true,
      width: 120,
    },
    { colKey: 'acture_vendor', title: $t('实际供应商'), ellipsis: true, width: 150 },
    { colKey: 'item_price', title: $t('单价'), ellipsis: true, width: 150 },
    { colKey: 'item_quantity', title: $t('数量') + '(t)', ellipsis: true, width: 120 },
    { colKey: 'delivery_date_to', title: $t('最晚交货日期'), width: 120, ellipsis: true },
    { colKey: 'rest_quantity', title: $t('待交货数量') + '(t)', ellipsis: true, width: 110 },
    { colKey: 'signing_date', title: $t('签订日期'), ellipsis: true, width: 150 },
    {
      title: $t('操作'),
      colKey: 'link',
      fixed: 'right',
      ellipsis: true,
      width: 80,
    },
  ])
  const getRowClassName = ({ row }) => {
    console.log(row)
    if (Math.abs(row.end_rest_day)) return 'overdue-class'
  }
  /* 是否展示列配置 */
  const columnControllerVisible = ref<boolean>(false)
  /* 列配置 */
  const DEFAULT_FIELDS = [
    'order_no',
    'delivery_type',
    'vendor_name',
    'end_rest_day',
    'acture_vendor',
    'item_price',
    'item_quantity',
    'delivery_date_to',
    // 'staff_name',
    'rest_quantity',
    'signing_date',
  ]
  const columnControllerConfig: any = ref({
    dialogProps: { preventScrollThrough: true },
    fields: [...DEFAULT_FIELDS],
    hideTriggerButton: true,
  })
  /* 表格配置 */
  const DEFAULT_COLUMNS = [
    'order_no',
    'delivery_type',
    'vendor_name',
    'end_rest_day',
    'acture_vendor',
    'item_price',
    'item_quantity',
    'delivery_date_to',
    // 'staff_name',
    'rest_quantity',
    'signing_date',
    'link',
  ]
  const displayColumns = ref<string[]>([...DEFAULT_COLUMNS])

  /* 分页配置 */
  const pagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  /* 请求参数 */
  const formData = ref({
    status: 'pending',
    itemId: null,
    dataStart: null,
    dataEnd: null,
    customerIdList: globalStore.customerIds,
    vendorId: null,
  })

  /* 获取项目label字典 */
  const getProjectLabel = async () => {
    const { CUSTOMLIST_PROJECT_LIST, currency, CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE } = await useNsListLabelRaw(
      'CUSTOMLIST_PROJECT_LIST',
      'currency',
      'CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE',
    )
    projectType.value = CUSTOMLIST_PROJECT_LIST
    currencyType.value = currency
    tradeType.value = CUSTOMRECORD_HRK_PAYMENTTERMS_BASEDATE
  }

  /* 获取供应商列表 */
  const getVendorList = async () => {
    const res = await vendorListApi(formData.value.itemId)
    supplierList.value = (res.data as any).map((it) => ({
      label: it.vendorName,
      value: it.vendorId,
    }))
  }

  /* 获取采购合同列表 */
  const getPurchContractList = async () => {
    try {
      purchaseInfoLoading.value = true
      const res = await purchContractGoodsApi()
      purchaseList.value = res.data
      activeProjectId.value = purchaseList.value[0]?.projectId
    } finally {
      purchaseInfoLoading.value = false
    }
  }

  /* 获取采购合同订单列表 */
  const purchContractList = async () => {
    loading.value = true
    try {
      const { data } = await purchContractListApi(formData.value)
      orderList.value = data.filter((it: any) => it.project_id === activeProjectId.value)
      pagination.value.total = orderList.value.length
      if (formData.value.status !== 'pending') {
        columnControllerConfig.value.fields = DEFAULT_FIELDS.filter(
          (field) => !['rest_quantity', 'end_rest_day'].includes(field),
        )
        displayColumns.value = DEFAULT_COLUMNS.filter((field) => !['rest_quantity', 'end_rest_day'].includes(field))
      } else {
        columnControllerConfig.value.fields = DEFAULT_FIELDS
        displayColumns.value = [...DEFAULT_COLUMNS]
      }
    } catch (error) {
    } finally {
      loading.value = false
    }
  }

  /* 详情 */
  const toContractDetail = async (row: any) =>
    router.push({ path: '/contract/purchase-details', state: { info: JSON.stringify(row) } })

  /* 选择时间 */
  const timeChange = (value: string[]) => {
    formData.value.dataStart = value[0]
    formData.value.dataEnd = value[1]
  }

  // 模糊搜索
  const filterOptions = (keyword: string) => {
    if (!keyword) return []
    const lowerKeyword = keyword.trim().toLowerCase()
    return supplierList.value.filter((item) => item.label.trim().toLowerCase().includes(lowerKeyword))
  }

  // 添加防抖处理（300ms）
  const debouncedSearch = debounce((value: string) => {
    filteredOptions.value = filterOptions(value)
  }, 300)

  /* 处理搜索输入 */
  const handleSearch = (value: string) => debouncedSearch(value)

  // 处理选项选择
  const handleSelect = (value: any) =>
    (formData.value.vendorId = supplierList.value.find((item) => item.label === value)?.value)

  /* 表单提交 */
  const onSubmit = async () => await purchContractList()

  /* 重置表单 */
  const onReset = async () => {
    timeRange.value = []
    inputValue.value = ''

    formData.value = {
      ...formData.value,
      itemId: null,
      dataStart: null,
      dataEnd: null,
      status: 'pending',
    }
    await purchContractList()
  }

  const tabChange = () => onReset()

  /* 刷新表格 */
  const refresh = async () => await purchContractList()
  /* 格式化货币 */
  const formatCurrency = (value: any, currencyId: number) => {
    if (value === null || value === undefined) return ''
    const number = parseFloat(value)
    if (isNaN(number)) return ''
    return new Intl.NumberFormat(locale.value, {
      style: 'currency',
      currencyDisplay: 'code',
      currency: currencyType.value[currencyId],
      notation: 'compact',
      compactDisplay: 'long',
      minimumFractionDigits: 2,
      roundingMode: 'halfFloor',
    }).format(value)
  }

  onMounted(async () => {
    getProjectLabel()
    getVendorList()
    await getPurchContractList()
    await purchContractList()
  })

  return {
    purchaseInfoLoading,
    purchaseList,
    formData,
    loading,
    activeProjectId,
    projectType,
    onSubmit,
    onReset,
    orderList,
    timeRange,
    disableDate,
    timeChange,
    orderStatus,
    inputValue,
    filteredOptions,
    handleSearch,
    handleSelect,
    columns,
    pagination,
    columnControllerConfig,
    columnControllerVisible,
    displayColumns,
    refresh,
    formatCurrency,
    toContractDetail,
    tabChange,
    tradeType,
    getRowClassName,
  }
}

import { computed, ref } from 'vue'
import dayjs from 'dayjs'
import { useYearDataStore } from '../hooks/dataYearStore'

const startEffect = () => {
  const { slectVal, yearStart } = storeToRefs(useYearDataStore())
  const currentDate = dayjs().format('YYYY-MM-DD')
  const minDate = dayjs().add(-3, 'year').format('YYYY-MM-DD')

  const startTime = ref<string>(yearStart.value)
  const showStartTime = ref<boolean>(false)
  const startTimeText = computed(() => dayjs(startTime.value).format('YYYY-MM-DD'))
  const startTimeValue = computed(() => dayjs(startTime.value).format('DD-MM-YYYY'))
  const disableDate = ref({
    before: dayjs().add(-3, 'year').format('YYYY-MM-DD'),
    after: dayjs().format('YYYY-MM-DD'),
  })

  watch(
    () => slectVal.value,
    () => {
      startTime.value = yearStart.value
    },
  )

  return {
    startTime,
    minDate,
    showStartTime,
    startTimeValue,
    startTimeText,
    currentDate,
    disableDate,
  }
}

export default startEffect

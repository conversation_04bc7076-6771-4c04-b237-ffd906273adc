<script setup lang="ts">
  import usePersonnelChart from '../hooks/usePersonnelChart'
  const props = defineProps<{
    totalMoney: Array<any>
    imminentMoney: Array<any>
    overdueMoney: Array<any>
    title: string
    params: object
    legend: string[]
  }>()
  import { useI18n } from 'vue-i18n'
  const { t } = useI18n()
  const $t = t
  const { setChartRef, currencyTypes, title } = usePersonnelChart(props)
</script>
<template>
  <div ref="personnelChart" class="container">
    <div v-for="currencyType in currencyTypes" :key="currencyType" class="chart-container" :ref="setChartRef"></div>
    <div v-if="currencyTypes?.length" class="tips">
      （{{ $t('点击客商') }}{{ title }}{{ $t('款跳转对应') }}{{ title }}{{ $t('款单据列表') }}）
    </div>
  </div>
</template>
<style scoped lang="less">
  .container {
    height: 100%;
    box-sizing: border-box;
    border-radius: var(--td-radius-medium);
    background-color: var(--td-bg-color-container);
    color: var(--td-text-color-primary);

    .chart-container {
      width: 100%;
      height: 70vh;
      margin-bottom: 20px;
    }
    .tips {
      text-align: center;
      padding-bottom: 10px;
    }
  }
</style>

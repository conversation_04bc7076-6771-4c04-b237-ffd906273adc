<script setup lang="ts">
  import Dialog from '@/components/dialog/index.vue'

  import { formatNumber } from '@/utils/tools'
  import { useUserStore } from '@/store'
  import useSalesDetails from '../hooks/details'
  const globalStore = useUserStore()
  const {
    orderInfo,
    currencyType,
    tradeType,
    pickMethod,
    totalReceived,
    totalPayment,
    stepsActive,
    waitingDespatch,
    materialTotalOutboundNum,
    totalDespatch,
    materialTotalSignNum,
    totalMaterialNum,
    visible,
    fileLoading,
    filePagination,
    fileList,
    fileColumns,
    donloadFile,
    deleteFile,
    files,
    customUpload,
    canUpload,
    canDelete,
    canDownload,
    downloadFile,
    outboundVisible,
    deliveryInfo,
    outboundColumns,
    formatCurrency,
    invoiceAmountVisible,
    saleContract,
    invoiceAmountColumns,
    desLoading,
    stepLoading,
    consignment,
    pickVisible,
    waitSelfPickup,
    openSelfPickupForm,
    selfPickupList,
    selfPickupColumns,
    selfPickupPagination,
    editSelfPickup,
    deleteSelfPickup,
    selfPickupFormVisible,
    selfPickupForm,
    submitSelfPickupForm,
    resetSelfPickupForm,
    itemNameOptions,
    selfPickupFormLoading,
    onPageChange,
  } = useSalesDetails()
  const { t } = useI18n()
  const $t = t
</script>
<template>
  <div class="container">
    <t-space direction="vertical" style="width: 100%">
      <t-card>
        <t-loading size="small" :loading="desLoading">
          <t-descriptions size="small" colon tableLayout="auto" :labelStyle="{ width: 'fit-content' }" :column="4">
            <t-descriptions-item :label="$t('销售单号')">{{ orderInfo?.order_no }}</t-descriptions-item>
            <t-descriptions-item :label="$t('产品名称')">{{ orderInfo?.item_name }}</t-descriptions-item>
            <t-descriptions-item :label="$t('订单日期')">{{ orderInfo?.signing_date }}</t-descriptions-item>
            <t-descriptions-item :label="$t('数量') + '（t）'">{{
              orderInfo?.item_quantity
                .split('/')
                .map((it: any) => formatNumber(it / 1000))
                .join('/')
            }}</t-descriptions-item>
            <t-descriptions-item :label="$t('单价') + `（${currencyType?.[orderInfo?.currency_id]}）`">{{
              orderInfo?.item_price
                .split('/')
                .map((it: any) => formatNumber(it * 1000))
                .join('/')
            }}</t-descriptions-item>
            <t-descriptions-item :label="$t('总金额')">{{
              formatCurrency(orderInfo?.item_amount, orderInfo?.currency_id)
            }}</t-descriptions-item>
            <t-descriptions-item :label="globalStore.userType === 2 ? $t('已付款') : $t('已收款')">
              {{ formatCurrency(totalPayment, orderInfo?.currency_id) }}
            </t-descriptions-item>

            <t-descriptions-item :label="$t('交货方式')">{{
              pickMethod?.[orderInfo?.delivery_type]
            }}</t-descriptions-item>
            <t-descriptions-item :label="$t('交货地点')">{{ orderInfo?.delivery_address }}</t-descriptions-item>
            <t-descriptions-item :label="$t('最晚交货期')">{{ orderInfo?.delivery_date_to }}</t-descriptions-item>
            <t-descriptions-item :label="$t('销售代表')" :span="2">{{ orderInfo?.staff_name }}</t-descriptions-item>
            <t-descriptions-item :label="$t('结算方式')" :span="4">{{
              tradeType?.[orderInfo?.payment_type]
            }}</t-descriptions-item>
            <t-descriptions-item :label="$t('客户')" :span="2">{{ orderInfo?.customer_name }}</t-descriptions-item>
            <t-descriptions-item :label="$t('终端客户')">{{ orderInfo?.end_customer }}</t-descriptions-item>
          </t-descriptions>
        </t-loading>
      </t-card>
      <t-card>
        <template #header>
          <div>
            <t-button size="small" theme="primary" @click="consignment()">{{
              orderInfo?.delivery_type === 1 ? $t('提货') : $t('交货')
            }}</t-button>
            <t-button size="small" theme="primary" :disabled="!orderInfo?.contract_files" @click="downloadFile">{{
              $t('合同附件')
            }}</t-button>

            <t-button size="small" theme="primary" @click="visible = true">{{ $t('交单') }}</t-button>
          </div>
        </template>
        <t-card :bordered="false" :loading="stepLoading">
          <t-steps theme="dot" :current="stepsActive" readonly layout="vertical">
            <t-step-item :title="$t('已双签')"> </t-step-item>
            <t-step-item v-if="orderInfo?.delivery_type == 1" @click="pickVisible = true">
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('待提货') }}</div>
                    <div>（{{ formatNumber((totalMaterialNum - materialTotalOutboundNum) / 1000) || 0 }} t）</div>
                  </t-col>
                  <t-col :span="3">
                    <t-button size="small" variant="base">{{ $t('详情') }}</t-button>
                  </t-col>
                </t-row>
              </template>
              <template #extra> </template>
            </t-step-item>

            <t-step-item v-else>
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('待发货') }}</div>
                    <div>
                      （{{ !waitingDespatch || waitingDespatch < 0 ? 0 : formatNumber(waitingDespatch / 1000) || 0 }}）t
                    </div>
                  </t-col>
                </t-row>
              </template>
            </t-step-item>
            <t-step-item>
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('已出库') }}</div>
                    <div>（{{ formatNumber(materialTotalOutboundNum / 1000) || 0 }}）t</div>
                  </t-col>
                  <t-col v-if="materialTotalOutboundNum" :span="3">
                    <t-button size="small" variant="base" @click="outboundVisible = true">{{ $t('详情') }}</t-button>
                  </t-col>
                </t-row>
              </template>
            </t-step-item>

            <t-step-item v-if="orderInfo?.delivery_type !== 1">
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('已发货') }}</div>
                    <div>（{{ formatNumber(totalDespatch / 1000) || 0 }}）t</div>
                  </t-col>
                </t-row>
              </template>
            </t-step-item>
            <t-step-item v-if="orderInfo?.delivery_type !== 1" @click="consignment(1)">
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('在途') }}</div>
                    <div>（{{ formatNumber((totalDespatch - materialTotalSignNum) / 1000) || 0 }}）t</div>
                  </t-col>
                  <t-col :span="3">
                    <t-button size="small" variant="base">{{ $t('详情') }}</t-button>
                  </t-col>
                </t-row>
              </template>
            </t-step-item>
            <t-step-item v-if="orderInfo?.delivery_type !== 1">
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('已签收') }}</div>
                    <div>（{{ formatNumber(materialTotalSignNum / 1000) || 0 }}）t</div>
                  </t-col>
                </t-row>
              </template>
            </t-step-item>
            <t-step-item>
              <template #title>
                <t-row :gutter="16" align="center" class="step-row">
                  <t-col :span="3">
                    <div>{{ $t('已开票') }}</div>
                    <div>（{{ formatCurrency(totalReceived, orderInfo?.currency_id) || 0 }}）</div>
                  </t-col>
                  <t-col v-if="totalReceived" :span="3">
                    <t-button size="small" variant="base" @click="invoiceAmountVisible = true">{{
                      $t('详情')
                    }}</t-button>
                  </t-col>
                </t-row>
              </template>
            </t-step-item>
          </t-steps>
        </t-card>
      </t-card>
    </t-space>
    <!-- 已出库弹窗 -->
    <Dialog v-model="outboundVisible" :footer="false" width="460px">
      <template #header>{{ $t('已出库详情') }}</template>
      <template #body>
        <t-table
          row-key="index"
          size="small"
          hover
          max-height="260"
          :data="deliveryInfo"
          :columns="outboundColumns"
          lazy-load
        >
          <template #quantity="{ row }">
            <span>{{ formatNumber(row.quantity / 1000) || 0 }} t</span>
          </template>
        </t-table>
      </template>
    </Dialog>
    <!-- 已开票弹窗 -->
    <Dialog v-model="invoiceAmountVisible" :footer="false" width="520px">
      <template #header>{{ $t('结算详情') }}</template>
      <template #body>
        <t-table
          row-key="index"
          size="small"
          hover
          max-height="260"
          :data="saleContract"
          :columns="invoiceAmountColumns"
          lazy-load
        >
          <template #invoice_sum="{ row }">
            <span>{{ formatCurrency(row.invoice_sum, row.currency) }}</span>
          </template>
          <template #paid="{ row }">
            <span>{{ formatCurrency(row.paid, row.currency) }}</span>
          </template>
          <template #unpaid="{ row }">
            <span>{{ formatCurrency(row.unpaid, row.currency) }}</span>
          </template>
        </t-table>
      </template>
    </Dialog>
    <!-- 待提货 -->
    <Dialog v-model="pickVisible" :footer="false" width="920px" top="5%">
      <template #header>{{ $t('待提货详情') }}</template>
      <template #body>
        <t-descriptions
          v-if="orderInfo.item_name"
          :title="`${$t('产品')}：${orderInfo?.item_name}`"
          table-layout="auto"
          bordered
          :column="2"
        >
          <t-descriptions-item :label="$t('销售单号')">{{ orderInfo?.order_no }}</t-descriptions-item>
          <t-descriptions-item :label="$t('待提货量') + '（t）'">{{
            formatNumber(waitSelfPickup / 1000)
          }}</t-descriptions-item>
          <t-descriptions-item :label="$t('发货单位')">{{ orderInfo?.subsidiary_name }}</t-descriptions-item>
          <t-descriptions-item :label="$t('收货单位')">{{ orderInfo?.customer_name }}</t-descriptions-item>
        </t-descriptions>
        <t-space style="margin: 20px 0">
          <t-button size="small" @click="openSelfPickupForm()">{{ $t('填写自提单') }}</t-button>
        </t-space>

        <t-table
          row-key="index"
          max-height="200px"
          size="small"
          :data="selfPickupList"
          :columns="selfPickupColumns"
          :pagination="selfPickupPagination"
          @page-change="onPageChange"
        >
          <template #pickupQuantity="{ row }">
            <span>{{ formatNumber(row.pickupQuantity) || 0 }} t</span>
          </template>
          <template #link="{ row }">
            <t-button size="small" theme="primary" @click="editSelfPickup(row)">{{ $t('编辑') }}</t-button>
            <t-popconfirm :content="$t('确认删除吗')" @confirm="deleteSelfPickup(row)">
              <t-button size="small" theme="warning">{{ $t('删除') }}</t-button>
            </t-popconfirm>
          </template>
        </t-table>
      </template>
    </Dialog>
    <!-- 自提订单form弹窗 -->
    <Dialog v-model="selfPickupFormVisible" :footer="false" width="580px">
      <template #body>
        <t-form label-width="auto" :data="selfPickupForm" @submit="submitSelfPickupForm" @reset="resetSelfPickupForm">
          <t-space break-line>
            <t-form-item :label="$t('发货单位')">
              <t-input
                clearable
                disabled
                v-model="selfPickupForm.subsidiary_id"
                :placeholder="$t('请输入发货单位')"
              ></t-input>
            </t-form-item>
            <t-form-item :label="$t('收货单位')">
              <t-input
                clearable
                disabled
                v-model="selfPickupForm.contract_name"
                :placeholder="$t('请输入收货单位')"
              ></t-input>
            </t-form-item>
            <t-form-item :label="$t('货物名称')">
              <t-input
                clearable
                disabled
                v-model="selfPickupForm.itemName"
                :placeholder="$t('请输入货物名称')"
                v-if="orderInfo?.item_name && orderInfo?.item_name.split('/').length <= 1"
              ></t-input>
              <t-select
                v-else
                v-model="selfPickupForm.itemName"
                :options="itemNameOptions"
                :placeholder="$t('请选择货物名称')"
                clearable
              />
            </t-form-item>
            <t-form-item :label="$t('装货重量')">
              <t-input-number v-model="selfPickupForm.pickupQuantity" theme="normal"></t-input-number>
            </t-form-item>
            <t-form-item :label="$t('车头号')">
              <t-input clearable v-model="selfPickupForm.vehicleNo" placeholder="请输入车头号"></t-input>
            </t-form-item>
            <t-form-item :label="$t('挂车号')">
              <t-input clearable v-model="selfPickupForm.trailerNo" placeholder="请输入挂车号"></t-input>
            </t-form-item>
            <t-form-item :label="$t('司机姓名')">
              <t-input clearable v-model="selfPickupForm.driverName" placeholder="请输入司机姓名"></t-input>
            </t-form-item>
            <t-form-item :label="$t('电话')">
              <t-input clearable v-model="selfPickupForm.driverTel" placeholder="请输入电话"></t-input>
            </t-form-item>
            <t-form-item :label="$t('身份证号')">
              <t-input clearable v-model="selfPickupForm.driverIdCard" placeholder="请输入身份证号"></t-input>
            </t-form-item>
            <t-form-item :label="$t('车型')">
              <t-input clearable v-model="selfPickupForm.vehicleType" placeholder="请输入车型"></t-input>
            </t-form-item>
            <t-form-item :label="$t('空车皮重')">
              <t-input clearable v-model="selfPickupForm.tareWeight" placeholder="请输入空车皮重"></t-input>
            </t-form-item>
          </t-space>
          <t-form-item>
            <t-space style="margin-top: 20px; flex: 1; justify-content: flex-end">
              <t-button size="small" theme="primary" type="submit" :loading="selfPickupFormLoading">{{
                $t('保存')
              }}</t-button>
              <t-button
                size="small"
                theme="default"
                variant="base"
                type="reset"
                @click="selfPickupFormVisible = false"
                >{{ $t('取消') }}</t-button
              >
            </t-space>
          </t-form-item>
        </t-form>
      </template>
    </Dialog>
    <!-- 交单弹窗 -->
    <Dialog v-model="visible" :footer="false" width="600px">
      <template #header>{{ $t('交单详情') }}</template>
      <template #body>
        <t-table
          row-key="index"
          height="200px"
          size="small"
          :loading="fileLoading"
          :pagination="filePagination"
          :data="fileList"
          :columns="fileColumns"
          table-layout="fixed"
        >
          <template #link="{ row }">
            <t-button v-if="canDownload" size="small" @click="donloadFile(row.id)">{{ $t('下载') }}</t-button>
            <t-popconfirm
              v-if="canDelete"
              theme="default"
              :content="$t('确认删除此文件吗')"
              @confirm="deleteFile(row.id)"
            >
              <t-button size="small" theme="warning">{{ $t('删除') }}</t-button>
            </t-popconfirm>
          </template>
        </t-table>
        <t-upload v-if="canUpload" v-model="files" theme="custom" :request-method="customUpload" draggable>
          <template #dragContent="params">
            <div>{{ $t('点击上传 / 拖拽到此区域') }}</div>
          </template>
        </t-upload>
      </template>
    </Dialog>
  </div>
</template>
<style scoped lang="less">
  :deep(.t-steps) {
    .t-steps-item--process {
      .t-steps-item__title {
        font-weight: 100;
      }
    }
  }

  :deep(.t-upload__dragger-center) {
    margin: 20px auto 0;
    height: 120px;
  }
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 6px;
    border: 2px solid transparent;
    background-clip: content-box;
    background-color: var(--td-scrollbar-color);
  }
  .step-row {
    color: inherit;
    width: 60vw;

    div:nth-child(1) {
      display: flex;
    }
  }
</style>

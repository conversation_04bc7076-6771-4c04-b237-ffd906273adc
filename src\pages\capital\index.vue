<script setup lang="ts">
  import useCapital from './hooks'
  const { t } = useI18n()
  const $t = t
  const {
    projectId,
    statusId,
    dateTabs,
    changeStatus,
    projectList,
    formatCurrency,
    cardList,
    onSubmit,
    loading,
    lookDetail,
  } = useCapital()

  const cashIconBase64 =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGIAAAASCAYAAACghwvPAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAACrSURBVFhH7ZixCsQgEAXz/39oY6NWgtpYGxY05OJdsbtHIuENDGzSpBhE49bAEiCEklJKn3QghJIYY8s59yc5CKGEQhhj1DEQQskIQaaU+ls+rBDOOXjRWnuE0MRghTh/EH7qvT9mSQxRiFor/CIFkMYQhQC/CSGIYmBF/NHbVwScpY17zNwIBCvE9cQAHzo1gZlH/iPAzAihiUAghBLcNS0Cbl9fBkIsQWs7z5rmJA4hcEkAAAAASUVORK5CYII='
</script>
<template>
  <div class="container">
    <t-card>
      <t-form layout="inline" label-width="auto" @submit="onSubmit">
        <t-form-item :label="$t('项目名称')">
          <t-select
            size="small"
            v-model="projectId"
            :options="projectList"
            :keys="{ label: 'text' }"
            :onChange="onSubmit"
          />
        </t-form-item>
        <t-form-item>
          <t-button theme="primary" size="small" type="submit">{{ $t('查询') }}</t-button>
        </t-form-item>
      </t-form>
      <div class="status-btn">
        <template v-for="s in dateTabs" :key="s.id">
          <t-button
            size="small"
            :theme="statusId == s.id ? 'success' : 'primary'"
            :variant="statusId == s.id ? 'base' : 'outline'"
            @click="changeStatus(s.id)"
            >{{ s.name }}</t-button
          >
          <img class="img" :src="cashIconBase64" />
        </template>
      </div>
    </t-card>
    <div class="card-container">
      <t-row :gutter="16" style="row-gap: 16px">
        <t-col :md="6" :sm="6" :xs="6" :lg="3" v-for="c in cardList" :key="c.id" class="col">
          <t-card>
            <t-loading size="small" :loading="c.loading()">
              <div class="card-body">
                <div class="title-wrap">
                  <div class="title">
                    {{ c.title }}
                  </div>
                  <div v-if="c.list.length" style="min-height: 44px">
                    <h4 class="money" v-for="(item, idx) in c.list" :key="idx">
                      {{ formatCurrency(item.occupyAmount, item.currencyId) }}
                    </h4>
                  </div>
                  <div v-else class="noData">0.00</div>
                </div>
                <div class="text" v-if="c.list.length && c.isDetail" @click="c.type ? lookDetail(c.type) : ''">
                  <span>{{ $t('查看详情') }}</span>
                  <t-icon name="chevron-right-double-s" size="20" />
                </div>
                <span class="icon">
                  <t-icon name="currency-exchange" color="#0052d9" size="24" />
                </span>
              </div>
            </t-loading>
          </t-card>
        </t-col>
      </t-row>
    </div>
  </div>
</template>
<style scoped lang="less">
  .card-container {
    margin: 20px 0;
    .col {
      &:nth-child(1) .money {
        color: #00b0f0;
      }
      &:nth-child(2) .money {
        color: #002060;
      }
      &:nth-child(3) .money {
        color: #ffc000;
      }
      &:nth-child(4) .money {
        color: #ff7600;
      }
      &:nth-child(5) .money {
        color: var(--td-brand-color-7);
      }
    }
    .t-card {
      min-height: 156px;
      .card-body {
        height: 100%;
        padding: 10px 10px 4px;
        box-sizing: border-box;
        position: relative;
        .icon {
          position: absolute;
          right: 10px;
          top: 10px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: var(--td-comp-size-xxl);
          height: var(--td-comp-size-xxl);
          background: var(--td-brand-color-light);
          border-radius: 50%;
        }
        .money {
          text-align: center;
        }
        .noData {
          text-align: center;
          font-size: 18px;
          color: #999;
        }
        .text {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 12px;
          margin-top: 10px;
        }
      }
    }
    .title {
      font-size: 12px;
      margin-bottom: 16px;
      color: #525252;
    }
  }
  .status-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px;
    .img {
      width: 50px;
      height: 10px;
      &:last-child {
        display: none;
      }
    }
  }
</style>

import { useSessionStorage } from '@vueuse/core'
import { $getList } from '@/api/inquiryQuote/acquisition'
import { groupBy, sortBy, uniqWith } from 'lodash-es'
import { useRequest } from 'vue-request'
import { useNsListLabel, useNsListLabelRaw } from '@/use/dict'
import { vueI18n } from '@/i18n'

export const useAcquisitionStore = defineStore('inquiryQuote/acquisition', () => {
  const { t } = vueI18n.global
  /* tabs active */
  const activeTab = useSessionStorage<string | number>('scm-acquisitionTab', 0)
  const nsList = useNsListLabel('customlist605')

  const getProductList = async () => {
    if (productList.value?.length) productList.value
    const res = await $getList('vn-raw-procure-rfq')
    if (res.code !== 200) {
      MessagePlugin.error(res.msg)
      return []
    }
    const itemGroup = groupBy(res.data, 'itemId')
    return Object.entries(itemGroup).map((g) => {
      const groupData = sortBy(g[1], 'sortBy')
      const configList = groupBy(
        groupData.flatMap((c) =>
          c.metricsConfig.map((m: any) => ({
            ...m,
            configName: c.configName,
          })),
        ),
        'key',
      )
      const bannerConfig = Object.entries(configList).map((cg) => {
        const formatValueG = uniqWith(cg[1], (v1, v2) => (v1.value || '').trim() === (v2.value || '').trim())
        return {
          key: cg[0],
          label: t(cg[1][0].label),
          value: formatValueG.length > 1 ? formatValueG : t(formatValueG[0].value),
        }
      })

      return {
        itemId: g[0],
        itemName: groupData[0].itemName,
        itemCode: groupData[0].itemCode,
        priceMap: groupData.reduce((acc, val) => {
          acc[val.configName] = Number(val.standardPrice)
          return acc
        }, {} as Record<string, number>),
        configs: groupData,
        bannerConfig,
      }
    })
  }

  const { data: productList, runAsync: loadProductList } = useRequest(getProductList, {
    manual: true,
    defaultParams: [],
  })

  const productMap = computed<Record<string, any>>(() =>
    (productList.value || []).reduce((acc, cur) => {
      acc[cur.itemId] = cur
      return acc
    }, {} as Record<string, any>),
  )

  /**
   * 按货品查找质量标准，根据质量等级和水分产生单价
   */
  const calcPrice = async (itemId: number, weightLevel: number, waterContent: string) => {
    await loadProductList()
    await nextTick()
    const productData = productMap.value[itemId]
    if (!productData) return 0
    let levelLabelMap = nsList['customlist605']
    if (!levelLabelMap) {
      levelLabelMap = (await useNsListLabelRaw('customlist605'))['customlist605']
    }
    const weightLevelLabel = nsList['customlist605'][weightLevel]
    const levelPrice = productData.priceMap?.[weightLevelLabel]
    if (!levelPrice) return 0
    const water = Number(waterContent.replace('%', ''))
    if (!water) return levelPrice
    if (water <= 14) {
      return levelPrice
    }
    if (water > 30) {
      return 0
    }

    return Math.round(levelPrice / (1 + (water - 14) * 0.015))
  }

  return {
    activeTab,
    productList,
    calcPrice,
    productMap,
    loadProductList,
  }
})

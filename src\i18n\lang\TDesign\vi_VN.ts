import 'dayjs/locale/vi'
const vi_VN = {
  autoComplete: {
    empty: 'Dữ liệu trống',
  },
  pagination: {
    itemsPerPage: '{size} / trang',
    jumpTo: 'Nh<PERSON>y tới',
    page: '',
    total: '{total} mục',
  },
  cascader: {
    empty: 'D<PERSON> liệu trống',
    loadingText: 'đang tải...',
    placeholder: 'vui lòng chọn',
  },
  calendar: {
    yearSelection: '{year}',
    monthSelection: '{month}',
    yearRadio: 'năm',
    monthRadio: 'tháng',
    hideWeekend: 'Ẩn cuối tuần',
    showWeekend: 'Hiện cuối tuần',
    today: 'Hôm nay',
    thisMonth: 'Tháng này',
    week: '<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>',
    cellMonth: '<PERSON>h<PERSON><PERSON> 1,<PERSON>h<PERSON><PERSON> 2,<PERSON>h<PERSON>g 3,<PERSON><PERSON><PERSON><PERSON> 4,<PERSON><PERSON><PERSON>g 5,<PERSON><PERSON><PERSON><PERSON> 6,<PERSON><PERSON><PERSON><PERSON> 7,<PERSON><PERSON><PERSON><PERSON> 8,<PERSON><PERSON><PERSON><PERSON> 9,<PERSON><PERSON><PERSON><PERSON> 10,<PERSON><PERSON><PERSON><PERSON> 11,<PERSON><PERSON><PERSON><PERSON> 12',
  },
  transfer: {
    title: '{checked} / {total}',
    empty: 'Dữ liệu trống',
    placeholder: 'nhập từ khóa để tìm kiếm',
  },
  timePicker: {
    dayjsLocale: 'vi',
    now: 'Bây giờ',
    confirm: 'Xác nhận',
    anteMeridiem: 'SA',
    postMeridiem: 'CH',
    placeholder: 'vui lòng chọn',
  },
  dialog: {
    confirm: 'Xác nhận',
    cancel: 'Hủy',
  },
  drawer: {
    confirm: 'Xác nhận',
    cancel: 'Hủy',
  },
  popconfirm: {
    confirm: {
      content: 'OK',
    },
    cancel: {
      content: 'Hủy',
    },
  },
  table: {
    empty: 'Dữ liệu trống',
    loadingText: 'đang tải...',
    loadingMoreText: 'tải thêm',
    filterInputPlaceholder: '',
    sortAscendingOperationText: 'nhấp để sắp xếp tăng dần',
    sortCancelOperationText: 'nhấp để hủy sắp xếp',
    sortDescendingOperationText: 'nhấp để sắp xếp giảm dần',
    clearFilterResultButtonText: 'Xóa',
    columnConfigButtonText: 'Cấu hình cột',
    columnConfigTitleText: 'Cấu hình cột bảng',
    columnConfigDescriptionText: 'Vui lòng chọn cột để hiển thị trong bảng',
    confirmText: 'Xác nhận',
    cancelText: 'Hủy',
    resetText: 'Đặt lại',
    selectAllText: 'Chọn tất cả',
    searchResultText: 'Tìm kiếm "{result}". Tìm thấy {count} mục.',
  },
  select: {
    empty: 'Dữ liệu trống',
    loadingText: 'đang tải...',
    placeholder: 'vui lòng chọn',
  },
  tree: {
    empty: 'Dữ liệu trống',
  },
  treeSelect: {
    empty: 'Dữ liệu trống',
    loadingText: 'đang tải...',
    placeholder: 'vui lòng chọn',
  },
  datePicker: {
    dayjsLocale: 'vi',
    placeholder: {
      date: 'chọn ngày',
      month: 'chọn tháng',
      year: 'chọn năm',
      quarter: 'chọn quý',
      week: 'chọn tuần',
    },
    weekdays: ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'],
    months: [
      'Thg 1',
      'Thg 2',
      'Thg 3',
      'Thg 4',
      'Thg 5',
      'Thg 6',
      'Thg 7',
      'Thg 8',
      'Thg 9',
      'Thg 10',
      'Thg 11',
      'Thg 12',
    ],
    quarters: ['Q1', 'Q2', 'Q3', 'Q4'],
    rangeSeparator: ' - ',
    direction: 'ltr',
    format: 'DD/MM/YYYY', // Vietnamese date format
    dayAriaLabel: 'N',
    yearAriaLabel: 'N',
    monthAriaLabel: 'T',
    weekAbbreviation: 'Tuần',
    confirm: 'Xác nhận',
    selectTime: 'Chọn giờ',
    selectDate: 'Chọn ngày',
    nextYear: 'Năm sau',
    preYear: 'Năm trước',
    nextMonth: 'Tháng sau',
    preMonth: 'Tháng trước',
    preDecade: 'Thập kỷ trước',
    nextDecade: 'Thập kỷ sau',
    now: 'Bây giờ',
  },
  upload: {
    sizeLimitMessage: 'Tệp quá lớn để tải lên. {sizeLimit}',
    cancelUploadText: 'Hủy',
    triggerUploadText: {
      fileInput: 'Tải lên',
      image: 'Nhấp để tải lên',
      normal: 'Tải lên',
      reupload: 'Tải lên lại',
      continueUpload: 'Tiếp tục tải lên',
      delete: 'Xóa',
      uploading: 'Đang tải lên',
    },
    dragger: {
      dragDropText: 'Thả vào đây',
      draggingText: 'Kéo tệp vào đây để tải lên',
      clickAndDragText: 'Nhấp "Tải lên" hoặc kéo tệp vào đây',
    },
    file: {
      fileNameText: 'tên tệp',
      fileSizeText: 'kích thước',
      fileStatusText: 'trạng thái',
      fileOperationText: 'thao tác',
      fileOperationDateText: 'ngày',
    },
    progress: {
      uploadingText: 'Đang tải lên',
      waitingText: 'Đang chờ',
      failText: 'Thất bại',
      successText: 'Thành công',
    },
  },
  form: {
    errorMessage: {
      date: '${name} không hợp lệ',
      url: '${name} không hợp lệ',
      required: '${name} là bắt buộc',
      whitespace: '${name} không được để trống',
      max: '${name} phải có ít nhất ${validate} ký tự',
      min: '${name} không được dài hơn ${validate} ký tự',
      len: '${name} phải chính xác ${validate} ký tự',
      enum: '${name} phải là một trong ${validate}',
      idcard: '${name} không hợp lệ',
      telnumber: '${name} không hợp lệ',
      pattern: '${name} không hợp lệ',
      validator: '${name} không hợp lệ',
      boolean: '${name} phải là true/false',
      number: '${name} phải là số',
    },
    colonText: ':',
  },
  input: {
    placeholder: 'vui lòng nhập',
  },
  list: {
    loadingText: 'đang tải...',
    loadingMoreText: 'tải thêm',
  },
  alert: {
    expandText: 'mở rộng',
    collapseText: 'thu gọn',
  },
  anchor: {
    copySuccessText: 'đã sao chép liên kết',
    copyText: 'sao chép liên kết',
  },
  colorPicker: {
    swatchColorTitle: 'Mặc định hệ thống',
    recentColorTitle: 'Đã dùng gần đây',
    clearConfirmText: 'Xóa màu đã dùng gần đây?',
    singleColor: 'Đơn sắc',
    gradientColor: 'Gradient',
  },
  guide: {
    finishButtonProps: {
      content: 'Hoàn thành',
      theme: 'primary',
    },
    nextButtonProps: {
      content: 'Bước tiếp',
      theme: 'primary',
    },
    skipButtonProps: {
      content: 'Bỏ qua',
      theme: 'default',
    },
    prevButtonProps: {
      content: 'Bước trước',
      theme: 'default',
    },
  },
  image: {
    errorText: 'không thể tải',
    loadingText: 'đang tải',
  },
  imageViewer: {
    errorText: 'không thể tải',
    mirrorTipText: 'phản chiếu',
    rotateTipText: 'xoay',
    originalSizeTipText: 'kích thước gốc',
  },
  typography: {
    expandText: 'thêm',
    collapseText: 'thu gọn',
    copiedText: 'đã sao chép',
  },
  rate: {
    rateText: ['tệ', 'thất vọng', 'bình thường', 'hài lòng', 'ngạc nhiên'],
  },
  empty: {
    titleText: {
      maintenance: 'Đang bảo trì',
      success: 'Thành công',
      fail: 'Thất bại',
      empty: 'Không có dữ liệu',
      networkError: 'Lỗi mạng',
    },
  },
  descriptions: {
    colonText: ':',
  },
  chat: {
    placeholder: 'nhập tin nhắn...',
    stopBtnText: 'dừng',
    refreshTipText: 'tạo lại',
    copyTipText: 'sao chép',
    likeTipText: 'thích',
    dislikeTipText: 'không thích',
    copyCodeBtnText: 'sao chép mã',
    copyCodeSuccessText: 'đã sao chép',
    clearHistoryBtnText: 'xóa lịch sử',
    copyTextSuccess: 'đã sao chép',
    copyTextFail: 'sao chép thất bại',
    confirmClearHistory: 'Bạn chắc chắn muốn xóa toàn bộ tin nhắn?',
  },
}
export default vi_VN

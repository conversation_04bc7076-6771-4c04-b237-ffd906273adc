/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Dialog: typeof import('./src/components/dialog/index.vue')['default']
    Empty: typeof import('./src/components/Empty/index.vue')['default']
    LuckySheet: typeof import('./src/components/LuckySheet/index.vue')['default']
    Result: typeof import('./src/components/result/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TAnchor: typeof import('tdesign-vue-next')['Anchor']
    TAnchorItem: typeof import('tdesign-vue-next')['AnchorItem']
    TAside: typeof import('tdesign-vue-next')['Aside']
    TAutoComplete: typeof import('tdesign-vue-next')['AutoComplete']
    TAvatar: typeof import('tdesign-vue-next')['Avatar']
    TBadge: typeof import('tdesign-vue-next')['Badge']
    TBaseTable: typeof import('tdesign-vue-next')['BaseTable']
    TBreadcrumb: typeof import('tdesign-vue-next')['Breadcrumb']
    TBreadcrumbItem: typeof import('tdesign-vue-next')['BreadcrumbItem']
    TButton: typeof import('tdesign-vue-next')['Button']
    TCalendar: typeof import('tdesign-vue-next')['Calendar']
    TCard: typeof import('tdesign-vue-next')['Card']
    TCheckbox: typeof import('tdesign-vue-next')['Checkbox']
    TCheckboxGroup: typeof import('tdesign-vue-next')['CheckboxGroup']
    TCol: typeof import('tdesign-vue-next')['Col']
    TCollapse: typeof import('tdesign-vue-next')['Collapse']
    TCollapsePanel: typeof import('tdesign-vue-next')['CollapsePanel']
    TConfigProvider: typeof import('tdesign-vue-next')['ConfigProvider']
    TContent: typeof import('tdesign-vue-next')['Content']
    TDatePicker: typeof import('tdesign-vue-next')['DatePicker']
    TDateRangePicker: typeof import('tdesign-vue-next')['DateRangePicker']
    TDescriptions: typeof import('tdesign-vue-next')['Descriptions']
    TDescriptionsItem: typeof import('tdesign-vue-next')['DescriptionsItem']
    TDialog: typeof import('tdesign-vue-next')['Dialog']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TDropdown: typeof import('tdesign-vue-next')['Dropdown']
    TDropdownItem: typeof import('tdesign-vue-next')['DropdownItem']
    TDropdownMenu: typeof import('tdesign-vue-next')['DropdownMenu']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    THeader: typeof import('tdesign-vue-next')['Header']
    THeadMenu: typeof import('tdesign-vue-next')['HeadMenu']
    TIcon: typeof import('tdesign-vue-next')['Icon']
    TImageViewer: typeof import('tdesign-vue-next')['ImageViewer']
    TInput: typeof import('tdesign-vue-next')['Input']
    TInputNumber: typeof import('tdesign-vue-next')['InputNumber']
    TLayout: typeof import('tdesign-vue-next')['Layout']
    TLink: typeof import('tdesign-vue-next')['Link']
    TList: typeof import('tdesign-vue-next')['List']
    TListItem: typeof import('tdesign-vue-next')['ListItem']
    TListItemMeta: typeof import('tdesign-vue-next')['ListItemMeta']
    TLoading: typeof import('tdesign-vue-next')['Loading']
    TMenu: typeof import('tdesign-vue-next')['Menu']
    TMenuItem: typeof import('tdesign-vue-next')['MenuItem']
    TOption: typeof import('tdesign-vue-next')['Option']
    TOptionGroup: typeof import('tdesign-vue-next')['OptionGroup']
    TPopconfirm: typeof import('tdesign-vue-next')['Popconfirm']
    TPopup: typeof import('tdesign-vue-next')['Popup']
    TRadioButton: typeof import('tdesign-vue-next')['RadioButton']
    TRadioGroup: typeof import('tdesign-vue-next')['RadioGroup']
    Trend: typeof import('./src/components/trend/index.vue')['default']
    TRow: typeof import('tdesign-vue-next')['Row']
    TSelect: typeof import('tdesign-vue-next')['Select']
    TSpace: typeof import('tdesign-vue-next')['Space']
    TStepItem: typeof import('tdesign-vue-next')['StepItem']
    TSteps: typeof import('tdesign-vue-next')['Steps']
    TSubmenu: typeof import('tdesign-vue-next')['Submenu']
    TSwitch: typeof import('tdesign-vue-next')['Switch']
    TTable: typeof import('tdesign-vue-next')['Table']
    TTabPanel: typeof import('tdesign-vue-next')['TabPanel']
    TTabs: typeof import('tdesign-vue-next')['Tabs']
    TTag: typeof import('tdesign-vue-next')['Tag']
    TTagInput: typeof import('tdesign-vue-next')['TagInput']
    TTextarea: typeof import('tdesign-vue-next')['Textarea']
    TTree: typeof import('tdesign-vue-next')['Tree']
    TTypographyText: typeof import('tdesign-vue-next')['Text']
    TTypographyTitle: typeof import('tdesign-vue-next')['Title']
    TUpload: typeof import('tdesign-vue-next')['Upload']
    UniverSheet: typeof import('./src/components/UniverSheet/index.vue')['default']
  }
}

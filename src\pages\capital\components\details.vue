<script setup lang="ts">
  import personnelChart from './personnelChart.vue'
  import useCapitalDetails from '../hooks/useCapitalDetails'
  const { title, showLoading, chartParams, legend, projectPerson, projectPersonOverdue, projectPersonOverdueTotal } =
    useCapitalDetails()
</script>
<template>
  <div class="container">
    <t-loading :loading="showLoading" size="small">
      <personnelChart
        :totalMoney="projectPerson"
        :imminentMoney="projectPersonOverdue"
        :overdueMoney="projectPersonOverdueTotal"
        :title="title"
        :params="chartParams"
        :legend="legend"
      />
    </t-loading>
  </div>
</template>
<style scoped></style>

/// <reference types="vitest" />

import { ConfigEnv, UserConfig, loadEnv } from 'vite'

import createVuePlugin from '@vitejs/plugin-vue'
import VueDevtools from 'vite-plugin-vue-devtools'
import vueJsx from '@vitejs/plugin-vue-jsx'
import svgLoader from 'vite-svg-loader'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { TDesignResolver } from 'unplugin-vue-components/resolvers'

import path from 'path'

const CWD = process.cwd()
export const isProduction = () => process.env.NODE_ENV === 'production'

import { viteMockServe } from 'vite-plugin-mock'

// https://vitejs.dev/config/
export default ({ mode }: ConfigEnv): UserConfig => {
  const env = loadEnv(mode, CWD)
  return {
    base: mode === 'production' ? '/scm/' : '/',
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },

    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            hack: `true; @import (reference) "${path.resolve('src/style/variables.less')}";`,
          },
          math: 'strict',
          javascriptEnabled: true,
        },
      },
    },

    plugins: [
      VueDevtools(),
      createVuePlugin(),
      vueJsx(),
      svgLoader(),
      AutoImport({
        resolvers: [
          TDesignResolver({
            library: 'vue-next',
          }),
        ],
        imports: ['vue', 'vue-router', 'pinia', 'vue-i18n'],
      }),
      Components({
        resolvers: [
          TDesignResolver({
            library: 'vue-next',
          }),
        ],
      }),
      viteMockServe({
        mockPath: 'mock', // mock文件存放目录
        enable: true,
      }),
    ],

    server: {
      port: 5317,
      host: true,
      open: true,
      proxy: {
        [env.VITE_APP_BASE_API]: {
          // target: `http://**************:8654`,
          // target: `http://localhost:8654`,
          target: `https://external.honoroad.cn/gateway`, // 正式接口
          changeOrigin: true,
          rewrite: (p) => p.replace(env.VITE_APP_BASE_API, ''),
        },
      },
    },
    esbuild: {
      drop: isProduction() ? ['console', 'debugger'] : [],
    },
    test: {
      globals: true,
      environment: 'happy-dom',
    },
  }
}

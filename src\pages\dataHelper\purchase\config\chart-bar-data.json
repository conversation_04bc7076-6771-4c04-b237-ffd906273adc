{"dataZoom": [{"type": "inside", "show": true, "xAxisIndex": [0], "start": 1, "end": 45}], "legend": {"data": ["<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON> kho"]}, "label": {"show": true, "position": "top", "color": "#919294", "fontSize": 10}, "xAxis": {"type": "category", "data": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]}, "yAxis": {"type": "value", "axisLabel": ""}, "series": [{"name": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "barMinWidth": 20, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "type": "bar"}, {"name": "<PERSON><PERSON><PERSON><PERSON> kho", "barMinWidth": 20, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "type": "bar"}]}
import 'axios'
declare module 'axios' {
  import { AxiosRequestConfig } from 'axios'
  interface AxiosResponse<T = any> {
    [key: string]: any
    code: number
    msg: string
    data: T
    rows: T[]
    total: number
  }

  interface RequestOptions {
    apiUrl?: string
    isJoinPrefix?: boolean
    urlPrefix?: string
    joinParamsToUrl?: boolean
    formatDate?: boolean
    isTransformResponse?: boolean
    isReturnNativeResponse?: boolean
    ignoreRepeatRequest?: boolean
    joinTime?: boolean
    withToken?: boolean
    retry?: {
      count: number
      delay: number
    }
  }

  interface Result<T = any> {
    code: number
    data: T
  }

  interface AxiosRequestConfigRetry extends AxiosRequestConfig {
    retryCount?: number
  }
}

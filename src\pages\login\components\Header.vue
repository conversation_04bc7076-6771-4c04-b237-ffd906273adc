<template>
  <header class="login-header">
    <img :src="logo" shape="round" class="logo" />
  </header>
</template>

<script setup lang="ts">
  import logo from '@/assets/assets-company-logo.png'
  import { useSettingStore } from '@/store'

  const settingStore = useSettingStore()
</script>

<style lang="less" scoped>
  .login-header {
    padding: 0 5%;
    backdrop-filter: blur(5px);

    .logo {
      width: 96px;
      object-fit: cover;
    }

    .operations-container {
      display: flex;
      align-items: center;
      .t-button {
        margin-left: 16px;
      }

      .icon {
        height: 20px;
        width: 20px;
        padding: 6px;
        box-sizing: content-box;

        &:hover {
          cursor: pointer;
        }
      }
    }
  }
</style>

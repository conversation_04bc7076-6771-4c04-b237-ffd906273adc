<template>
  <t-form
    v-if="type == 'password'"
    ref="form"
    :class="['item-container', `login-${type}`]"
    :data="formData"
    :rules="FORM_RULES"
    label-width="0"
    @submit="onSubmit"
  >
    <t-form-item name="username">
      <t-input size="small" autocomplete="username" v-model="formData.username" :placeholder="$t('请输入用户名')">
        <template #prefix-icon>
          <t-icon name="user" />
        </template>
      </t-input>
    </t-form-item>

    <t-form-item name="password">
      <t-input
        size="small"
        autocomplete="current-password"
        v-model="formData.password"
        :type="showPsw ? 'text' : 'password'"
        clearable
        :placeholder="$t('请输入密码')"
      >
        <template #prefix-icon>
          <t-icon name="lock-on" />
        </template>
        <template #suffix-icon>
          <t-icon :name="showPsw ? 'browse' : 'browse-off'" @click="showPsw = !showPsw" />
        </template>
      </t-input>
    </t-form-item>
    <div class="verification-code">
      <t-form-item name="code">
        <t-input size="small" v-model="formData.code" :placeholder="$t('请输入验证码')">
          <template #prefix-icon>
            <t-icon name="user" />
          </template>
        </t-input>
      </t-form-item>
      <img :src="codeUrl" alt="" style="width: 120px; height: 100%" @click="sendCode" />
    </div>

    <t-form-item class="btn-container">
      <t-button size="small" block type="submit" :loading="loginLoading">{{ $t('登录') }} </t-button>
    </t-form-item>
  </t-form>

  <!-- 平台登陆 -->
  <t-form v-else-if="type == 'wx'" :class="['item-container', `login-${type}`]" label-width="0" @submit="submitWxLogin">
    <t-form-item class="btn-container">
      <t-button size="small" block type="submit" :loading="loginLoading">
        <template #icon><LogoWechatStrokeIcon /></template>
        {{ $t('微信登录') }}
      </t-button>
    </t-form-item>
  </t-form>

  <!-- 微信扫码登陆 -->
  <template v-else-if="type == 'wxqr'">
    <div class="tip-container">
      <span class="tip">请使用微信扫一扫登录</span>
      <span class="refresh">刷新 <t-icon name="refresh" /> </span>
    </div>
  </template>

  <!-- 微信扫码登陆 -->
  <template v-else-if="type == 'wxcomqr'">
    <div class="tip-container">
      <span class="tip">请使用企业微信扫一扫登录</span>
      <span class="refresh">刷新 <t-icon name="refresh" /> </span>
    </div>
  </template>

  <div class="switch-container">
    <span v-if="type !== 'password'" class="tip" @click="switchType('password')">{{ t('账号密码登录') }}</span>
    <!-- <span v-if="type !== 'wxqr' && !userStore.canIUseWx" class="tip" @click="switchType('wxqr')">{{
      t('微信扫码登录')
    }}</span>
    <span v-if="type !== 'wxcomqr' && !userStore.canIUseWx" class="tip" @click="switchType('wxcomqr')">{{
      t('企微扫码登录')
    }}</span> -->
    <span v-if="type !== 'wx' && userStore.canIUseWx" class="tip" @click="switchType('wx')">{{
      t('微信一键登录')
    }}</span>
  </div>
</template>

<script setup lang="ts">
  import { LogoWechatStrokeIcon } from 'tdesign-icons-vue-next'
  import { useRoute, useRouter } from 'vue-router'
  import { MessagePlugin } from 'tdesign-vue-next'
  import type { FormInstanceFunctions, FormRule } from 'tdesign-vue-next'
  import { useUserStore } from '@/store'
  import { useUrlSearchParams } from '@vueuse/core'
  import { getRedirect, removeRedirect } from '@/utils/auth'

  const { t } = useI18n()
  const $t = t

  const userStore = useUserStore()
  const params = useUrlSearchParams('history')
  const wxLoginLoading = ref(!!params.code)

  const INITIAL_DATA = {
    username: '',
    password: '',
    code: '',
    uuid: '',
  }

  const FORM_RULES: Record<string, FormRule[]> = {
    username: [{ required: true, message: t('账号必填'), type: 'error' }],
    password: [{ required: true, message: t('密码必填'), type: 'error' }],
    code: [{ required: true, message: t('验证码必填'), type: 'error' }],
  }

  const type = ref(userStore.canIUseWx ? 'wx' : 'password')

  const form = ref<FormInstanceFunctions>()
  const formData = ref({ ...INITIAL_DATA })
  const showPsw = ref(false)

  const switchType = (val: string) => {
    type.value = val
  }

  const router = useRouter()
  const route = useRoute()
  const captchaEnabled = ref(false)
  const codeUrl = ref<string>('')
  const loginLoading = ref(false)
  /**
   * 发送验证码
   */
  const sendCode = async () => {
    const res = await userStore.capchat()
    const isCaptchaEnabled = res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled
    captchaEnabled.value = isCaptchaEnabled
    if (isCaptchaEnabled) {
      codeUrl.value = `data:image/gif;base64,${res.data.img}`
      formData.value.uuid = res.data.uuid
    }
  }

  const onSubmit = async ({ validateResult }) => {
    if (validateResult === true) {
      try {
        loginLoading.value = true
        await userStore.login(formData.value)
        MessagePlugin.success(t('登陆成功'))
        const redirect = route.query.redirect as string
        const redirectUrl = redirect ? decodeURIComponent(redirect) : '/'
        router.push(redirectUrl)
      } catch (e) {
        sendCode()
      } finally {
        loginLoading.value = false
      }
    }
  }

  onMounted(() => {
    sendCode()
    if (wxLoginLoading.value) {
      submitWxLogin()
    }
  })

  const submitWxLogin = async () => {
    const code = params.code
    if (code) {
      try {
        loginLoading.value = true
        await userStore.tryWxLogin(String(code))
        params.code = null as any
        MessagePlugin.success(t('登陆成功'))
        try {
          const redirectPath = getRedirect()
          if (redirectPath) {
            router.replace(redirectPath)
          } else {
            router.replace('/')
          }
        } finally {
          removeRedirect()
        }
      } finally {
        loginLoading.value = false
      }
    } else {
      userStore.wxRedirectLogin()
    }
  }
</script>

<style lang="less" scoped>
  @import url('../index.less');
</style>

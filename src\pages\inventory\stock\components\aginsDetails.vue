<script setup lang="ts">
  import useAginsDetails from './hooks'
  const { t } = useI18n()
  const $t = t
  const { value, tabList, projectList, columns, Loading, pagination, handleChange } = useAginsDetails()
</script>
<template>
  <div class="container">
    <t-tabs v-model="value" @change="handleChange">
      <t-tab-panel v-for="t in tabList" :key="t.value" :value="t.value" :label="t.label">
        <t-table
          style="margin-top: 20px"
          row-key="index"
          size="small"
          :data="projectList"
          :columns="columns"
          :loading="Loading"
          :pagination="pagination"
          lazy-load
        >
          <template #occupyDays="{ row }">
            <t-tag shape="round" color="#ff0000">{{ row.occupyDays }}{{ $t('天') }}</t-tag>
          </template>
        </t-table>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>
<style scoped lang="less"></style>

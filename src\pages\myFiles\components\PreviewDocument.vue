<script setup lang="ts">
  import type { Univer } from '@univerjs/presets'
  import { UniverSheetsCorePreset } from '@univerjs/preset-sheets-core'
  import sheetsCoreZhCN from '@univerjs/preset-sheets-core/locales/zh-CN'
  import sheetsCoreENUS from '@univerjs/preset-sheets-core/locales/en-US'
  import sheetsCoreRU from '@univerjs/preset-sheets-core/locales/ru-RU'
  import sheetsCoreVIVN from '@univerjs/preset-sheets-core/locales/vi-VN'
  import { createUniver, LocaleType, mergeLocales } from '@univerjs/presets'

  import { getSheetProperties, getSheetRangeData, getDocConfig } from '@/api/myFile'

  const props = defineProps<{
    docId: string
  }>()

  const { locale } = useI18n({ useScope: 'global' })
  const loading = ref<boolean>(true)
  const univerSheet = ref<HTMLElement | null>(null)
  const univerInstance = ref<Univer | null>(null)
  let univerAPIInstance: any = null
  const sheetProperties = ref<any>([])
  const docConfig = ref<any>({})

  const selectRange = ref<string>()
  const docId = ref<string>()
  const sheetId = ref<string>()

  /* 语言环境映射 */
  const localeMap: Record<string, LocaleType> = {
    zh: LocaleType.ZH_CN,
    en: LocaleType.EN_US,
    ru: LocaleType.RU_RU,
    vi: LocaleType.VI_VN,
  }

  const initUniver = async () => {
    loading.value = true
    const properties = await fetchSheetProperties()
    const docConfig = await fetchConfig()

    const sheets: Record<string, any> = {}
    const sheetOrder: string[] = []

    for (const { sheetId, title } of properties) {
      const finalRowCount = 500
      const finalColumnCount = 30

      sheetOrder.push(sheetId)
      sheets[sheetId] = {
        id: sheetId,
        name: docConfig[sheetId]?.name || title,
        rowCount: finalRowCount,
        columnCount: finalColumnCount,
        ...docConfig[sheetId],
      }
    }

    const { univer, univerAPI } = createUniver({
      locale: localeMap[locale.value] || LocaleType.ZH_CN,
      locales: {
        [LocaleType.ZH_CN]: mergeLocales(sheetsCoreZhCN),
        [LocaleType.EN_US]: mergeLocales(sheetsCoreENUS),
        [LocaleType.RU_RU]: mergeLocales(sheetsCoreRU),
        [LocaleType.VI_VN]: mergeLocales(sheetsCoreVIVN),
      },
      presets: [
        UniverSheetsCorePreset({
          container: univerSheet.value as HTMLElement,
          sheets: {
            protectedRangeShadow: false,
          },

          header: false,
        }),
      ],
    })

    univerInstance.value = univer
    univerAPIInstance = univerAPI

    // 只调用一次，传入所有表
    univerAPIInstance.createWorkbook({
      id: props.docId,
      name: 'My Workbook',
      sheetOrder,
      sheets,
    })

    // 逐个加载数据
    for (const { sheetId, rowCount, columnCount } of properties) {
      if (rowCount && columnCount) {
        await fetchSheetDataIfNeeded(props.docId, sheetId, rowCount, columnCount)
      }
    }
    // 设置保护：整表只读，只允许选区
    const workbook = univerAPIInstance.getActiveWorkbook()
    const permission = workbook?.getPermission()
    if (permission) {
      const unitId = workbook.getId()
      const subUnitId = workbook.getActiveSheet().getSheetId()
      const worksheetEditPermission = permission.permissionPointsDefinition.WorksheetEditPermission

      permission.addWorksheetBasePermission(unitId, subUnitId).then((permissionId) => {
        permission.sheetRuleChangedAfterAuth$.subscribe((currentPermissionId) => {
          if (currentPermissionId === permissionId) {
            permission.setWorksheetPermissionPoint(unitId, subUnitId, worksheetEditPermission, false)
          }
        })
      })
    }

    loading.value = false
  }

  /* 获取工作簿信息 */
  const fetchSheetProperties = async () => {
    try {
      const { data } = await getSheetProperties(props.docId)
      const properties = data?.properties || []
      sheetProperties.value = properties
      return properties
    } catch (error) {
      console.error('获取表格属性失败', error)
      sheetProperties.value = []
      return []
    }
  }

  /* 获取文档配置 */
  const fetchConfig = async () => {
    try {
      const { data } = await getDocConfig(props.docId)
      docConfig.value = data
      return data
    } catch (err) {
      console.log(`获取配置失败:${err}`)
    }
  }

  /* 将 0-255 RGB 转为 HEX  */
  const rgbToHex = (r: number, g: number, b: number) => {
    const toHex = (v: number) => {
      const hex = Math.round(v).toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
  }

  /* 列号转字母 */
  const numberToColumn = (n: number): string => {
    let col = ''
    while (n > 0) {
      const mod = (n - 1) % 26
      col = String.fromCharCode(65 + mod) + col
      n = Math.floor((n - 1) / 26)
    }
    return col
  }

  /* 根据 sheet 属性条件请求区域数据 */
  const fetchSheetDataIfNeeded = async (docId: string, sheetId: string, rowCount = 0, columnCount = 0) => {
    if (!rowCount || rowCount <= 0 || !columnCount || columnCount <= 0) return

    const MAX_ROWS = 1000
    const MAX_COLUMNS = 200
    const MAX_CELLS = 10000

    const fWorkbook = univerAPIInstance.getActiveWorkbook()
    const fWorksheet = fWorkbook.getSheetBySheetId(sheetId)
    if (!fWorksheet) return

    for (let rowStart = 1; rowStart <= rowCount; rowStart += MAX_ROWS) {
      // 计算剩余行数
      const rowsThisBatch = Math.min(MAX_ROWS, rowCount - rowStart + 1)

      for (let colStart = 1; colStart <= columnCount; colStart += MAX_COLUMNS) {
        // 计算剩余列数
        let colsThisBatch = Math.min(MAX_COLUMNS, columnCount - colStart + 1)

        // 保证单次 <= 10000 cells
        const maxColsByCells = Math.floor(MAX_CELLS / rowsThisBatch)
        if (colsThisBatch > maxColsByCells) {
          colsThisBatch = maxColsByCells
        }

        // 生成范围
        const range = `${numberToColumn(colStart)}${rowStart}:${numberToColumn(colStart + colsThisBatch - 1)}${
          rowStart + rowsThisBatch - 1
        }`

        try {
          loading.value = true
          const res = await getSheetRangeData(docId, sheetId, range)
          const sheetStyleConfig = docConfig.value?.[sheetId]?.style || {}
          const univerData = convertApiDataToUniverFormat(res.data.gridData, sheetStyleConfig)
          const fRange = fWorksheet.getRange(range)
          fRange.setValues(univerData.cellData)
          loading.value = false
        } catch (err) {
          loading.value = false
          console.error(`加载范围 ${range} 失败`, err)
        }
      }
    }
  }

  /* 将企业微信api数据转换为univer所需数据 */
  const convertApiDataToUniverFormat = (apiData: any, styleConfig: Record<number, Record<number, any>> = {}) => {
    const cellData: Record<number, Record<number, any>> = {}
    const startRow = apiData.startRow || 0
    const startColumn = apiData.startColumn || 0
    const rows = apiData.rows || []

    rows.forEach((row: any, rowIndex: number) => {
      const absoluteRow = startRow + rowIndex
      if (!row.values) return

      row.values.forEach((cell: any, columnIndex: number) => {
        const absoluteCol = startColumn + columnIndex
        const cellValue = cell?.cellValue?.text ?? null
        const textFormat = cell?.cellFormat?.textFormat ?? null

        if (cellValue !== null && cellValue !== undefined) {
          if (!cellData[absoluteRow]) {
            cellData[absoluteRow] = {}
          }

          const univerCell: any = { v: cellValue, s: {} }

          // 文本样式
          if (textFormat) {
            Object.assign(univerCell.s, {
              ff: textFormat.font || undefined,
              fs: textFormat.fontSize || undefined,
              bl: !!textFormat.bold,
              it: !!textFormat.italic,
              ul: textFormat.underline ? { s: 1 } : undefined,
              st: textFormat.strikethrough ? { s: 1 } : undefined,
              cl: textFormat.color
                ? { rgb: rgbToHex(textFormat.color.red, textFormat.color.green, textFormat.color.blue) }
                : undefined,
            })
          }

          // 合并 styleConfig 样式
          const cellStyle = styleConfig[absoluteRow]?.[absoluteCol]

          if (cellStyle) {
            if (cellStyle.ht !== undefined) univerCell.s.ht = cellStyle.ht
            if (cellStyle.vt !== undefined) univerCell.s.vt = cellStyle.vt
            if (cellStyle.bg?.rgb !== undefined) univerCell.s.bg = { rgb: cellStyle.bg.rgb }
            if (cellStyle.bd != undefined) univerCell.s.bd = cellStyle.bd
          }
          cellData[absoluteRow][absoluteCol] = univerCell
        }
      })
    })

    return { cellData }
  }

  /* 监听选区事件，获取选区的范围 */
  const bindSelectionChange = () => {
    const fWorkbook = univerAPIInstance.getActiveWorkbook()
    const fWorksheet = fWorkbook.getActiveSheet()

    univerAPIInstance.addEvent(univerAPIInstance.Event.SelectionChanged, () => {
      const fSelection = fWorksheet.getSelection()
      const { actualRow, actualColumn } = fSelection.getCurrentCell()
      const range = fWorksheet.getRange(actualRow, actualColumn).getA1Notation()
      const activeRange = fSelection.getActiveRange().getA1Notation()
      const isMerge = fWorksheet.getRange(activeRange).isMerged()
      const { startRow, endRow, startColumn, endColumn } = fSelection.getActiveRange().getRange()
      const rowCount = endRow - startRow + 1
      const colCount = endColumn - startColumn + 1

      if (!isMerge && (rowCount > 1 || colCount > 1)) {
        MessagePlugin.warning('只能选择单个单元格')
        return
      }
      selectRange.value = range
      docId.value = fWorkbook?.getId()
      sheetId.value = fWorksheet.getSheetId()
    })
  }

  /* 暴露方法传递给父组件 */
  const getSelectedRange = () => {
    return {
      range: selectRange.value,
      docId: docId.value,
      sheetId: sheetId.value,
    }
  }

  defineExpose({
    getSelectedRange,
  })

  watch(
    () => props.docId,
    async (newDocId) => {
      if (newDocId) {
        await initUniver()
      }
    },
  )

  onMounted(async () => {
    await initUniver()
    bindSelectionChange()
  })
</script>
<template>
  <t-loading size="small" :loading="loading" show-overlay>
    <div ref="univerSheet" style="width: 100%; height: 100%"></div>
  </t-loading>
</template>
<style scoped>
  .t-loading__parent {
    width: 100%;
    height: 100%;
  }
</style>

<script setup lang="ts">
  import { AddIcon } from 'tdesign-icons-vue-next'
  // 成本模板的CRUD，通过NS
  import { costTplListByProject, saveCostTemplate, removeCostTemplte } from '@/api/cost'
  import { useProjectStore } from '@/use/useProject'
  import { DialogPlugin } from 'tdesign-vue-next'
  import { useRequest } from 'vue-request'
  import { useI18n } from 'vue-i18n'

  const { t } = useI18n()

  const router = useRouter()

  const { activeProjectId, menuOptions } = storeToRefs(useProjectStore())
  /* 项目列表 */
  const projectList = computed(() => menuOptions.value)
  const templateColumns = [
    { colKey: 'name', title: t('成本模板名称') },
    { colKey: 'action', title: t('操作'), width: '180', ellipsis: true },
  ]

  const {
    data: templateList,
    refreshAsync: refreshTemplateList,
    loading,
  } = useRequest(() => costTplListByProject(activeProjectId.value).then((res) => res.data), {
    manual: false,
    refreshDeps: [activeProjectId],
  })

  // 成本模板配置编辑进入新的页面
  const toConfigPage = (e: any) => {
    if (e.row?.id) {
      router.push({
        name: 'CostConfig',
        params: {
          id: e.row.id,
        },
        state: {
          name: e.row.name,
        },
      })
    }
  }
  const deleteTemplate = (item: any) => {
    const dialog = DialogPlugin.confirm({
      theme: 'warning',
      header: t('提示'),
      body: t('确定删除成本模板吗？'),
      cancelBtn: t('取消'),
      confirmBtn: t('确定'),
      onConfirm: async () => {
        dialog.setConfirmLoading(true)
        await removeCostTemplte(item.id)
        dialog.setConfirmLoading(false)
        dialog.hide()
        refreshTemplateList()
      },
    })
  }

  const showAddTemplateDialig = ref(false)
  const newTemplateForm = ref<any>({
    id: null,
    templateName: '',
    projectId: activeProjectId.value,
  })
  const initTemplateForm = () => {
    newTemplateForm.value = {
      id: null,
      templateName: '',
      projectId: activeProjectId.value,
    }
  }

  const handleAddNewTemplate = () => {
    showAddTemplateDialig.value = true
    initTemplateForm()
  }
  const handleRenameTemplate = (item: any) => {
    showAddTemplateDialig.value = true
    newTemplateForm.value = {
      id: item.id,
      templateName: item.name,
      projectId: activeProjectId.value,
    }
  }

  const saveLoading = ref<boolean>(false)
  const submitAddNewTemplate = async () => {
    if (!newTemplateForm.value.templateName) {
      DialogPlugin.alert({
        theme: 'danger',
        body: t('请输入成本模板名称'),
      })
      return
    }
    saveLoading.value = true
    await saveCostTemplate(newTemplateForm.value)
    saveLoading.value = false
    showAddTemplateDialig.value = false
    refreshTemplateList()
    initTemplateForm()
  }
</script>

<template>
  <div class="container">
    <t-card>
      <t-form label-width="auto" layout="inline">
        <t-form-item :label="$t('项目名称')">
          <t-radio-group variant="primary-filled" v-model="activeProjectId">
            <t-radio-button v-for="p in projectList" :value="p.value">{{ p.text }}</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item>
          <t-button size="small" theme="primary" @click="handleAddNewTemplate">
            <template #icon><add-icon /></template>
            <span>{{ $t('新增成本模板') }}</span>
          </t-button>
        </t-form-item>
      </t-form>

      <t-table
        style="margin-top: 20px"
        row-key="index"
        size="small"
        maxHeight="500"
        :data="templateList"
        :columns="templateColumns"
        :loading="loading"
        @row-click="toConfigPage"
      >
        <template #action="{ row }">
          <t-space>
            <t-button size="small" theme="primary" variant="outline" @click.stop="handleRenameTemplate(row)">{{
              $t('重命名')
            }}</t-button>
            <t-button size="small" theme="danger" variant="outline" @click.stop="deleteTemplate(row)">{{
              $t('删除')
            }}</t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>
    <t-dialog
      v-model:visible="showAddTemplateDialig"
      :header="$t('新增成本模板')"
      confirm-on-enter
      :confirm-loading="saveLoading"
      @closed="initTemplateForm"
      @confirm="submitAddNewTemplate"
    >
      <t-form layout="inline" label-width="7em" label-align="left">
        <t-form-item class="form-item" :label="$t('项目')">
          <t-select v-model:value="newTemplateForm.projectId">
            <t-option v-for="p in projectList" :key="p.value" :label="p.text" :value="p.value" />
          </t-select>
        </t-form-item>
        <t-form-item class="form-item" :label="$t('成本模板名称')">
          <t-input v-model="newTemplateForm.templateName" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<style scoped lang="less">
  .form-item {
    width: 100%;
  }
</style>

import { taskTodo, taskDone } from '@/api/myTask'
import { useUserStore } from '@/store'
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'

export default () => {
  const { t } = useI18n()
  const $t = t

  const userStore = useUserStore()

  const value = ref<number>(0)
  /* 任务列表 */
  const taskList = ref<any[]>([])
  /* tab列表 */
  const tabList = computed(() => [
    { title: $t('未完成'), value: 0 },
    { title: $t('已完成'), value: 1 },
  ])
  const columns = computed(() => {
    return value.value === 0 ? todoColumns.value : doneColumns.value
  })
  /* 表格配置 */
  const todoColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'taskname', title: $t('任务名称'), width: '100', ellipsis: true },
    { colKey: 'taskRemark', title: $t('任务描述'), width: '160', ellipsis: true },
    { colKey: 'planEndDate', title: $t('截止时间'), width: '100' },
    { colKey: 'realityEndDate', title: $t('实际完成时间'), width: '120', ellipsis: true },
    { colKey: 'projectName', title: $t('项目'), width: '100', ellipsis: true },
  ])

  const doneColumns: ComputedRef<PrimaryTableCol<TableRowData>[]> = computed(() => [
    { colKey: 'taskname', title: $t('任务名称'), width: '100', ellipsis: true },
    { colKey: 'taskRemark', title: $t('任务描述'), width: '160', ellipsis: true },
    { colKey: 'realityEndDate', title: $t('实际完成时间'), width: '120', ellipsis: true },
    { colKey: 'projectName', title: $t('项目'), width: '100', ellipsis: true },
  ])

  /* 表格分页 */
  const pagination = ref({
    defaultCurrent: 1,
    defaultPageSize: 10,
    total: 0,
  })

  const loading = ref<boolean>(false)

  const params = reactive({
    id: userStore.cpInfo?.userId as string,
    pageNum: 1,
    pageSize: 9999,
  })

  const getTodoList = async () => {
    loading.value = true
    try {
      const res = await taskTodo(params)
      taskList.value = res.data.list
      pagination.value.total = taskList.value.length
      taskList.value.forEach((item) => {
        item.isBar = false
      })
    } catch (err) {
    } finally {
      loading.value = false
    }
  }
  const getAll = async () => {
    loading.value = true
    try {
      const res = await taskDone(params)
      taskList.value = res.data.list
      pagination.value.total = taskList.value.length
      taskList.value.forEach((item) => {
        item.isBar = true
      })
    } catch (err) {
      MessagePlugin.error(err.msg)
    } finally {
      loading.value = false
    }
  }

  const tabRequest: any[] = [getTodoList, getAll]

  watch(
    value,
    (val) => {
      tabRequest[val]()
    },
    { immediate: true },
  )

  onMounted(() => {
    tabRequest[value.value]
  })

  return {
    value,
    tabList,
    taskList,
    columns,
    pagination,
    loading,
  }
}

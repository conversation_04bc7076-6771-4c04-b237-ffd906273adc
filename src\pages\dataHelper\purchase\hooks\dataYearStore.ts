import { computed, ref } from 'vue'
import dayjs from 'dayjs'
import { useSessionStorage } from '@vueuse/core'

const curYear = parseInt(dayjs().format('YYYY'))

interface YearType {
  text: string
  value: string
}

const YearList: YearType[] = []
for (let index = curYear - 2; index <= curYear; index++) {
  YearList.unshift({
    text: index.toString(),
    value: index.toString(),
  })
}

export const useYearDataStore = defineStore('dataHelper/year', () => {
  const slectVal = useSessionStorage<string>('scm-data-helper-year', String(YearList[0].value))
  const yearStart = computed(() => dayjs(slectVal.value).startOf('year').format('YYYY-MM-DD'))
  const yearEnd = computed(() => {
    let endYearDate = dayjs(slectVal.value).endOf('year')
    if (endYearDate.isAfter(dayjs())) endYearDate = dayjs()
    return endYearDate.format('YYYY-MM-DD')
  })

  return {
    slectVal,
    yearStart,
    yearEnd,
  }
})

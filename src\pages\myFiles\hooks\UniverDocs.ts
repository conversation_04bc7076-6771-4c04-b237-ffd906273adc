import { getDocContent, saveDocContent } from '@/api/myFile'
import type { FUniver, Univer } from '@univerjs/presets'
import { UniverDocsCorePreset } from '@univerjs/preset-docs-core'
import docsCoreZhCN from '@univerjs/preset-docs-core/locales/zh-CN'
import { createUniver, LocaleType, mergeLocales } from '@univerjs/presets'
import docsCoreENUS from '@univerjs/preset-docs-core/locales/en-US'
import docsCoreRU from '@univerjs/preset-docs-core/locales/ru-RU'
import docsCoreVIVN from '@univerjs/preset-docs-core/locales/vi-VN'
import { useMagicKeys, useEventListener } from '@vueuse/core'

export default () => {
  const { locale } = useI18n({ useScope: 'global' })
  const keys = useMagicKeys()
  const loading = ref<boolean>(false)
  const univerDocsRef = ref<HTMLElement | null>(null)
  let univerInstance: Univer | null = null
  let univerAPIInstance: FUniver | null = null
  const route = useRoute()
  const docId = route.query.docId as string

  /* 语言环境映射 */
  const localeMap: Record<string, LocaleType> = {
    zh: LocaleType.ZH_CN,
    en: LocaleType.EN_US,
    ru: LocaleType.RU_RU,
    vi: LocaleType.VI_VN,
  }

  const initUniver = async () => {
    if (univerAPIInstance) return univerAPIInstance
    if (!univerDocsRef.value) return

    await fetchDocContent()

    const { univer, univerAPI } = createUniver({
      locale: localeMap[locale.value] || LocaleType.ZH_CN,
      locales: {
        [LocaleType.ZH_CN]: mergeLocales(docsCoreZhCN),
        [LocaleType.EN_US]: mergeLocales(docsCoreENUS),
        [LocaleType.RU_RU]: mergeLocales(docsCoreRU),
        [LocaleType.VI_VN]: mergeLocales(docsCoreVIVN),
      },
      presets: [
        UniverDocsCorePreset({
          container: univerDocsRef.value as HTMLElement,
        }),
      ],
    })
    univerAPI.createUniverDoc({})
    univerInstance = univer
    univerAPIInstance = univerAPI
  }

  /* 获取文档信息 */
  const fetchDocContent = async () => {
    if (!docId) return
    try {
      const { data } = await getDocContent(docId)
    } catch (error) {
      console.error('获取文档内容失败', error)
    }
  }

  /* 保存文档 */
  const save = async () => {
    if (!univerAPIInstance) return
    try {
      const fDoc = univerAPIInstance.getActiveDocument()
      const saveData = fDoc.getSnapshot()
      const requestPayload = convertSaveDataToWeDocRequest(saveData)
      await saveDocContent(requestPayload)

      console.log(requestPayload)
    } catch (error) {
      console.error('保存文档失败', error)
    }
  }

  /* 映射 Univer 属性到企业微信接口 */
  const mapTsToTextProperty = (ts: any) => {
    const prop: any = {}
    if (ts.bl === 1) prop.bold = true
    if (ts.cl && ts.cl.rgb) prop.color = ts.cl.rgb
    if (ts.bg && ts.bg.rgb) prop.backgroundColor = ts.bg.rgb
    return prop
  }

  /* 判断两个 TextProperty 是否相同 */
  const isSameTextProperty = (a: any, b: any) => {
    return JSON.stringify(a) === JSON.stringify(b)
  }

  /* 根据 textRuns 生成批量updateTextProperty 请求，每个请求最多10个范围 */
  const generateUpdateTextPropertyRequests = (textRuns: any[]) => {
    const requests: { textProperty: any; ranges: { startIndex: number; length: number }[] }[] = []
    const mergedRuns: { prop: any; start: number; end: number }[] = []

    for (const run of textRuns) {
      const prop = mapTsToTextProperty(run.ts)
      if (mergedRuns.length === 0) {
        mergedRuns.push({ prop, start: run.st, end: run.ed })
      } else {
        const last = mergedRuns[mergedRuns.length - 1]
        if (isSameTextProperty(last.prop, prop) && run.st === last.end) {
          last.end = run.ed
        } else {
          mergedRuns.push({ prop, start: run.st, end: run.ed })
        }
      }
    }

    const groups = new Map<string, { startIndex: number; length: number }[]>()
    for (const run of mergedRuns) {
      const key = JSON.stringify(run.prop)
      if (!groups.has(key)) groups.set(key, [])
      groups.get(key)!.push({ startIndex: run.start, length: run.end - run.start })
    }

    for (const [key, ranges] of groups.entries()) {
      const textProperty = JSON.parse(key)
      for (let i = 0; i < ranges.length; i += 10) {
        const slice = ranges.slice(i, i + 10)
        requests.push({
          textProperty,
          ranges: slice,
        })
      }
    }

    return requests
  }

  /* 主转换函数：把 saveData 转换为企业微信批量更新请求体 */
  const convertSaveDataToWeDocRequest = (saveData: any) => {
    const text = saveData.body.dataStream || ''
    const length = text.length
    const requests: any[] = []

    if (length > 0) {
      // 删除全文
      requests.push({
        deleteContent: {
          range: { startIndex: 0, length },
        },
      })
      // 插入文本
      requests.push({
        insertText: {
          text,
          location: { index: 0 },
        },
      })

      // 生成属性更新请求
      const updateTextProps = generateUpdateTextPropertyRequests(saveData.body.textRuns)

      const maxRequests = 30 - requests.length

      for (let i = 0; i < updateTextProps.length && i < maxRequests; i++) {
        const req = updateTextProps[i]
        requests.push({
          updateTextProperty: {
            textProperty: req.textProperty,
            ranges: req.ranges,
          },
        })
      }
    }

    return {
      docId,
      requests,
    }
  }

  /* 监听语言变化 */
  watch(
    () => locale.value,
    async (newLocale) => {
      if (univerInstance) {
        univerInstance.setLocale(localeMap[newLocale] || LocaleType.ZH_CN)
      }
    },
  )

  watch(
    () => keys['ctrl+s'].value,
    async (pressed) => {
      if (pressed) {
        await save()
        MessagePlugin.success('保存成功')
      }
    },
    { immediate: false },
  )

  useEventListener(window, 'keydown', (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 's') {
      e.preventDefault()
    }
  })

  onMounted(() => {
    initUniver()
  })

  onBeforeUnmount(() => {
    univerInstance?.dispose()
    univerAPIInstance?.dispose()
    univerInstance = null
    univerAPIInstance = null
  })

  return {
    loading,
    univerDocsRef,
  }
}

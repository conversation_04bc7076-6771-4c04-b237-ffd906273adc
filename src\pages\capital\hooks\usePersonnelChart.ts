import * as echarts from 'echarts'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { sortBy } from 'lodash-es'

export default (props: any) => {
  const { t, locale } = useI18n()
  const $t = t
  const router = useRouter()
  const title = ref<string>(props.title)

  const currencyTypes = ref<any>([])
  const curType = ref('')
  const chartRefs = ref<any>([])
  let chart
  // 按 currencyType 分组并累加 occupyAmount
  const groupByCurrencyType = (arr: any) => {
    const map = new Map()
    arr.forEach((item: any) => {
      const currencyType = item.currencyType
      const occupyAmount = Number(item.occupyAmount)
      const entityName = item.entityName
      const entityId = item.entityId
      if (map.has(currencyType)) {
        map.get(currencyType)!.push({ value: occupyAmount, entityId, entityName })
      } else {
        map.set(currencyType, [{ value: occupyAmount, entityId, entityName }])
      }
    })
    return map
  }
  // 设置图表容器的 ref
  const setChartRef = (el: any) => {
    if (el) {
      chartRefs.value.push(el)
    }
  }

  // 格式化 X 轴数值
  const formatXAxisValue = (value: number) => {
    if (value >= 100000000) {
      return `${(value / 100000000).toFixed(1)}${$t('亿')}` // 大于 1 亿时显示为 "X亿"
    }
    return value.toString() // 否则正常显示
  }
  /* 初始化echarts */
  const initCharts = () => {
    const totalMap = groupByCurrencyType(props.totalMoney) // 全部金额
    const pendingMap = groupByCurrencyType(props.imminentMoney) // 临期金额
    const overdueMap = groupByCurrencyType(props.overdueMoney) // 超期金额

    // 获取所有 currencyType
    currencyTypes.value = Array.from(new Set([...totalMap.keys(), ...pendingMap.keys(), ...overdueMap.keys()]))

    currencyTypes.value.forEach((currencyType: any, idx: any) => {
      curType.value = currencyType
      const chartDom: any = chartRefs.value[idx]
      if (chartDom) {
        // 检查是否已经存在 ECharts 实例
        const existingChart = echarts.getInstanceByDom(chartDom)
        if (existingChart) existingChart.dispose() // 销毁旧的 ECharts 实例

        chart = echarts.init(chartDom)
        const totalData = sortBy(totalMap.get(currencyType) || [], 'value')
        const pendingData = pendingMap.get(currencyType) || []
        const overdueData = overdueMap.get(currencyType) || []
        // 合并数据
        const entityNames = Array.from(
          new Set([
            ...totalData.map((item: any) => item.entityName),
            ...pendingData.map((item: any) => item.entityName),
            ...overdueData.map((item: any) => item.entityName),
          ]),
        ).filter((entityName) => {
          const total = totalData.find((item: any) => item.entityName === entityName)?.value || 0
          const pending = pendingData.find((item: any) => item.entityName === entityName)?.value || 0
          const overdue = overdueData.find((item: any) => item.entityName === entityName)?.value || 0
          return total !== 0 || pending !== 0 || overdue !== 0 // 过滤掉全部为 0 的 entityName
        })
        // 构造 series 数据
        const seriesData = [
          {
            name: props.legend[0],
            type: 'bar',
            label: {
              show: true,
              position: 'outside',
              fontSize: 12,
              formatter: function ({ value }: any) {
                return new Intl.NumberFormat(locale.value, {
                  style: 'decimal',
                  notation: 'compact',
                  compactDisplay: 'long',
                  minimumFractionDigits: 3,
                  roundingMode: 'halfFloor',
                }).format(Number(value))
              },
            },
            labelLayout: {
              align: 'left',
              verticalAlign: 'middle',
            },
            data: entityNames.map((entityName) => {
              const item = totalData.find((item: any) => item.entityName === entityName)
              return {
                value: item?.value,
                entityId: item?.entityId,
                ...props.params,
                dataLevel: null,
              }
            }),
          },
          {
            name: props.legend[1],
            type: 'bar',
            label: {
              show: true,
              position: 'outside',
              fontSize: 12,
              formatter: function ({ value }: any) {
                return new Intl.NumberFormat(locale.value, {
                  style: 'decimal',
                  notation: 'compact',
                  compactDisplay: 'long',
                  minimumFractionDigits: 3,
                  roundingMode: 'halfFloor',
                }).format(Number(value))
              },
            },
            labelLayout: {
              align: 'left',
              verticalAlign: 'middle',
            },
            data: entityNames.map((entityName) => {
              const item = pendingData.find((item: any) => item.entityName === entityName)
              return {
                value: item?.value,
                entityId: item?.entityId,
                ...props.params,
                dataLevel: 1,
              }
            }),
          },
          {
            name: props.legend[2],
            type: 'bar',
            labelLayout: {
              align: 'left',
              verticalAlign: 'middle',
            },
            label: {
              show: true,
              position: 'outside',
              fontSize: 12,
              formatter: function ({ value }: any) {
                return new Intl.NumberFormat(locale.value, {
                  style: 'decimal',
                  notation: 'compact',
                  compactDisplay: 'long',
                  minimumFractionDigits: 3,
                  roundingMode: 'halfFloor',
                }).format(Number(value))
              },
            },
            data: entityNames.map((entityName) => {
              const item = overdueData.find((item: any) => item.entityName === entityName)
              return {
                value: item?.value,
                entityId: item?.entityId,
                ...props.params,
                dataLevel: 2,
              }
            }),
          },
        ]
        const option = {
          color: ['#4874cb', '#ee822f', '#f2ba02'],
          title: {
            text: `${props.title}${$t('款项')} (${currencyType})`,
            left: 0,
            top: 0,
            textStyle: {
              fontSize: 12,
            },
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          legend: {
            data: props.legend,
            formatter: function (value: string) {
              const match = value.match(/[\u4e00-\u9fa5]+/g)
              return match ? match.join('') : value.length > 13 ? value.slice(0, 10) + '...' : value
            },
            textStyle: {
              fontSize: 12,
            },
          },
          grid: {
            left: '100',
            bottom: 0,
          },

          xAxis: {
            type: 'value',
            axisLabel: {
              rotate: 45,
              formatter: (value: number) => formatXAxisValue(value),
            },

            axisTick: {
              show: false,
            },
          },
          yAxis: {
            type: 'category',
            data: entityNames,
            axisLabel: {
              interval: 0,
              // rotate: 50,
              width: '92',
              overflow: 'break',
              formatter: function (value: string) {
                const match = value.match(/[\u4e00-\u9fa5]+/g)
                return match ? match.join('') : value
              },
              fontSize: 12,
            },
            axisTick: {
              show: false,
            },
          },
          series: seriesData,
        }
        chart.setOption(option)
        chart.resize()
        chart.on('click', (params: any) => {
          router.push({ path: '/capital/paymentTerm', state: { info: JSON.stringify(params.data) } })
        })
      }
    })
  }
  watch(
    () => [props.totalMoney, props.imminentMoney, props.overdueMoney],
    () => {
      initCharts()
    },
    {
      deep: true,
    },
  )

  watch(
    () => props,
    () => {
      if (chart) {
        chart.setOption({
          title: {
            text: `${props.title}${$t('款项')} (${curType.value})`,
          },
          legend: {
            data: props.legend,
          },
          series: [{ name: props.legend[0] }, { name: props.legend[1] }, { name: props.legend[2] }],
        })
      }

      title.value = props.title
    },
    {
      deep: true,
    },
  )

  onMounted(() => {
    initCharts()
  })

  return {
    setChartRef,
    currencyTypes,
    title,
  }
}

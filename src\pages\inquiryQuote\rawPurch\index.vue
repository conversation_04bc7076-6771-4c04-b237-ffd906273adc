<script setup lang="ts">
  import useRawPurch from './hooks/index'
  import Dialog from '@/components/dialog/index.vue'
  const { t } = useI18n()
  const $t = t

  const {
    value,
    selected,
    options,
    inquiryData,
    inquiryColumns,
    nsListDict,
    searchInquiry,
    activeList,
    activeBtn,
    visible,
    openInquiry,
    formData,
    region,
    productName,
    companyList,
    optionsData,
    formRules,
    submitInquiry,
    loading,
    select,
    quotationForm,
    searchQuotation,
    quotationData,
    quotationColumns,
    quotationPagination,
    inquiryPagination,
    tableLoading,
    resetInquiryForm,
    resetForm,
  } = useRawPurch()
</script>
<template>
  <div class="container">
    <t-tabs v-model="value">
      <t-tab-panel :value="1" :label="$t('询价记录')" :destroy-on-hide="false">
        <t-card :bordered="false">
          <t-form label-width="auto" layout="inline" @submit="searchInquiry" @reset="resetInquiryForm">
            <t-form-item :label="$t('产品名称')" name="name">
              <t-select size="small" v-model="selected" :placeholder="$t('请选择产品')" filterable>
                <t-option-group v-for="group in options" :key="group.label" :label="group.label">
                  <t-option v-for="item in group.options" :key="item.value" :label="item.itemName" :value="item.itemId">
                  </t-option>
                </t-option-group>
              </t-select>
            </t-form-item>
            <t-form-item>
              <t-button size="small" theme="primary" type="submit">{{ $t('查询') }}</t-button>
              <t-button size="small" theme="default" variant="base" type="reset">{{ $t('重置') }}</t-button>
            </t-form-item>
          </t-form>
          <t-table
            style="margin-top: 20px"
            max-height="500px"
            row-key="index"
            size="small"
            :loading="tableLoading"
            :data="inquiryData"
            :columns="inquiryColumns"
            :pagination="inquiryPagination"
          >
            <template #custrecord_it_enquiry_country="{ row }">
              <span>{{ row.custrecord_it_enquiry_country === 5 ? $t('越南') : $t('中亚') }}</span>
            </template>
            <template #custrecord_it_enquiry_status="{ row }">
              <span>{{ nsListDict.CUSTOMLIST_IT_SRM_APPROVE_STATUS?.[row.custrecord_it_enquiry_status || 1] }}</span>
            </template>
          </t-table>
        </t-card>
      </t-tab-panel>
      <t-tab-panel :value="2" :label="$t('主动询价')" :destroy-on-hide="false">
        <t-space class="space">
          <div>{{ $t('询价区域') }}：</div>
          <t-button
            v-for="b in activeList"
            :key="b.id"
            size="small"
            :variant="activeBtn == b.id ? 'base' : 'outline'"
            @click="activeBtn = b.id"
            >{{ b.text }}</t-button
          >
        </t-space>
        <t-list>
          <t-list-item v-for="it in options[activeBtn].options" :key="it.itemId">
            {{ it.itemName }}
            <template #action>
              <t-button size="small" theme="primary" variant="outline" @click="openInquiry(it)">{{
                $t('询价')
              }}</t-button>
            </template>
          </t-list-item>
        </t-list>
      </t-tab-panel>
      <t-tab-panel :value="3" :label="$t('报价记录')" :destroy-on-hide="false">
        <t-card :bordered="false">
          <t-form label-width="auto" layout="inline" @submit="searchQuotation" @reset="resetForm">
            <t-form-item>
              <t-select size="small" v-model="quotationForm.status" :options="select" :placeholder="$t('请选择状态')" />
            </t-form-item>
            <t-form-item>
              <t-select size="small" v-model="quotationForm.item" :placeholder="$t('请选择产品')" filterable>
                <t-option-group v-for="group in options" :key="group.label" :label="group.label">
                  <t-option v-for="item in group.options" :key="item.value" :label="item.itemName" :value="item.itemId">
                  </t-option>
                </t-option-group>
              </t-select>
            </t-form-item>
            <t-form-item>
              <t-button size="small" theme="primary" type="submit">{{ $t('查询') }}</t-button>
              <t-button size="small" theme="default" variant="base" type="reset">{{ $t('重置') }}</t-button>
            </t-form-item>
          </t-form>
          <t-table
            style="margin-top: 20px"
            row-key="index"
            size="small"
            max-height="500px"
            :data="quotationData"
            :columns="quotationColumns"
            :pagination="quotationPagination"
            table-layout="fixed"
            :loading="tableLoading"
          >
            <template #custrecord_it_quote_modifyprice="{ row }">
              <span>{{
                row.custrecord_it_quote_modifyprice
                  ? row.custrecord_it_quote_modifyprice
                  : row.custrecord_it_quote_price
              }}</span>
            </template>
            <template #custrecord_it_quote_modifysupply="{ row }">
              <span>{{
                row.custrecord_it_quote_modifysupply
                  ? row.custrecord_it_quote_modifysupply
                  : row.custrecord_it_quote_supply
              }}</span>
            </template>
            <template #vendor_name="{ row }">
              <span>{{ row.vendor_name ? row.vendor_name : row.customer_name }}</span>
            </template>
          </t-table>
        </t-card>
      </t-tab-panel>
    </t-tabs>
    <!-- 主动询价弹窗 -->
    <Dialog v-model="visible" width="660px" top="6%" :footer="false">
      <template #header>{{ $t('主动询价详情') }}</template>
      <template #body>
        <h4 style="margin-bottom: 20px">{{ productName }}</h4>
        <t-form label-width="auto" :data="formData" :rules="formRules" @submit="submitInquiry">
          <t-space break-line>
            <t-form-item :label="$t('标书名称')" name="altname">
              <t-input size="small" :placeholder="$t('请输入标书名称')" v-model="formData.altname" />
            </t-form-item>
            <t-form-item :label="$t('招标公司')" name="custrecord_it_enquiry_com">
              <t-select
                size="small"
                :placeholder="$t('请选择招标公司')"
                clearable
                :keys="{ label: 'subsidiaryName', value: 'subsidiaryId' }"
                v-model="formData.custrecord_it_enquiry_com"
                :options="companyList"
              />
            </t-form-item>
            <t-form-item :label="$t('货品')" name="productName">
              <t-input size="small" :placeholder="$t('请输入货品名称')" readonly v-model="productName" />
            </t-form-item>
            <t-form-item :label="$t('报价截止时间')" name="custrecord_it_enquiry_endtime">
              <t-date-picker
                size="small"
                :placeholder="$t('请选择报价截止时间')"
                enable-time-picker
                allow-input
                clearable
                v-model="formData.custrecord_it_enquiry_endtime"
              />
            </t-form-item>
            <t-form-item :label="$t('议价截止时间')" name="custrecord_it_bargaining_endtime">
              <t-date-picker
                size="small"
                :placeholder="$t('请选择议价截止时间')"
                enable-time-picker
                allow-input
                clearable
                v-model="formData.custrecord_it_bargaining_endtime"
              />
            </t-form-item>
            <t-form-item :label="$t('最晚提货日期')" name="custrecord_it_delivery_enddate">
              <t-date-picker
                size="small"
                :placeholder="$t('请选择最晚提货日期')"
                clearable
                allow-input
                v-model="formData.custrecord_it_delivery_enddate"
              />
            </t-form-item>
            <t-form-item :label="$t('单价')" name="custrecord_it_enquiry_price">
              <t-input
                size="small"
                :placeholder="$t('请输入单价')"
                type="number"
                v-model="formData.custrecord_it_enquiry_price"
              />
            </t-form-item>
            <t-form-item :label="$t('可供数量')" name="custrecord_it_enquiry_qty">
              <t-input
                size="small"
                :placeholder="$t('请输入可供数量')"
                type="number"
                v-model="formData.custrecord_it_enquiry_qty"
              />
            </t-form-item>
            <t-form-item :label="$t('交货地点')" name="custrecord_it_enquiry_addr">
              <t-select
                size="small"
                :placeholder="$t('请选择交货地点')"
                clearable
                :keys="{ label: 'listLabel', value: 'listValue' }"
                v-model="formData.custrecord_it_enquiry_addr"
                :options="optionsData.location"
              />
            </t-form-item>
            <t-form-item :label="$t('质量标准')" name="custrecord_it_enquiry_spec">
              <t-input size="small" :placeholder="$t('请输入质量标准')" v-model="formData.custrecord_it_enquiry_spec" />
            </t-form-item>
            <t-form-item :label="$t('包装方式')" name="custrecord_it_enquiry_pack">
              <t-select
                size="small"
                :placeholder="$t('请选择包装方式')"
                clearable
                :keys="{ label: 'listLabel', value: 'listValue' }"
                v-model="formData.custrecord_it_enquiry_pack"
                :options="optionsData.way"
              />
            </t-form-item>
            <t-form-item :label="$t('付款条件')" name="custrecord_it_enquiry_payment">
              <t-select
                size="small"
                :placeholder="$t('请选择付款条件')"
                clearable
                :keys="{ label: 'listLabel', value: 'listValue' }"
                v-model="formData.custrecord_it_enquiry_payment"
                :options="optionsData.condition"
              />
            </t-form-item>
            <t-form-item :label="$t('招标区域')" name="region">
              <t-input size="small" placeholder="请输入招标区域" readonly v-model="region" />
            </t-form-item>
          </t-space>
          <t-form-item>
            <div style="margin-top: 20px; flex: 1; text-align: right">
              <t-button size="small" theme="default" type="reset" @click="visible = false">{{ $t('取消') }}</t-button>
              <t-button size="small" type="submit" :loading="loading">{{ $t('提交') }}</t-button>
            </div>
          </t-form-item>
        </t-form>
      </template>
    </Dialog>
  </div>
</template>
<style scoped lang="less">
  .space {
    margin: 16px;
  }
</style>

<script setup lang="ts">
  import usePaymentTerm from '../hooks/paymentTerm'
  const { paymentList, tabColumns, formatCurrency, toContractDetail, pagination, showLoading } = usePaymentTerm()
  import { useI18n } from 'vue-i18n'
  const { t } = useI18n()
  const $t = t
</script>
<template>
  <div class="container">
    <t-card>
      <t-table
        row-key="index"
        size="small"
        maxHeight="500"
        :data="paymentList"
        :columns="tabColumns"
        :pagination="pagination"
        :loading="showLoading"
      >
        <template #occupyDays="{ row }">
          <t-tag theme="danger" size="small" color="#FF0000">{{ Math.abs(row.occupyDays) }}{{ $t('天') }}</t-tag>
        </template>
        <template #currencyId="{ row }">
          <t-tag theme="danger" size="small" color="#FF0000">{{
            formatCurrency(row.occupyAmount, row.currencyId)
          }}</t-tag>
        </template>
        <template #link="{ row }">
          <t-link theme="primary" @click="toContractDetail(row)">{{ $t('详情') }}</t-link>
        </template>
      </t-table>
    </t-card>
  </div>
</template>
<style scoped></style>
